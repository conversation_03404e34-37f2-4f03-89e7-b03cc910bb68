import { LoginUserInfo } from './../models/LoginUserInfo';
import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";
import { User } from '../models/User';
import { Organization } from '../models/Organization';

// Define the UserInfo interface for state
interface UserInfo {
  accessToken: string;
  oidcInfo: any;
  user: User | null;
  orgDetails: Organization;
  userType: string; // added userType here
  orgLanguages: string[]; // <-- add this
  userRoles: any;
  setAccessToken: (token: string) => void;
  clearAccessToken: () => void;
  setOidcInfo: (info: any) => void;
  setUser: (user: User) => void;
  setOrgDetails: (orgDetails: Organization) => void;
  setUserType: (userType: string) => void;
  setOrgLanguages: (languages: string[]) => void; // <-- add this
  clearAll: () => void; 
  setUserRoles: (userRoles: any) => void;
}

// Zustand store with typed state
const useInfoStore = create<UserInfo>()(
  devtools(
    persist(
      immer((set) => ({
        accessToken: "",
        oidcInfo: {},
        user: {} as User,
        orgDetails: {} as Organization,
        userType: "",
        orgLanguages: [], // <-- add this
        userRoles: {} as any,

        setAccessToken: (token: string) => {
          set((state) => {
            state.accessToken = token;
          });
        },

        clearAccessToken: () => {
          set((state) => {
            state.accessToken = "";
          });
        },
            
        setOidcInfo: (info: any) => {
          set((state) => {
            state.oidcInfo = info;
          });
        },

        setUser: (user: User | null) => {
          set((state) => {
            state.user = user;
          });
        },

        setOrgDetails: (orgDetails: Organization) => {
          set((state) => {
            state.orgDetails = orgDetails;
          });
        },
        clearAll: () => {  // Reset all fields to initial values
            set((state) => {
              state.accessToken = "";
              state.oidcInfo = {};
              state.user = null;
              state.orgDetails = {} as Organization;
              state.userType = "";
              state.orgLanguages = []; // <-- add this
              state.userRoles = {};
            });
          },
        setUserType: (userType: string) => {
          set((state) => {
            state.userType = userType;
          });
        },
        setOrgLanguages: (languages: string[]) => {
          set((state) => {
            state.orgLanguages = languages;
          });
        },
        setUserRoles: (userRoles: any) => {
          set((state) => {
            state.userRoles = userRoles;
          })
        }
      })),
      {
        name: "user-info-storage", // unique name for localStorage
        partialize: (state) => ({
          accessToken: state.accessToken,
          oidcInfo: state.oidcInfo,
          user: state.user,
          orgDetails: state.orgDetails,
          userType: state.userType,
          orgLanguages: state.orgLanguages, 
          userRoles: state.userRoles,
        }), // Persist these fields
      }
    )
  )
);

export default useInfoStore;
