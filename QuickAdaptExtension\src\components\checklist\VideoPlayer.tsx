import React from "react";
import { useTranslation } from "react-i18next";

const VideoPlayer = ({ videoFile ,isMaximized}: { videoFile: string,isMaximized :any }) => {
  const { t: translate } = useTranslation();
  if (!videoFile) return null;

  return (
    <div>
      <video
        width="100%"
        height="100%"
        controls
        style={{ borderRadius: "8px", objectFit: isMaximized?"contain":"cover" }}
      >
        <source src={videoFile} type="video/mp4" />
        {translate("Your browser does not support the video tag.", { defaultValue: "Your browser does not support the video tag." })}
      </video>
    </div>
  );
};

export default VideoPlayer;
