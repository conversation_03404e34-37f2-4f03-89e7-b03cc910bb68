import React, { useState, useEffect } from 'react';
import axios from 'axios';
import CloseIcon from '@mui/icons-material/Close';
import { upload, ai } from '../../assets/icons/icons';
import { isScrapingActive, startScraping } from '../../services/ScrapingService';
import './AIOptionsPopup.css';
import { uploadSRSFile } from '../../services/FileService';
import { AccountContext } from '../../components/login/AccountContext';
import { useContext } from 'react';
import { useTranslation } from 'react-i18next';

// Define message type for TypeScript
interface ChromeMessage {
  action: string;
  isActive?: boolean;
  [key: string]: any;
}

interface AIOptionsPopupProps {
  onClose: () => void;
  onFileUpload: (file: File) => void;
  onStartScraping?: () => void;
}
declare global {
	interface Window {
		chrome:typeof chrome
	}
}

const AIOptionsPopup: React.FC<AIOptionsPopupProps> = ({ onClose, onFileUpload, onStartScraping }) => {
  const { t: translate } = useTranslation();
  // Track selected files
  const { accountId } = useContext(AccountContext);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [showScrapingButton, setShowScrapingButton] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      const fileArray = Array.from(files);
      setSelectedFiles(prevFiles => [...prevFiles, ...fileArray]);
    }
  };


  const handleUploadClick = async () => {
    if (selectedFiles.length > 0) {
      try {
        setIsLoading(true);
  
        const formData = new FormData();
        selectedFiles.forEach((file) => {
          formData.append('aifiles', file);
        });
        const response = await uploadSRSFile(accountId,formData);
        console.log('File upload successful:', response.data);
  
        onFileUpload(selectedFiles[0]);
  

        setShowScrapingButton(true);
      } catch (error) {
        console.error('Error uploading files:', error);
        alert('Error uploading files. Please try again.');
      } finally {
        setIsLoading(false);
      }
    }
  };
  

  const handleRemoveFile = (index: number) => {
    setSelectedFiles(prevFiles => prevFiles.filter((_, i) => i !== index));
  };

  const handleScrapClick = async () => {
    if (isScrapingActive()) {
      alert('Scraping is already in progress');
      return;
    }

    setIsLoading(true);
    try {
      // Close the popup immediately
      onClose();

      // Start scraping using the service (now async)
      await startScraping();

      // Call the callback if provided
      if (onStartScraping) {
        onStartScraping();
      }
    } catch (error) {
      console.error('Error during scraping:', error);
      alert('Error starting scraping');
    } finally {
      setIsLoading(false);
    }
  };

  // Listen for status changes
  // useEffect(() => {
  //   const handleStatusChange = (message: ChromeMessage) => {
  //     if (message.action === 'updateScrapingState') {
  //       if (message.isActive) {
  //         // If scraping becomes active, close the popup
  //         onClose();
  //       }
  //     }
  //   };

  //   // Add listener
  //   if (typeof chrome !== 'undefined' && chrome.runtime) {
  //     chrome.runtime.onMessage.addListener(handleStatusChange);
  //   }

  //   // Cleanup
  //   return () => {
  //     if (typeof chrome !== 'undefined' && chrome.runtime) {
  //       chrome.runtime.onMessage.removeListener(handleStatusChange);
  //     }
  //   };
  // }, [onClose]);


  return (
    <div className="ai-options-overlay">
      <div className="ai-options-popup">
        <div className="ai-options-header">
          <span className="ai-options-title">
            <span dangerouslySetInnerHTML={{ __html: ai }} />
            {translate("AI Options")}
          </span>
          <button className="close-button" onClick={onClose} aria-label={translate("Close")}> {/* accessibility */}
            <CloseIcon />
          </button>
        </div>

        <div className="ai-options-content">
          {!showScrapingButton && (
            <>
              <label className="option-button upload-button">
                <span className="option-icon" dangerouslySetInnerHTML={{ __html: upload }} />
                {translate("Upload File")}
                <input
                  type="file"
                  hidden
                  onChange={handleFileChange}
                  accept=".pdf,.doc,.docx,.txt"
                  multiple
                />
              </label>

              {selectedFiles.length > 0 && (
                <div className="file-list">
                  <h4>{translate("Selected Files:")}</h4>
                  <ul>
                    {selectedFiles.map((file, index) => (
                      <li key={index} className="file-item">
                        <span className="file-name">{file.name}</span>
                        <button
                          className="remove-file-btn"
                          onClick={() => handleRemoveFile(index)}
                          aria-label={translate("Remove file")}
                        >
                          ×
                        </button>
                      </li>
                    ))}
                  </ul>
                  <button
                    className="upload-submit-button"
                    onClick={handleUploadClick}
                    disabled={isLoading}
                  >
                    {isLoading ? translate('Uploading...') : translate('Upload')}
                  </button>
                </div>
              )}
            </>
          )}

          {showScrapingButton && (
            <div className="scraping-section">
              <div className="uploaded-file-info">
                <h4>{translate("File Uploaded Successfully")}</h4>
                <p>{translate("You can now start scraping the page.")}</p>
              </div>
              <button
                className="option-button scraping-button"
                onClick={handleScrapClick}
                disabled={isLoading}
              >
                <span className="option-icon">🔍</span>
                {isLoading ? translate('Scraping...') : translate('Start Scraping')}
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AIOptionsPopup;