.properties-panel {
    border: 1px solid #ccc;
    padding: 10px;
    border-radius: 8px;
    width: 250px;
  }
  
  .field {
    margin-bottom: 15px;
  }
  
  label {
    display: block;
    font-size: 14px;
    margin-bottom: 5px;
  }
  
  select,
  input {
    width: 100%;
    padding: 5px;
    border-radius: 4px;
    border: 1px solid #ccc;
  }
  
  .formatting-buttons {
    display: flex;
    gap: 10px;
  }
  
  .formatting-buttons button {
    flex: 1;
    padding: 8px;
    border: 1px solid #ccc;
    background-color: #f0f0f0;
    cursor: pointer;
  }
  
  .formatting-buttons .active {
    background-color: #d0e1f9;
  }
  
  .image-preview {
    width: 100%;
    height: 100px;
    margin-bottom: 15px;
    background-color: #f0f0f0;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  .image-preview img {
    max-width: 100%;
    max-height: 100%;
  }
  
  .image-preview img.fill {
    object-fit: cover;
  }
  
  .image-preview img.fit {
    object-fit: contain;
  }
  