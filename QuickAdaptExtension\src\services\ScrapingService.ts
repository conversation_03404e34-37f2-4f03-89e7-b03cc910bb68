// ScrapingService.ts - <PERSON>les targeted click-based scraping functionality

import useDrawerStore, { DrawerState } from '../store/drawerStore';
import {NewAgentTraining} from '../services/AIService';
import { constants } from 'node:crypto';

import i18n from '../multilinguial/i18n';

// Get the translation function directly from i18next
const t = i18n.t.bind(i18n);


let _currentAgentData: {
  accountId?: string;
  agentDescription?: string;
  agentName?: string;
  agentUrl?: string;
} | undefined = undefined;

// Global state to track if scraping is active
let _isScrapingActive = false;
let _scrapedData: any[] = [];
let _lastScrapedTimestamp: string = '';
let _elementMap: Map<string, ElementData> = new Map(); // Map to track elements by XPath
let _clickListener: ((event: MouseEvent) => void) | null = null;
let _highlightedElements: Set<HTMLElement> = new Set(); // Track highlighted elements
let _overlayElements: Set<HTMLElement> = new Set(); // Track overlay elements for click blocking
// Interface for element data
export interface ElementData {
  tagName: string;
  id: string;
  className: string;
  text: string;
  labelName: string; // Add labelName for fallback identification
  attributes: Record<string, string>;
  xpath: string;
  cssSelector: string;
  selector: string; // Primary selector (xpath with cssSelector fallback)
  rect: {
    top: number;
    left: number;
    width: number;
    height: number;
  };
  children: ElementData[];
  isVisible: boolean;
  timestamp: string;
  url: string; // Add URL field to track which page the element is from
}

// Interface for scraped page data
export interface ScrapedPageData {
  url: string;
  title: string;
  timestamp: string;
  elements: ElementData[];
}
export type ScrapedElement = {
  labelName: string;
  id: string;
  selector: string;
  xpath: string;
  cssSelector: string;
  value: string;
  type: string;
};

/**
 * Check if scraping is currently active
 */
export const isScrapingActive = (): boolean => {
  return _isScrapingActive;
};

/**
 * Set the scraping active state
 */
export const setScrapingActive = (active: boolean): void => {
  _isScrapingActive = active;
};

/**
 * Generate XPath for an element
 */

const generateXPath = (element: HTMLElement): string => {
  const tagName = element.tagName.toLowerCase();

  // ✅ ejs-multiselect logic
  if (tagName === 'ejs-multiselect') {
    if (element.id) {
      return `//*[@id="${element.id}"]`;
    }

    const input = element.querySelector('input');
    if (input && input.id) {
      return `//*[@id="${input.id}"]/ancestor::ejs-multiselect[1]`;
    }

    const dataId = element.getAttribute('data-id');
    if (dataId) {
      return `//ejs-multiselect[@data-id="${dataId}"]`;
    }

    const className = element.className;
    if (className) {
      const uniqueClasses = className
        .split(' ')
        .filter(cls => cls && !cls.startsWith('e-') && cls.length > 2);
      if (uniqueClasses.length > 0) {
        return `//ejs-multiselect[contains(@class, "${uniqueClasses[0]}")]`;
      }
    }

    return generatePositionalXPath(element);
  }

  // ✅ ejs-dropdownlist logic (updated)
  const dropdownContainer = tagName === 'ejs-dropdownlist'
    ? element
    : element.closest('ejs-dropdownlist');

  if (dropdownContainer instanceof HTMLElement) {
    const dropdownId = dropdownContainer.id;
    const input = dropdownContainer.querySelector('input');

    if (dropdownId && input) {
      return `//ejs-dropdownlist[@id="${dropdownId}"]/div/input`;
    }

    return generatePositionalXPath(dropdownContainer);
  }

  if (element.id) {
    return `//*[@id="${element.id}"]`;
  }

  return generatePositionalXPath(element);
};

const generatePositionalXPath = (element: HTMLElement): string => {
  const path: string[] = [];
  let current: Element | null = element;

  while (current && current.nodeType === Node.ELEMENT_NODE) {
    let selector = current.nodeName.toLowerCase();

    if (current.id) {
      selector += `[@id="${current.id}"]`;
      path.unshift(selector);
      break;
    } else {
      let sibling = current.previousElementSibling;
      let position = 1;
      while (sibling) {
        if (sibling.nodeName.toLowerCase() === selector) {
          position++;
        }
        sibling = sibling.previousElementSibling;
      }

      if (position > 1) {
        selector += `[${position}]`;
      }

      path.unshift(selector);
    }

    current = current.parentElement;
  }

  return '//' + path.join('/');
};





/**
 * Generate CSS selector for an element
 */


const generateCssSelector = (el: HTMLElement): string => {
  if (!el) return '';
  const path = [];
  while (el && el.nodeType === Node.ELEMENT_NODE) {
    let selector = el.tagName.toLowerCase();
    if (el.id) {
      // Check if ID starts with a number or contains special characters
      const id = el.id;
      if (/^[0-9]/.test(id) || /[^a-zA-Z0-9_-]/.test(id)) {
        // Use attribute selector for IDs that start with numbers or have special chars
        selector += `[id="${id}"]`;
      } else {
        // Use standard ID selector for valid IDs
        selector += `#${id}`;
      }
      path.unshift(selector);
      break;
    } else {
      // Safely handle className - it might be a string or SVGAnimatedString
      const className = getElementClassName(el);
      if (className) {
        selector += '.' + className.trim().replace(/\s+/g, '.');
      }
      path.unshift(selector);
      el = el.parentElement!;
    }
  }
  return path.join(' > ');
};

/**
 * Safely get className from an element (handles both HTML and SVG elements)
 */
const getElementClassName = (el: Element): string => {
  if (!el) return '';

  // For HTML elements, className is usually a string
  if (typeof el.className === 'string') {
    return el.className;
  }

  // For SVG elements, className might be an SVGAnimatedString
  if (el.className && typeof el.className === 'object' && 'baseVal' in el.className) {
    return (el.className as any).baseVal || '';
  }

  // Fallback: try to get class attribute directly
  return el.getAttribute('class') || '';
};

/**
 * Generate a labelName for an element that can be used for fallback identification
 */
const generateLabelName = (element: HTMLElement): string => {
  // Try to find a meaningful label or name for the element

  // 1. Check for explicit label element
  if (element.id) {
    const label = document.querySelector(`label[for="${element.id}"]`);
    if (label && label.textContent?.trim()) {
      return label.textContent.trim();
    }
  }

  // 2. Special handling for dropdown elements - prioritize text content over generic aria-labels
  const tagName = element.tagName.toLowerCase();
  const isDropdown = tagName === 'ejs-dropdownlist' ||
                     element.closest('ejs-dropdownlist') ||
                     element.getAttribute('role') === 'combobox' ||
                     element.className.includes('dropdown') ||
                     element.className.includes('select');

  if (isDropdown) {
    // For dropdowns, prioritize visible text content over generic aria-labels
    const textContent = element.textContent?.trim();
    if (textContent && textContent.length > 0) {
      // Filter out generic dropdown text like "dropdownlist", "combobox", etc.
      const genericTerms = ['dropdownlist', 'combobox', 'select', 'dropdown'];
      const isGeneric = genericTerms.some(term =>
        textContent.toLowerCase() === term.toLowerCase()
      );

      if (!isGeneric) {
        return textContent.length > 50 ? textContent.substring(0, 50) + '...' : textContent;
      }
    }
  }

  // 3. Check for aria-label (but not for dropdowns with generic values)
  const ariaLabel = element.getAttribute('aria-label');
  if (ariaLabel?.trim()) {
    // Skip generic aria-labels for dropdowns
    if (isDropdown) {
      const genericTerms = ['dropdownlist', 'combobox', 'select', 'dropdown'];
      const isGeneric = genericTerms.some(term =>
        ariaLabel.toLowerCase() === term.toLowerCase()
      );
      if (!isGeneric) {
        return ariaLabel.trim();
      }
    } else {
      return ariaLabel.trim();
    }
  }

  // 4. Check for title attribute
  const title = element.getAttribute('title');
  if (title?.trim()) {
    return title.trim();
  }

  // 5. Check for placeholder
  const placeholder = element.getAttribute('placeholder');
  if (placeholder?.trim()) {
    return placeholder.trim();
  }

  // 6. Check for name attribute
  const name = element.getAttribute('name');
  if (name?.trim()) {
    return name.trim();
  }

  // 7. Check for data-label or similar data attributes
  const dataLabel = element.getAttribute('data-label') || element.getAttribute('data-name');
  if (dataLabel?.trim()) {
    return dataLabel.trim();
  }

  // 8. Check for text content (for non-dropdown elements or as fallback)
  if (!isDropdown) {
    const textContent = element.textContent?.trim();
    if (textContent && textContent.length > 0) {
      return textContent.length > 50 ? textContent.substring(0, 50) + '...' : textContent;
    }
  }

  // 9. Check for value attribute (for inputs)
  const value = element.getAttribute('value');
  if (value?.trim()) {
    return value.trim();
  }

  // 10. Fallback to element type and id/class
  const id = element.id;
  const className = getElementClassName(element);

  if (id) {
    return `${tagName}#${id}`;
  } else if (className) {
    const firstClass = className.split(' ')[0];
    return `${tagName}.${firstClass}`;
  }

  // 11. Final fallback
  return tagName;
};

/**
 * Check if element should be ignored for highlighting
 */
const shouldIgnoreHighlight = (element: HTMLElement): boolean => {
  return (
    element.classList.contains('AgentAdditionalContextPopup') ||
    element.classList.contains("mdc-tooltip__surface") ||
    element.classList.contains("mdc-tooltip__surface-animation") ||
    element.classList.contains("mdc-tooltip") ||
    element.classList.contains("mdc-tooltip--shown") ||
    element.classList.contains("mdc-tooltip--showing") ||
    element.classList.contains("mdc-tooltip--hiding") ||
    element.getAttribute("role") === "tooltip" ||
    !!element.closest("#Tooltip-unique") ||
    !!element.closest("#my-react-drawer") ||
    !!element.closest("#tooltip-section-popover") ||
    !!element.closest("#btn-setting-toolbar") ||
    !!element.closest("#button-toolbar") ||
    !!element.closest("#color-picker") ||
    !!element.closest(".qadpt-ext-banner") ||
    !!element.closest("#leftDrawer") ||
    !!element.closest("#image-popover") ||
    !!element.closest("#toggle-fit") ||
    !!element.closest("#color-popover") ||
    !!element.closest("#rte-popover") ||
    !!element.closest("#rte-alignment") ||
    !!element.closest("#rte-alignment-menu") ||
    !!element.closest("#rte-font") ||
    !!element.closest("#rte-bold") ||
    !!element.closest("#rte-italic") ||
    !!element.closest("#rte-underline") ||
    !!element.closest("#rte-strke-through") ||
    !!element.closest("#rte-alignment-menu-items") ||
    !!element.closest("#rte-more") ||
    !!element.closest("#rte-text-color") ||
    !!element.closest("#rte-text-color-popover") ||
    !!element.closest("#rte-text-highlight") ||
    !!element.closest("#rte-text-highlight-pop") ||
    !!element.closest("#rte-text-heading") ||
    !!element.closest("#rte-text-heading-menu-items") ||
    !!element.closest("#rte-text-format") ||
    !!element.closest("#rte-text-ul") ||
    !!element.closest("#rte-text-hyperlink") ||
    !!element.closest("#rte-video") ||
    !!element.closest("#rte-clear-formatting") ||
    !!element.closest("#rte-hyperlink-popover") ||
    !!element.closest("#rte-box") ||
    !!element.closest(element.id.startsWith("rt-editor") ? `#${element.id}` : "nope") ||
    !!element.closest("#rte-placeholder") ||
    !!element.closest("#qadpt-designpopup") ||
    !!element.closest("#image-properties") ||
    !!element.closest("#rte-toolbar") ||
    !!element.closest("#tooltipdialog") ||
    !!element.closest("#rte-toolbar-paper") ||
    !!element.closest("#stop-scraping-button-container") ||
    !!element.closest("#rte-alignment-menu") ||
    !!element.closest("#rte-alignment-menu-items") ||
    !!element.closest("#quickadapt-scraping-instructions") || // Ignore our own instruction banner
    !!element.closest("#AgentAdditionalContextPopup") || // Ignore our own instruction banner
    !!element.closest("#textareaadditionalcontextpopup")

  );
};

/**
 * Check if element should be ignored for events
 */
const shouldIgnoreEvents = (element: HTMLElement): boolean => {
  return (
    element.classList.contains("mdc-tooltip__surface") ||
    element.classList.contains("mdc-tooltip__surface-animation") ||
    element.classList.contains("mdc-tooltip") ||
    element.classList.contains("mdc-tooltip--shown") ||
    element.classList.contains("mdc-tooltip--showing") ||
    element.classList.contains("mdc-tooltip--hiding") ||
    element.getAttribute("role") === "tooltip" ||
    !!element.closest("#Tooltip-unique") ||
    !!element.closest("#tooltip-section-popover") ||
    !!element.closest("#btn-setting-toolbar") ||
    !!element.closest("#button-toolbar") ||
    !!element.closest("#color-picker") ||
    !!element.closest(".qadpt-ext-banner") ||
    !!element.closest("#leftDrawer") ||
    !!element.closest("#rte-popover") ||
    !!element.closest("#stop-scraping-button-container") ||
    !!element.closest(element.id.startsWith("rt-editor") ? `#${element.id}` : "nope") ||
    !!element.closest("#rte-box") ||
    !!element.closest("#rte-placeholder") ||
    !!element.closest("#rte-alignment-menu-items") ||
    !!element.closest("#qadpt-designpopup") ||
    !!element.closest("#quickadapt-scraping-instructions")||
     // Ignore our own instruction banner
     !!element.closest("#AgentAdditionalContextPopup")|| // Ignore our own instruction banner
    !!element.closest("#textareaadditionalcontextpopup")
  );
};

/**
 * Add persistent red border to element WITHOUT blocking clicks
 */
const addPersistentHighlightWithoutBlocking = (element: HTMLElement): void => {
  if (shouldIgnoreHighlight(element)) return;

  // Add persistent red border
  element.style.outline = '3px solid #ff0000 !important';
  element.style.outlineOffset = '2px';
  element.setAttribute('data-quickadapt-highlighted', 'true');
  _highlightedElements.add(element);

  // No overlay creation - allow clicks to pass through
};

/**
 * Add persistent red border to element and create click-blocking overlay (legacy function)
 */
const addPersistentHighlight = (element: HTMLElement): void => {
  if (shouldIgnoreHighlight(element)) return;

  // Add persistent red border
  element.style.outline = '3px solid #ff0000 !important';
  element.style.outlineOffset = '2px';
  element.setAttribute('data-quickadapt-highlighted', 'true');
  _highlightedElements.add(element);

  // Create click-blocking overlay
  const overlay = document.createElement('div');
  overlay.style.cssText = `
    position: absolute;
    top: ${element.offsetTop}px;
    left: ${element.offsetLeft}px;
    width: ${element.offsetWidth}px;
    height: ${element.offsetHeight}px;
    background: transparent;
    z-index: 999999;
    pointer-events: auto;
    cursor: not-allowed;
  `;
  overlay.setAttribute('data-quickadapt-overlay', 'true');
 
  
  const tooltipText = t('Element already scraped click blocked');
  overlay.title = tooltipText;

  // Position overlay relative to the element's parent
  const rect = element.getBoundingClientRect();
  const parentRect = element.offsetParent?.getBoundingClientRect() || { top: 0, left: 0 };

  overlay.style.top = `${rect.top - parentRect.top + window.scrollY}px`;
  overlay.style.left = `${rect.left - parentRect.left + window.scrollX}px`;

  // Add overlay to the element's parent or body
  const parent = element.offsetParent || document.body;
  parent.appendChild(overlay);
  _overlayElements.add(overlay);

  // Block clicks on the overlay
  overlay.addEventListener('click', (e) => {
    e.preventDefault();
    e.stopPropagation();
  }, true);
};

/**
 * Remove all highlights and overlays
 */
const removeAllHighlights = (): void => {
  // Remove highlights
  _highlightedElements.forEach(element => {
    if (element && element.style) {
      element.style.outline = '';
      element.style.outlineOffset = '';
      element.removeAttribute('data-quickadapt-highlighted');
    }
  });
  _highlightedElements.clear();

  // Remove overlays
  _overlayElements.forEach(overlay => {
    if (overlay && overlay.parentNode) {
      overlay.parentNode.removeChild(overlay);
    }
  });
  _overlayElements.clear();
};

/**
 * Show brief visual feedback when an element is clicked and scraped
 */
const showClickFeedback = (element: HTMLElement): void => {
  try {
    // Create a temporary feedback indicator
    const feedback = document.createElement('div');
    feedback.style.cssText = `
      position: absolute;
      background: #4CAF50;
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: bold;
      z-index: 10001;
      pointer-events: none;
      box-shadow: 0 2px 8px rgba(0,0,0,0.3);
      opacity: 0;
      transition: opacity 0.2s ease;
    `;
  
    
    const scrapedText = `✓ ${t('Scraped')}`;
    feedback.textContent = scrapedText;

    // Position the feedback near the clicked element
    const rect = element.getBoundingClientRect();
    feedback.style.left = `${rect.left + window.scrollX}px`;
    feedback.style.top = `${rect.top + window.scrollY - 30}px`;

    document.body.appendChild(feedback);

    // Animate in
    setTimeout(() => {
      feedback.style.opacity = '1';
    }, 10);

    // Remove after 2 seconds
    setTimeout(() => {
      feedback.style.opacity = '0';
      setTimeout(() => {
        if (feedback.parentNode) {
          feedback.parentNode.removeChild(feedback);
        }
      }, 200);
    }, 2000);
  } catch (error) {
  }
};

/**
 * Extract data from a single element (optimized for click-based scraping)
 */
const extractElementData = (element: HTMLElement): ElementData & { value?: any ,type?:any } => {
  const rect = element.getBoundingClientRect();
  const xpath = generateXPath(element);
  const type =   getElementType(element);
  const cssSelector = generateCssSelector(element);

  // Helper to get value from input/select/textarea or custom dropdowns
  function getFieldValue(el: HTMLElement): any {
    // Standard HTML form elements
    if (el instanceof HTMLInputElement || el instanceof HTMLTextAreaElement) {
      return el.value;
    }
    if (el instanceof HTMLSelectElement) {
      return el.options[el.selectedIndex]?.text || el.value;
    }
    // Syncfusion/other custom dropdowns (e.g., ejs-dropdownlist)
    // Try to find an input or selected item inside
    const input = el.querySelector('input');
    if (input && input instanceof HTMLInputElement) {
      return input.value;
    }
    // Try to find selected item span (common in custom dropdowns)
    const selectedSpan = el.querySelector('.e-selected-item, .e-list-item.e-active, .e-ddl .e-input, .e-dropdownlist .e-input');
    if (selectedSpan) {
      return selectedSpan.textContent?.trim();
    }
    // Try data-value attribute
    if (el.hasAttribute('data-value')) {
      return el.getAttribute('data-value');
    }
    return undefined;
  }

  // Try to get value from the element itself
  let value = getFieldValue(element);
  // If not found, try to get from a child input/select/textarea
  if (value === undefined) {
    const childField = element.querySelector('input, select, textarea');
    if (childField) {
      value = getFieldValue(childField as HTMLElement);
    }
  }

  return {
    tagName: element.tagName,
    id: element.id || '',
    className: getElementClassName(element),
    text: element.textContent?.trim() || element.id,  //Added for agent training
    labelName: generateLabelName(element), // Add labelName for fallback identification
    attributes: Array.from(element.attributes).reduce((acc, attr) => {
      acc[attr.name] = attr.value;
      return acc;
    }, {} as Record<string, string>),
    xpath,
    cssSelector,
    selector: xpath || cssSelector, // Primary selector with fallback
    rect: {
      top: rect.top,
      left: rect.left,
      width: rect.width,
      height: rect.height
    },
    children: [], // We don't need children for click-based scraping
    isVisible: rect.width > 0 && rect.height > 0,
    timestamp: new Date().toISOString(),
    url: window.location.href, // Add URL to each element
    type,
    ...(value !== undefined ? { value } : {})
  };
};


const getElementType = (element: HTMLElement): any => {
  const tagName = element.tagName.toLowerCase();

  // Check for ejs-radiobutton first
  if (tagName === 'ejs-radiobutton' || element.closest('ejs-radiobutton')) {
    return 'radio';
  }

  if (tagName === 'input') {
    const inputElement = element as HTMLInputElement;
    const type = inputElement.type || 'text';

    // Special handling for date inputs
    if (
      type === 'text' &&
      (
        element.id.toLowerCase().includes('date') ||
        element.className.toLowerCase().includes('date') ||
        (element.getAttribute('placeholder') || '').toLowerCase().includes('date')
      )
    ) {
      // Check if it's a date range input or single date input
      const value = inputElement.value || '';
      const placeholder = element.getAttribute('placeholder') || '';
      const id = element.id.toLowerCase();
      const className = element.className.toLowerCase();
      const name = (element.getAttribute('name') || '').toLowerCase();

      // Check if the value contains date range patterns (e.g., "01/01/2023 - 01/31/2023")
      const dateRangePattern = /\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4}\s*[-–—]\s*\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4}/;
      const hasDateRangeValue = dateRangePattern.test(value);

      // Check for explicit date range terms (more specific)
      const explicitRangeTerms = [
        'daterange', 'date-range', 'date_range',
        'period', 'duration', 'span','dates'
      ];
      const hasExplicitRangeClass = explicitRangeTerms.some(term =>
        id.includes(term) || className.includes(term) || name.includes(term)
      );

      // Check for range indicators in a more specific way
      const rangeIndicators = [
        'fromdate', 'todate', 'startdate', 'enddate',
        'date_from', 'date_to', 'date-from', 'date-to',
        'start_date', 'end_date', 'start-date', 'end-date'
      ];

      const hasSpecificRangeIndicator = rangeIndicators.some(indicator =>
        id.includes(indicator) || className.includes(indicator) || name.includes(indicator)
      );

      // Check for range indicators in placeholder (more specific)
      const placeholderRangeTerms = ['from', 'to', 'between', 'range', '-', '–', '—'];
      const hasPlaceholderRangeIndicator = placeholderRangeTerms.some(term =>
        placeholder.toLowerCase().includes(term)
      );

      // Return daterange only if we have strong indicators, otherwise dateinput
      if (hasDateRangeValue || hasExplicitRangeClass || hasSpecificRangeIndicator || hasPlaceholderRangeIndicator) {
        return 'daterange';
      } else {
        return 'dateinput';
      }
    }

    // Inside a dropdown component
    if (element.closest('ejs-dropdownlist, ejs-combobox, ejs-multiselect, [role="combobox"]')) {
      return 'dropdown';
    }

    return type;
  }

  // ejs-dropdownlist
  if (tagName === 'ejs-dropdownlist' || element.closest('ejs-dropdownlist')) {
    return 'dropdown';
  }

  // select
  if (tagName === 'select') {
    const selectElement = element as HTMLSelectElement;
    return selectElement.multiple ? 'multiselect' : 'dropdown';
  }

  // textarea
  if (tagName === 'textarea') {
    return 'textarea';
  }

  // contenteditable
  if (element.contentEditable === 'true') {
    return 'contenteditable';
  }

  // dropdown-like custom components
  if (
    element.getAttribute('role') === 'combobox' ||
    element.getAttribute('role') === 'listbox' ||
    element.className.includes('dropdown') ||
    element.className.includes('select')
  ) {
    return 'dropdown';
  }

  // ✅ Fallback for clickable, non-input UI elements
  return 'click';
};





/**
 * Handle click events for element scraping
 */

const handleElementClick = async ( event: MouseEvent,agentData?: {
  accountId?: string;
  agentDescription?: string;
  agentName?: string;
  agentUrl?: string;
}): Promise<void> => {
  try {
    // IMPORTANT: Don't prevent default or stop propagation
    // This allows the original click functionality to work normally
    // (navigation, form submission, button clicks, etc.)

    const target = event.target as HTMLElement;
    if (!target || !target.nodeType || target.nodeType !== Node.ELEMENT_NODE) {
      return;
    }

    if (shouldIgnoreEvents(target)) {
      return;
    }

    if (target.hasAttribute('data-quickadapt-highlighted')) {
      return;
    }


    // Extract data from clicked element ONLY
    const clickedElementData = extractElementData(target);

    // Store only the clicked element data (no parent element)
    const elementsToStore = [clickedElementData];

    // Add to scraped data
    console.log(`📝 Attempting to scrape element: ${target.tagName} with XPath: ${clickedElementData.xpath}`);
    setScrapedData({ elements: elementsToStore }, true);

    // Save to local storage immediately after each element is scraped

    await saveScrapedDataToStorage(agentData);
    console.log(`💾 Element data saved to local storage immediately`);

    // Add persistent red border WITHOUT blocking clicks (only to clicked element)
    addPersistentHighlightWithoutBlocking(target);


    // // Show brief success feedback
    // showClickFeedback(target);


  } catch (error) {
    console.error('Error in handleElementClick:', error);
  }
};

export const setScrapedData = (data: any, append: boolean = false): void => {
  const timestamp = new Date().toISOString();
  _lastScrapedTimestamp = timestamp;

  if (!append) {
    // Clear existing data if not appending
    _scrapedData = [];
    _elementMap.clear();
  }

  // Process each element in the data
  if (data && data.elements && Array.isArray(data.elements)) {
    data.elements.forEach((element: ElementData) => {
      // Add timestamp to the element
      element.timestamp = timestamp;

      // Use XPath as a unique identifier for the element
      if (element.xpath) {
        // If element already exists in the map, don't add it again (prevent duplicates)
        if (_elementMap.has(element.xpath)) {
          console.log(`⚠️ Skipping duplicate element with XPath: ${element.xpath}`);
          return; // Skip this element
        } else {
          // New element, add to map and data array
          console.log(`✅ Adding new element with XPath: ${element.xpath}`);
          _elementMap.set(element.xpath, element);
          _scrapedData.push(element);
        
        }
      } else {
        // No XPath, check for duplicates by other means (tagName + id + className)
        const isDuplicate = _scrapedData.some(existing =>
          existing.tagName === element.tagName &&
          existing.id === element.id &&
          existing.className === element.className
        );

        if (!isDuplicate) {
          _scrapedData.push(element);

        } else {

        }
      }
    });
  }
};

/**
 * Get the currently scraped data
 */
export const getScrapedData = (): any[] => {
  return _scrapedData;
};

/**
 * Get element by XPath
 */
export const getElementByXPath = (xpath: string): ElementData | undefined => {
  return _elementMap.get(xpath);
};

/**
 * Find DOM element using fallback mechanisms
 * Priority: xpath -> labelName -> cssSelector
 */
// export const findElementWithFallback = (elementData: {
//   xpath?: string;
//   labelName?: string;
//   cssSelector?: string;
//   id?: string;
// }): HTMLElement | null => {
//   // Primary: Try xpath first
//   if (elementData.xpath) {
//     try {
//       const result = document.evaluate(
//         elementData.xpath,
//         document,
//         null,
//         XPathResult.FIRST_ORDERED_NODE_TYPE,
//         null
//       );
//       const element = result.singleNodeValue as HTMLElement;
//       if (element) {
//         console.log(`✅ Found element using xpath: ${elementData.xpath}`);
//         return element;
//       }
//     } catch (error) {
//       console.warn(`❌ XPath failed: ${elementData.xpath}`, error);
//     }
//   }

//   // Fallback 1: Try to find by labelName
//   if (elementData.labelName) {
//     console.log(`🔄 Falling back to labelName: ${elementData.labelName}`);

//     // Try different approaches to find by labelName
//     const labelSelectors = [
//       `[aria-label="${elementData.labelName}"]`,
//       `[title="${elementData.labelName}"]`,
//       `[placeholder="${elementData.labelName}"]`,
//       `[name="${elementData.labelName}"]`,
//       `[data-label="${elementData.labelName}"]`,
//       `[data-name="${elementData.labelName}"]`
//     ];

//     for (const selector of labelSelectors) {
//       try {
//         const element = document.querySelector(selector) as HTMLElement;
//         if (element) {
//           console.log(`✅ Found element using labelName selector: ${selector}`);
//           return element;
//         }
//       } catch (error) {
//         console.warn(`❌ LabelName selector failed: ${selector}`, error);
//       }
//     }

//     // Try to find by text content containing the labelName
//     try {
//       const xpath = `//*[contains(text(), "${elementData.labelName}")]`;
//       const result = document.evaluate(
//         xpath,
//         document,
//         null,
//         XPathResult.FIRST_ORDERED_NODE_TYPE,
//         null
//       );
//       const element = result.singleNodeValue as HTMLElement;
//       if (element) {
//         console.log(`✅ Found element using text content: ${elementData.labelName}`);
//         return element;
//       }
//     } catch (error) {
//       console.warn(`❌ Text content search failed for: ${elementData.labelName}`, error);
//     }

//     // Try to find label element and get its associated input
//     try {
//       const labelElement = Array.from(document.querySelectorAll('label')).find(
//         label => label.textContent?.trim() === elementData.labelName
//       );
//       if (labelElement) {
//         const forAttribute = labelElement.getAttribute('for');
//         if (forAttribute) {
//           const associatedElement = document.getElementById(forAttribute) as HTMLElement;
//           if (associatedElement) {
//             console.log(`✅ Found element using label association: ${elementData.labelName}`);
//             return associatedElement;
//           }
//         }
//       }
//     } catch (error) {
//       console.warn(`❌ Label association search failed for: ${elementData.labelName}`, error);
//     }
//   }

//   // Fallback 2: Try cssSelector
//   if (elementData.cssSelector) {
//     console.log(`🔄 Falling back to cssSelector: ${elementData.cssSelector}`);
//     try {
//       const element = document.querySelector(elementData.cssSelector) as HTMLElement;
//       if (element) {
//         console.log(`✅ Found element using cssSelector: ${elementData.cssSelector}`);
//         return element;
//       }
//     } catch (error) {
//       console.warn(`❌ CSS selector failed: ${elementData.cssSelector}`, error);

//       // Try to fix common CSS selector issues
//       if (elementData.cssSelector.includes('#')) {
//         try {
//           // Extract ID and try attribute selector
//           const idMatch = elementData.cssSelector.match(/#([^.\s>+~]+)/);
//           if (idMatch) {
//             const id = idMatch[1];
//             const tagMatch = elementData.cssSelector.match(/^([a-zA-Z]+)/);
//             const tag = tagMatch ? tagMatch[1] : '';
//             const attributeSelector = tag ? `${tag}[id="${id}"]` : `[id="${id}"]`;

//             console.log(`🔄 Trying attribute selector fallback: ${attributeSelector}`);
//             const element = document.querySelector(attributeSelector) as HTMLElement;
//             if (element) {
//               console.log(`✅ Found element using attribute selector: ${attributeSelector}`);
//               return element;
//             }
//           }
//         } catch (attributeError) {
//           console.warn(`❌ Attribute selector fallback also failed`, attributeError);
//         }
//       }
//     }
//   }

//   // Final fallback: Try by ID if available
//   if (elementData.id) {
//     console.log(`🔄 Final fallback to ID: ${elementData.id}`);
//     try {
//       const element = document.getElementById(elementData.id) as HTMLElement;
//       if (element) {
//         console.log(`✅ Found element using ID: ${elementData.id}`);
//         return element;
//       }
//     } catch (error) {
//       console.warn(`❌ ID selector failed: ${elementData.id}`, error);
//     }
//   }

//   console.error(`❌ All fallback methods failed for element:`, elementData);
//   return null;
// };

/**
 * Test function to demonstrate element finding with fallback mechanisms
 * This can be called from browser console for testing
 */
// export const testElementFinding = (elementData: {
//   xpath?: string;
//   labelName?: string;
//   cssSelector?: string;
//   id?: string;
// }): void => {
//   console.log('🧪 Testing element finding with fallback mechanisms...');
//   console.log('Input data:', elementData);

//   const foundElement = findElementWithFallback(elementData);

//   if (foundElement) {
//     console.log('✅ Successfully found element:', foundElement);
//     console.log('Element details:', {
//       tagName: foundElement.tagName,
//       id: foundElement.id,
//       className: foundElement.className,
//       textContent: foundElement.textContent?.substring(0, 100)
//     });
//   } else {
//     console.log('❌ Could not find element with any fallback method');
//   }
// };



/**
 * Get all xpath data from scraped elements
 */
export const getXPathData = (): Array<{xpath: string, tagName: string, id: string, className: string, text: string, timestamp: string, url: string}> => {
  return _scrapedData.map(element => ({
    xpath: element.xpath,
    tagName: element.tagName,
    id: element.id,
    className: element.className,
    text: element.text,
    timestamp: element.timestamp,
    url: element.url
  }));
};

/**
 * Manually send current scraped data to backend API (can be called independently)
 */
export const exportScrapedDataToFile = async (accountId?: string): Promise<void> => {
  try {
    if (_scrapedData.length === 0) {
      // Try to load from storage if no current data
      const storedData = await loadScrapedDataFromStorage();
      if (!storedData || !storedData.scrapedData || storedData.scrapedData.length === 0) {
        // alert('No scraped data available to send to API. Please scrape some elements first.');
        return;
      }
      // Use stored data for API call
      await saveScrapedDataToFile(accountId);
    } else {
      // Save current data to storage first, then send to API
      await saveScrapedDataToStorage();
      await saveScrapedDataToFile(accountId);
    }
  } catch (error) {
    // alert('Error sending scraped data to backend API. Check console for details.');
  }
};

/**
 * Get scraped data count
 */
export const getScrapedDataCount = (): number => {
  return _scrapedData.length;
};

/**
 * Check if there's existing scraped data in storage
 */
export const hasScrapedDataInStorage = async (): Promise<boolean> => {
  try {
    const storedData = await loadScrapedDataFromStorage();
    return storedData && storedData.scrapedData && Array.isArray(storedData.scrapedData) && storedData.scrapedData.length > 0;
  } catch (error) {
    return false;
  }
};

/**
 * Clear all scraped data (both in-memory and storage) - use when starting completely fresh
 */
export const clearAllScrapedData = async (): Promise<void> => {
  console.log('🧹 Clearing all scraped data (memory + storage)');
  clearScrapedData(); // Clear in-memory data
  await clearScrapedDataFromStorage(); // Clear storage data
  removeAllHighlights(); // Clear visual highlights
};

/**
 * Get current scraping status for debugging
 */
export const getScrapingStatus = (): {
  isActive: boolean;
  elementCount: number;
  elementMapSize: number;
  lastTimestamp: string;
  highlightedElementsCount: number;
} => {
  return {
    isActive: _isScrapingActive,
    elementCount: _scrapedData.length,
    elementMapSize: _elementMap.size,
    lastTimestamp: _lastScrapedTimestamp,
    highlightedElementsCount: _highlightedElements.size
  };
};

/**
 * Get the timestamp of the last scrape
 */
export const getLastScrapedTimestamp = (): string => {
  return _lastScrapedTimestamp;
};

/**
 * Clear scraped data
 */
export const clearScrapedData = (): void => {
  console.log(`🧹 Clearing scraped data - had ${_scrapedData.length} elements and ${_elementMap.size} in element map`);
  _scrapedData = [];
  _lastScrapedTimestamp = '';
  _elementMap.clear(); // Clear the element map to allow re-scraping of same elements
};

/**
 * Clear scraped data from Chrome storage (for debugging/reset purposes)
 */
export const clearScrapedDataFromStorage = async (): Promise<void> => {
 
  try {
          localStorage.removeItem('quickadapt-scraped-data');

  } catch (error) {
    console.error('Error clearing scraped data from storage:', error);
  }
};

/**
 * Save scraped data to Chrome storage
 */

export const saveScrapedDataToStorage = async (agentData?: {
  accountId?: string;
  agentDescription?: string;
  agentName?: string;
  agentUrl?: string;
}): Promise<void> => {
  try {
      const storageData = {
        scrapedData: _scrapedData,
        timestamp: _lastScrapedTimestamp,
        url: window.location.href,
        title: document.title,
        elementCount: _scrapedData.length,
        xpathData: _scrapedData.map(element => ({
          xpath: element.xpath,
          tagName: element.tagName,
          id: element.id,
          className: element.className,
          text: element.text,
          labelName: element.labelName,
          cssSelector: element.cssSelector,
          selector: element.selector,
          attributes: element.attributes,
          timestamp: element.timestamp,
          url: element.url

        })),
        // Add agent data if provided
        ...(agentData && {
          agentData: {
            AccountId: agentData.accountId,
            Description: agentData.agentDescription,
            Name: agentData.agentName,
            url: agentData.agentUrl
          }
        })
      };
    localStorage.setItem('quickadapt-scraped-data', JSON.stringify(storageData));
   storageData.xpathData.forEach((item, index) => {
        console.log(`${index + 1}. ${item.tagName} - ${item.xpath}`);
      });

  } catch (error) {
  }
};

/**
 * Load scraped data from Chrome storage
 */
export const loadScrapedDataFromStorage = async (): Promise<any> => {
  try {
    
      const data = localStorage.getItem('quickadapt-scraped-data');
      return data ? JSON.parse(data) : null;
    
  } catch (error) {
    return null;
  }
};



/**
 * Send scraped data from Chrome storage to backend API
 */
export const saveScrapedDataToFile = async (accountId?: string): Promise<void> => {
  try {

    const storedData = await loadScrapedDataFromStorage();

    if (!storedData) {
      return;
    }

    const apiData = {
      metadata: {
        url: storedData.url || window.location.href,
        title: storedData.title || document.title,
        timestamp: storedData.timestamp || new Date().toISOString(),
        elementCount: storedData.elementCount || 0,
        exportedAt: new Date().toISOString()
      },
      elements: storedData.scrapedData || [],
      xpathData: storedData.xpathData || []
    };


    // Send data to backend API
    await uploadXPathsFile(apiData, accountId);

  } catch (error) {
  }
};

/**
 * Upload XPath data to backend API using existing FileService
 */
export const uploadXPathsFile = async (data: any, accountId?: string): Promise<void> => {
  try {

    // Convert JSON data to FormData as expected by the existing API
    const formData = new FormData();

    // Create a JSON file blob
    const jsonBlob = new Blob([JSON.stringify(data, null, 2)], {
      type: 'application/json'
    });

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const pageTitle = (data.metadata.title || 'scraped-data').replace(/[^a-zA-Z0-9]/g, '-');
    const filename = `quickadapt-xpath-data-${pageTitle}-${timestamp}.json`;

    // Add the file to FormData
    formData.append('aifiles', jsonBlob, filename); // ✅ Correct key name

    // Add metadata as form fields if needed
    formData.append('elementCount', data.metadata.elementCount.toString());
    formData.append('url', data.metadata.url);
    formData.append('timestamp', data.metadata.timestamp);

   

    // Import and use the existing uploadXpathsFile function
    const { uploadXpathsFile } = await import('./FileService');

    if (!accountId) {
      throw new Error('Account ID is required to upload XPath data');
    }

    const response = await uploadXpathsFile(accountId, formData);

  } catch (error) {

    // Show error message to user
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

    throw error; // Re-throw to be caught by calling function
  }
};

/**
 * Start click-based scraping process
 */

export const startAgentScraping = async (agentName: string, agentDescription: string, accountId: string, agentUrl: string): Promise<void> => {
  if (_isScrapingActive) return;

  _isScrapingActive = true;
  console.log('🎯 Starting scraping session - checking storage consistency');

  // Check if Chrome storage has scraped data
  const storedData = await loadScrapedDataFromStorage();
  if (!storedData || !storedData.scrapedData || !Array.isArray(storedData.scrapedData) || storedData.scrapedData.length === 0) {
    console.log('📊 No valid data in Chrome storage - clearing in-memory data');
    clearScrapedData(); // Clear in-memory data if storage is empty
    await saveScrapedDataToStorage({
      accountId,
      agentDescription,
      agentName,
      agentUrl
    });
  } else {
    
    console.log(`📊 Storage validation passed - ${storedData.scrapedData.length} elements in storage, ${_scrapedData.length} in memory`);
  }

  console.log(`📊 Current scraped elements count: ${_scrapedData.length}`);
 _currentAgentData = {
    accountId,
    agentDescription,
    agentName,
    agentUrl
  };
  // Re-highlight existing scraped elements instead of clearing all highlights
  _scrapedData.forEach(element => {
    if (element.xpath) {
      try {
        const elementNode = document.evaluate(
          element.xpath,
          document,
          null,
          XPathResult.FIRST_ORDERED_NODE_TYPE,
          null
        ).singleNodeValue as HTMLElement;

        if (elementNode) {
          addPersistentHighlightWithoutBlocking(elementNode);
        }
      } catch (error) {
        // Element might not exist anymore, that's okay
      }
    }
  });

  // Add click event listener to capture element clicks
  if (!_clickListener) {
    _clickListener = (event: MouseEvent) => {
      // Call the async function without awaiting to avoid blocking the event handler

      handleElementClick(event,_currentAgentData).catch(error => {
        console.error('Error in click handler:', error);
      });
    };
    document.addEventListener('click', _clickListener, true); // Use capture phase
  }

  // Send message to content script to enable click-based scraping
  if (typeof chrome !== 'undefined' && chrome.runtime) {
    try {
      chrome.runtime.sendMessage({
        action: 'startClickScraping'
      });
    } catch (error) {
      // Fallback: try to communicate through window events
      window.dispatchEvent(new CustomEvent('quickadapt-start-click-scraping'));
    }
  }

 
  // Show user instruction with translation support
  showScrapingInstructions();
};

 
export const startScraping = (): void => {
  if (_isScrapingActive) return;

  _isScrapingActive = true;
  clearScrapedData();


  // Add click event listener to capture element clicks
  if (!_clickListener) {
    _clickListener = handleElementClick;
    document.addEventListener('click', _clickListener, true); // Use capture phase
  }

  // Send message to content script to enable click-based scraping
  if (typeof chrome !== 'undefined' && chrome.runtime) {
    try {
      chrome.runtime.sendMessage({
        action: 'startClickScraping'
      });
    } catch (error) {
      // Fallback: try to communicate through window events
      window.dispatchEvent(new CustomEvent('quickadapt-start-click-scraping'));
    }
  }

  // Show user instruction
  showScrapingInstructions();
};


 



/**
 * Stop click-based scraping process
 */
export const stopScraping = async (
  isAgentTraining: boolean,
  accountId: string,
  agentName?: string,
  agentDescription?: string,
  agentUrl?: string,

): Promise<void> => {
  if (!_isScrapingActive) return;
  _isScrapingActive = false;


  // Remove click event listener
  if (_clickListener) {
    document.removeEventListener('click', _clickListener, true);
    _clickListener = null;
  }

  // Process scraped data before clearing from storage
  if (_scrapedData.length > 0) {


    // Save to storage one final time to ensure we have the latest data, including agent data
     await saveScrapedDataToStorage({
      accountId,
      agentDescription,
       agentName,
      agentUrl
    });



    // Get data from Chrome storage and save to file
    !isAgentTraining && await saveScrapedDataToFile(accountId);
   const filteredData = await getFilteredScrapedData();
   console.log(filteredData,"filteredData");

    const agent = {
      AccountId:accountId,
      Description:agentDescription,
      Name:agentName,
      TrainingFields:filteredData,
      url: window.location.href
    };
    if (isAgentTraining && agentName && agentDescription) {
      await NewAgentTraining(agent);
    }
  }

     await clearScrapedDataFromStorage();


  // Remove all highlights and overlays
  removeAllHighlights();

  // Send message to background script to stop scraping
  if (typeof chrome !== 'undefined' && chrome.runtime) {
    try {
      chrome.runtime.sendMessage({
        action: 'stopClickScraping'
      });
    } catch (error) {
      // Fallback: try to communicate through window events
      window.dispatchEvent(new CustomEvent('quickadapt-stop-click-scraping'));
    }
  }

  // Hide instructions
  hideScrapingInstructions();
};

/**
 * Cancel training process - stops scraping and clears data without processing
 */
export const cancelTraining = async (): Promise<void> => {
  if (!_isScrapingActive) return;
  _isScrapingActive = false;

  // Remove click event listener
  if (_clickListener) {
    document.removeEventListener('click', _clickListener, true);
    _clickListener = null;
  }

  // Clear scraped data from storage immediately without processing
  await clearScrapedDataFromStorage();
  console.log('🧹 Cleared scraped data from storage after cancel training');

  // Clear in-memory data
  clearScrapedData();

  // Remove all highlights and overlays
  removeAllHighlights();

  // Send message to background script to stop scraping
  if (typeof chrome !== 'undefined' && chrome.runtime) {
    try {
      chrome.runtime.sendMessage({
        action: 'stopClickScraping'
      });
    } catch (error) {
      // Fallback: try to communicate through window events
      window.dispatchEvent(new CustomEvent('quickadapt-stop-click-scraping'));
    }
  }

  // Hide instructions
  hideScrapingInstructions();
};

export const getFilteredScrapedData = async (): Promise<ScrapedElement[]> => {
  const storedData = await loadScrapedDataFromStorage();
  if (!storedData || !Array.isArray(storedData.scrapedData)) return [];

  return storedData.scrapedData.map((item: any) => {
    // Implement fallback logic for selector identification
    let primarySelector = item.xpath || '';
    let fallbackSelector = item.cssSelector || '';

    // If no xpath, use labelName as fallback identifier
    if (!primarySelector && item.labelName) {
      primarySelector = `[aria-label="${item.labelName}"]`; // Try aria-label first
      // Additional fallback selectors based on labelName
      if (!primarySelector) {
        primarySelector = `[title="${item.labelName}"]`; // Try title attribute
      }
      if (!primarySelector) {
        primarySelector = `[placeholder="${item.labelName}"]`; // Try placeholder
      }
      if (!primarySelector) {
        primarySelector = `[name="${item.labelName}"]`; // Try name attribute
      }
    }

    // If still no selector, use cssSelector as final fallback
    if (!primarySelector) {
      primarySelector = fallbackSelector;
    }

    return {
      Name:item.text || '',
      xpath: item.xpath || '',
      labelName: item.labelName  || '',
      selector: primarySelector,
      cssSelector: item.cssSelector || '',
      value: item.value || '',
      type: item.type || '',
    };
  });
};

/**
 * Show scraping instructions to user
 */
const showScrapingInstructions = (): void => {
  // Remove existing instruction if any
  hideScrapingInstructions();

  const instructionDiv = document.createElement('div');
  instructionDiv.id = 'quickadapt-scraping-instructions';
  instructionDiv.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #4CAF50;
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    font-family: Arial, sans-serif;
    font-size: 14px;
    font-weight: bold;
    z-index: 10000;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    max-width: 320px;
    text-align: center;
  `;
  
  // Use translations with i18n instance directly
  const mainTitle = `🎯 ${t('Click Scraping Active')}`;
  const clickElement = `• ${t('Click any element to scrape its XPath data')}`;
  const onlyClicked = `• ${t('Only the clicked element is scraped no duplicates')}`;
  const originalClick = `• ${t('Original click functionality still works')}`;
  const redBorders = `• ${t('Red borders show scraped elements')}`;
  const dataSaved = `• ${t('Data is saved to Chrome storage')}`;
  
  instructionDiv.innerHTML = `
  
    ${mainTitle}<br>
    <small style="font-weight: normal; opacity: 0.9; display: block; margin-top: 8px;">
      ${clickElement}<br>
      ${onlyClicked}<br>
      ${originalClick}<br>
      ${redBorders}<br>
      ${dataSaved}
    </small>
  `;

  document.body.appendChild(instructionDiv);

  // Auto-hide after 8 seconds
  setTimeout(() => {
    if (instructionDiv.parentNode) {
      instructionDiv.style.opacity = '0.7';
    }
  }, 8000);
};

/**
 * Hide scraping instructions
 */
const hideScrapingInstructions = (): void => {
  const existingInstruction = document.getElementById('quickadapt-scraping-instructions');
  if (existingInstruction) {
    existingInstruction.remove();
  }
};


/**
 * Initialize click-based scraping service
 * This should be called when the extension is loaded
 */
export const initScrapingService = async (): Promise<void> => {
  _isScrapingActive = false;
  _scrapedData = [];
  _elementMap.clear();
  _lastScrapedTimestamp = '';
  _clickListener = null;

  // Try to restore scraped data from storage (in case of page refresh)
  try {
    const storedData = await loadScrapedDataFromStorage();
    if (storedData && storedData.scrapedData && Array.isArray(storedData.scrapedData)) {
      _scrapedData = storedData.scrapedData;
      _lastScrapedTimestamp = storedData.timestamp || '';

      // Rebuild the element map for duplicate detection
      _scrapedData.forEach(element => {
        if (element.xpath) {
          _elementMap.set(element.xpath, element);
        }
      });

      console.log(`🔄 Restored ${_scrapedData.length} scraped elements from storage after page refresh`);

      // Re-highlight the previously scraped elements
      _scrapedData.forEach(element => {
        if (element.xpath) {
          try {
            const elementNode = document.evaluate(
              element.xpath,
              document,
              null,
              XPathResult.FIRST_ORDERED_NODE_TYPE,
              null
            ).singleNodeValue as HTMLElement;

            if (elementNode) {
              addPersistentHighlightWithoutBlocking(elementNode);
            }
          } catch (error) {
            // Element might not exist anymore, that's okay
          }
        }
      });
    }
  } catch (error) {
    console.log('No previous scraped data found or error loading from storage');
  }

  // Check if we're in a Chrome extension environment
  if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onMessage) {
    // Listen for messages from background script
    chrome.runtime.onMessage.addListener((message, _sender, sendResponse) => {
      if (message.action === 'updateScrapingState') {
        _isScrapingActive = message.isActive;
        sendResponse({ success: true });
        return true;
      }

      if (message.action === 'getScrapingState') {
        sendResponse({
          isActive: _isScrapingActive,
          lastTimestamp: _lastScrapedTimestamp,
          elementCount: _scrapedData.length
        });
        return true;
      }

      if (message.action === 'getScrapedData') {
        sendResponse({
          data: _scrapedData,
          timestamp: _lastScrapedTimestamp
        });
        return true;
      }

      if (message.action === 'clearScrapedData') {
        clearScrapedData();
        sendResponse({ success: true });
        return true;
      }
    });
  } else {
  }
};


// Initialize the service
initScrapingService().catch(error => {
  console.error('Error initializing scraping service:', error);
});

/**
 * Utility to extract only essential data from a scraped element object
 * Includes fallback identification mechanisms
 */
export function extractMinimalScrapeData(element: any): {
  xpath: string,
  labelName: string,
  cssSelector: string,
  selector: string,
  value: any,
  text: string,
  id: string
} {
  return {
    xpath: element.xpath || '',
    labelName: element.labelName || element.text || '',
    cssSelector: element.cssSelector || '',
    selector: element.selector || element.xpath || element.cssSelector || '',
    value: element.value,
    text: element.text || '',
    id: element.id || '',
  };
}
