.qadpt-container {
  width: 100%;
  left: 0;
  position: fixed;
  z-index: 999999 !important;
  top: 0;
}
.qadpt-container.creation{
  z-index: 99999 !important;
}

.qadpt-container .qadpt-box {
    position: relative;
    display: flex;
    flex-direction: column;
     background-color: #f1f1f7; 
    z-index: 99;
    top: 55px;
}
.qadpt-container .qadpt-boxpre {
  position: relative;
  display: flex;
  flex-direction: column;
   background-color: #f1f1f7; 
  z-index: 99;
}

.qadpt-container .qadpt-box .qadpt-row {
    display: flex;
    position: relative;
    place-content: end;
    align-items: center;
}
.qadpt-container .qadpt-box .qadpt-row .qadpt-text-area-wrapper {
  position: relative;
  flex-grow: 1;
  /* padding: 5px; */
  display: flex;
  align-items: center;
}
.qadpt-container .qadpt-box .qadpt-row .qadpt-text-area-wrapper .qadpt-text-area {
    height: auto;
     border-radius: 5px;
    font-size: 16px;
   /* border: 1px solid #ccc; */
    box-sizing: border-box;
    width: calc(100% - 10px);
}
.qadpt-container .qadpt-box .qadpt-row .qadpt-text-area-wrapper .qadpt-text-area textarea {
    width: 100%;
    height: 40px;
    border-radius: 5px;
    border: 0 !important;
    padding: 0 !important;
    resize: none;
}
.qadpt-container .qadpt-box .qadpt-row .qadpt-text-area-wrapper .qadpt-text-area .qadpt-emoji-button {
    position: absolute;
    right: 25px;
}
/* .qadpt-container .qadpt-box .qadpt-row .qadpt-text-area-wrapper .qadpt-delete-button {
    position: absolute;
    top: 10px;
    left: auto;
    right: -13px;
}
.qadpt-container .qadpt-box .qadpt-row .qadpt-text-area-wrapper .qadpt-delete-button svg{
  height: 21px;
  width: 21px;
} */
.qadpt-container .qadpt-box .qadpt-row .qadpt-add-btn {
  height: 25px;
  width: 30px;
  /*margin: 10px 0;*/
}

.qadpt-container .qadpt-box .qadpt-row .qadpt-add-btn svg{
  height: 21px;
  width: 21px;
}
.qadpt-container .qadpt-box .qadpt-row .qadpt-text-area-wrapper .qadpt-text-area .qadpt-rte h1,
.qadpt-container .qadpt-box .qadpt-row .qadpt-text-area-wrapper .qadpt-text-area .qadpt-rte h2,
.qadpt-container .qadpt-box .qadpt-row .qadpt-text-area-wrapper .qadpt-text-area .qadpt-rte h3 {
  margin: 0 !important;
}
.qadpt-container .qadpt-box .qadpt-row .qadpt-text-area-wrapper .qadpt-text-area .qadpt-rte ol,
.qadpt-container .qadpt-box .qadpt-row .qadpt-text-area-wrapper .qadpt-text-area .qadpt-rte ul{
  margin: 0 !important;
}


/* .qadpt-container .qadpt-box .qadpt-add-button {
    position: relative;
    left: 20px;
    top: 20px;
} */
.qadpt-container .qadpt-box .qadpt-options-menu {
    position: absolute;
    top: 108%;
    right: 25px;
    background-color: #fff;
    box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.2);
    padding: 10px;
    border-radius: 5px;
    flex-direction: column;
    gap: 10px;
    /* width: 100px; */
    z-index: 999;
}
.qadpt-container .qadpt-box .qadpt-options-menu .qadpt-options-content {
    display: flex;
    flex-direction: row;
    gap: 16px;
}
.qadpt-container .qadpt-box .qadpt-options-menu .qadpt-options-content .qadpt-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
}



/* start */
.qadpt-imageupload {
    width: 100%;
    height: auto;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}
.qadpt-imageupload img {
    width: 100%;
    height: 40px;
    object-fit: cover;
}
.qadpt-imageupload .upload-container {
    display: flex;
    align-items: center;
    gap: 15px;
}
.qadpt-imageupload .icon-text {
    display: flex;
    flex-direction: row;
    width: 100%;
    gap : 10px
}

.qadpt-imageupload .icon-text h6 {
    text-align: center;
}
.qadpt-imageupload .icon-row {
    display: flex;
    align-items: center;
    gap: 10px;
  
}
.qadpt-imageupload .icon-row span {
    cursor: pointer;
}
.qadpt-imageupload .icon-row svg{
    height: 30px;
    width: 30px;
}
.qadpt-imageupload input[type="file"] {
    display: none;
}

/* image popup */
.qadpt-imagepopup {
    height: 44px;
    width: 500px;
    margin-left: auto;
    margin-right: auto;
    margin-top: 65px !important; /* Use caution with !important */
  }
  
  .qadpt-imagepopup .qadpt-imagepopup-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    padding: 0 10px;
  
  }
  
  .qadpt-imagepopup .qadpt-imagepopup-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    cursor: pointer;
  }
  
  .qadpt-imagepopup .qadpt-imagepopup-text {
    font-size: 12px;
  }

  .qadpt-imagepopup .qadpt-imagepopup-upload {
    display: none;
  }
  /* imagepopup end */


  .qadpt-imggal {
    border-radius: 10px;
    padding: 10px;
  
  }
  .qadpt-imggal .MuiDialog-container .MuiPaper-root{
    width: 205px;
    top: 125px;
  }
  .qadpt-imggal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
  }
  .qadpt-imggal-backicon, .qadpt-imggal-closeicon {
    color: #333;
  }
  .qadpt-imggal-title {
    flex-grow: 1;
    text-align: center;
  }
  .qadpt-imggal-search {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px 8px;
  }
  /* .qadpt-imggal-searchfield {
    flex-grow: 1;
    margin-right: 8px;
  } */
  /* .qadpt-imggal-searchicon, .qadpt-imggal-filtericon {
    color: #666;
  } */
  .qadpt-imggal-images {
    padding: 16px;
  }
  .qadpt-imggal-imageitem {
    cursor: pointer;
    text-align: center;
  }
  .qadpt-imggal-imageitem img {
    width: 100%;
    border-radius: 5px;
  }
  .qadpt-imggal-imagetitle {
    font-size: 14px;
    margin-top: 4px;
  }
  .qadpt-imggal-imagesize {
    font-size: 12px;
    color: #888;
  }

  .qadpt-prop-section{
    margin-top: 10px;
  }
  .qadpt-prop-section .qadpt-actions{
    border: none;
    width: 100%;
    height: 40px;
    background: var(--ext-background);
  }
  .qadpt-prop-section button{
    background: var(--ext-background);
    width: 50%;
    border-radius: 4px;
    color: #000;
    text-transform: capitalize;
    border: none;
  }
    /* image area end */
  