import React, { createContext, useState, ReactNode, useEffect, useRef } from 'react';

// Create the context
export const AccountContext = createContext<{
  accountId: string;
  setAccountId: (id: string) => void;
  roles: string[];
  setRoles: (roles: [])=>void;
}>({
  accountId: '',      // Default value
  setAccountId: () => { },  // Empty function as a placeholder
  roles: [],
  setRoles: () =>[],
});

// Provider component
export const AccountProvider = ({ children }: { children: ReactNode }) => {
  const [roles, setRoles] = useState<string[]>([]); 
  // Get the default accountId from localStorage
  const getDefaultAccountId = () => localStorage.getItem("accountId") || "";
  const [accountId, setAccountId] = useState<string>(getDefaultAccountId());
  const originalAccountIdRef = useRef<string | null>(null);

  useEffect(() => {

    // On mount, check for qa_account_id in the URL

    const params = new URLSearchParams(window.location.search);
    
    const overrideAccountId = params.get('qa_account_id');
    const defaultAccountId = getDefaultAccountId();
    
    // --- Store intended URL if special param is present and not logged in ---
    const guideId = params.get('quickadopt_guide_id');
    const isLoggedIn = !!localStorage.getItem('access_token');
    if ((overrideAccountId || guideId) && !isLoggedIn) {
      sessionStorage.setItem('postLoginRedirect', window.location.href);
    }
    
    if (overrideAccountId && overrideAccountId !== defaultAccountId) {
      // Cache the original accountId (if not already cached)
      if (originalAccountIdRef.current === null) {
        originalAccountIdRef.current = defaultAccountId;
      }
      setAccountId(overrideAccountId);
      // Store override in localStorage for later use
      localStorage.setItem('qa_account_id_override', overrideAccountId);
      // Remove qa_account_id from the URL after use
      params.delete('qa_account_id');
      const newUrl = `${window.location.pathname}${params.toString() ? `?${params.toString()}` : ''}${window.location.hash}`;
      window.history.replaceState({}, '', newUrl);
    } else {
      setAccountId(defaultAccountId);
      // Remove any previous override if not using it
      localStorage.removeItem('qa_account_id_override');
    }
    // On unload, restore the original accountId and clean up override
    const handleUnload = () => {
      if (originalAccountIdRef.current !== null) {
        setAccountId(originalAccountIdRef.current);
        originalAccountIdRef.current = null;
      }
      localStorage.removeItem('qa_account_id_override');
    };
    window.addEventListener('beforeunload', handleUnload);
    return () => {
      window.removeEventListener('beforeunload', handleUnload);
      // Also restore on unmount
      handleUnload();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);


  return (
    <AccountContext.Provider value={{ accountId, setAccountId,roles,setRoles }}>
      {children}
    </AccountContext.Provider>
  );
};
