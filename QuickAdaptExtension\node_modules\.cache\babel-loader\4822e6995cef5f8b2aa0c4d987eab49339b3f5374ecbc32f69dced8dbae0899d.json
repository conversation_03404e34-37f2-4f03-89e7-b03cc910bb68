{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideBanners\\\\selectedpopupfields\\\\ImageProperties.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { Typo<PERSON>, <PERSON>con<PERSON><PERSON><PERSON>, Button } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport \"../../guideDesign/Canvas.module.scss\";\n// import Draggable from \"react-draggable\";\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\nimport \"../guideBanner.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ButtonSettings = () => {\n  _s();\n  const [borderColor, setBorderColor] = useState(\"#000000\");\n  const [backgroundColor, setBackgroundColor] = useState(\"#FFFFFF\");\n  const [isOpen, setIsOpen] = useState(true);\n  const [selectedPosition, setSelectedPosition] = useState(\"\");\n  const [url, setUrl] = useState(\"\");\n  const [action, setAction] = useState(\"close\");\n  const [openInNewTab, setOpenInNewTab] = useState(true);\n  const [colors, setColors] = useState({\n    fill: \"#4CAF50\",\n    border: \"#4CAF50\",\n    text: \"#ffffff\"\n  });\n  const handleClose = () => {\n    setIsOpen(false);\n  };\n  if (!isOpen) return null;\n  const handleColorChange = (type, color) => {\n    setColors(prevColors => ({\n      ...prevColors,\n      [type]: color\n    }));\n  };\n  return (\n    /*#__PURE__*/\n    //<Draggable>\n    _jsxDEV(\"div\", {\n      className: \"qadpt-designpopup qadpt-imgset\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-design-header\",\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            \"aria-label\": \"close\",\n            onClick: handleClose,\n            children: /*#__PURE__*/_jsxDEV(ArrowBackIosNewOutlinedIcon, {\n              className: \"qadpt-design-back\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 7\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 6\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-title\",\n            children: \"Image Properties\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 6\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            \"aria-label\": \"close\",\n            onClick: handleClose,\n            children: /*#__PURE__*/_jsxDEV(CloseIcon, {\n              className: \"qadpt-design-close\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 7\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 6\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 5\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-prop-section\",\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            sx: {\n              marginBottom: \"10px\"\n            },\n            children: \"Image Actions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 6\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            id: \"action\",\n            value: action,\n            className: \"qadpt-actions\",\n            onChange: e => setAction(e.target.value),\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Open URL\",\n              children: \"None\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 7\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"Another Action\",\n              children: \"new\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 7\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 6\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 5\n        }, this), \"Image Formatting\", /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-prop-section\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setOpenInNewTab(true),\n            style: {\n              border: \"1px solid var(--Theme-accentColor)\"\n            },\n            children: \"Fill\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 6\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setOpenInNewTab(true),\n            style: {\n              border: \"1px solid var(--Theme-accentColor)\"\n            },\n            children: \"Fit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 6\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 5\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 4\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 3\n    }, this)\n    //</Draggable>\n  );\n};\n_s(ButtonSettings, \"rTzWhCXoyXKhkNtTJSk5kPyUEZU=\");\n_c = ButtonSettings;\nexport default ButtonSettings;\nvar _c;\n$RefreshReg$(_c, \"ButtonSettings\");", "map": {"version": 3, "names": ["React", "useState", "Typography", "IconButton", "<PERSON><PERSON>", "CloseIcon", "ArrowBackIosNewOutlinedIcon", "jsxDEV", "_jsxDEV", "ButtonSettings", "_s", "borderColor", "setBorderColor", "backgroundColor", "setBackgroundColor", "isOpen", "setIsOpen", "selectedPosition", "setSelectedPosition", "url", "setUrl", "action", "setAction", "openInNewTab", "setOpenInNewTab", "colors", "setColors", "fill", "border", "text", "handleClose", "handleColorChange", "type", "color", "prevColors", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "sx", "marginBottom", "id", "value", "onChange", "e", "target", "style", "_c", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/guideBanners/selectedpopupfields/ImageProperties.tsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { <PERSON>, <PERSON>po<PERSON>, <PERSON><PERSON>ield, Grid, IconButton, Button } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport \"../../guideDesign/Canvas.module.scss\";\r\n// import Draggable from \"react-draggable\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport \"../guideBanner.css\";\r\nconst ButtonSettings = () => {\r\n\tconst [borderColor, setBorderColor] = useState(\"#000000\");\r\n\tconst [backgroundColor, setBackgroundColor] = useState(\"#FFFFFF\");\r\n\tconst [isOpen, setIsOpen] = useState(true);\r\n\tconst [selectedPosition, setSelectedPosition] = useState(\"\");\r\n\tconst [url, setUrl] = useState(\"\");\r\n\tconst [action, setAction] = useState(\"close\");\r\n\tconst [openInNewTab, setOpenInNewTab] = useState(true);\r\n\tconst [colors, setColors] = useState({\r\n\t\tfill: \"#4CAF50\",\r\n\t\tborder: \"#4CAF50\",\r\n\t\ttext: \"#ffffff\",\r\n\t});\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetIsOpen(false);\r\n\t};\r\n\r\n\tif (!isOpen) return null;\r\n\r\n\tconst handleColorChange = (type: any, color: any) => {\r\n\t\tsetColors((prevColors) => ({ ...prevColors, [type]: color }));\r\n\t};\r\n\r\n\treturn (\r\n\t\t//<Draggable>\r\n\t\t<div className=\"qadpt-designpopup qadpt-imgset\">\r\n\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<ArrowBackIosNewOutlinedIcon  className=\"qadpt-design-back\"/>\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t<div className=\"qadpt-title\">Image Properties</div>\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<CloseIcon className=\"qadpt-design-close\"/>\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div className=\"qadpt-prop-section\">\r\n\t\t\t\t\t<Typography\r\n\t\t\t\t\t\tsx={{ marginBottom: \"10px\" }}\r\n\t\t\t\t\t>Image Actions</Typography>\r\n\t\t\t\t\t<select\r\n\t\t\t\t\t\tid=\"action\"\r\n\t\t\t\t\t\tvalue={action}\r\n\t\t\t\t\t\tclassName=\"qadpt-actions\"\r\n\t\t\t\t\t\tonChange={(e) => setAction(e.target.value)}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<option value=\"Open URL\">None</option>\r\n\t\t\t\t\t\t<option value=\"Another Action\">new</option>\r\n\t\t\t\t\t</select>\r\n\t\t\t\t</div>\r\n\t\t\t\tImage Formatting\r\n\t\t\t\t<div className=\"qadpt-prop-section\">\r\n\t\t\t\t\t{/* <button\r\n\t\t\t\t\t\tclassName={`tab-btn ${openInNewTab ? \"active\" : \"\"}`}\r\n\t\t\t\t\t\tonClick={() => setOpenInNewTab(true)}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\tNew Tab\r\n\t\t\t\t\t</button> */}\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tonClick={() => setOpenInNewTab(true)}\r\n\t\t\t\t\t\tstyle={{ border: \"1px solid var(--Theme-accentColor)\" }}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\tFill\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tonClick={() => setOpenInNewTab(true)}\r\n\t\t\t\t\t\tstyle={{ border: \"1px solid var(--Theme-accentColor)\" }}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\tFit\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t</div>\r\n\t\t\t\t{/* <div className=\"properties-section\">\r\n\t\t\t\t\t<Typography>Enter URL</Typography>\r\n\t\t\t\t\t<input\r\n\t\t\t\t\t\ttype=\"text\"\r\n\t\t\t\t\t\tid=\"url\"\r\n\t\t\t\t\t\tplaceholder=\"http://www.example.com\"\r\n\t\t\t\t\t\tvalue={url}\r\n\t\t\t\t\t\tonChange={(e) => setUrl(e.target.value)}\r\n\t\t\t\t\t/>\r\n\t\t\t\t</div> */}\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t//</Draggable>\r\n\t);\r\n};\r\n\r\nexport default ButtonSettings;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAAcC,UAAU,EAAmBC,UAAU,EAAEC,MAAM,QAAQ,eAAe;AACpF,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAO,sCAAsC;AAC7C;AACA,OAAOC,2BAA2B,MAAM,6CAA6C;AACrF,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAC5B,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGX,QAAQ,CAAC,SAAS,CAAC;EACzD,MAAM,CAACY,eAAe,EAAEC,kBAAkB,CAAC,GAAGb,QAAQ,CAAC,SAAS,CAAC;EACjE,MAAM,CAACc,MAAM,EAAEC,SAAS,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACgB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACkB,GAAG,EAAEC,MAAM,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAClC,MAAM,CAACoB,MAAM,EAAEC,SAAS,CAAC,GAAGrB,QAAQ,CAAC,OAAO,CAAC;EAC7C,MAAM,CAACsB,YAAY,EAAEC,eAAe,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACwB,MAAM,EAAEC,SAAS,CAAC,GAAGzB,QAAQ,CAAC;IACpC0B,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,SAAS;IACjBC,IAAI,EAAE;EACP,CAAC,CAAC;EAEF,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACzBd,SAAS,CAAC,KAAK,CAAC;EACjB,CAAC;EAED,IAAI,CAACD,MAAM,EAAE,OAAO,IAAI;EAExB,MAAMgB,iBAAiB,GAAGA,CAACC,IAAS,EAAEC,KAAU,KAAK;IACpDP,SAAS,CAAEQ,UAAU,KAAM;MAAE,GAAGA,UAAU;MAAE,CAACF,IAAI,GAAGC;IAAM,CAAC,CAAC,CAAC;EAC9D,CAAC;EAED;IAAA;IACC;IACAzB,OAAA;MAAK2B,SAAS,EAAC,gCAAgC;MAAAC,QAAA,eAC9C5B,OAAA;QAAK2B,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC7B5B,OAAA;UAAK2B,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBACnC5B,OAAA,CAACL,UAAU;YACV,cAAW,OAAO;YAClBkC,OAAO,EAAEP,WAAY;YAAAM,QAAA,eAErB5B,OAAA,CAACF,2BAA2B;cAAE6B,SAAS,EAAC;YAAmB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACbjC,OAAA;YAAK2B,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnDjC,OAAA,CAACL,UAAU;YACVuC,IAAI,EAAC,OAAO;YACZ,cAAW,OAAO;YAClBL,OAAO,EAAEP,WAAY;YAAAM,QAAA,eAErB5B,OAAA,CAACH,SAAS;cAAC8B,SAAS,EAAC;YAAoB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACNjC,OAAA;UAAK2B,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBAClC5B,OAAA,CAACN,UAAU;YACVyC,EAAE,EAAE;cAAEC,YAAY,EAAE;YAAO,CAAE;YAAAR,QAAA,EAC7B;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC3BjC,OAAA;YACCqC,EAAE,EAAC,QAAQ;YACXC,KAAK,EAAEzB,MAAO;YACdc,SAAS,EAAC,eAAe;YACzBY,QAAQ,EAAGC,CAAC,IAAK1B,SAAS,CAAC0B,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAAAV,QAAA,gBAE3C5B,OAAA;cAAQsC,KAAK,EAAC,UAAU;cAAAV,QAAA,EAAC;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtCjC,OAAA;cAAQsC,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAAG;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,oBAEN,eAAAjC,OAAA;UAAK2B,SAAS,EAAC,oBAAoB;UAAAC,QAAA,gBAOlC5B,OAAA,CAACJ,MAAM;YACNiC,OAAO,EAAEA,CAAA,KAAMb,eAAe,CAAC,IAAI,CAAE;YACrC0B,KAAK,EAAE;cAAEtB,MAAM,EAAE;YAAqC,CAAE;YAAAQ,QAAA,EACxD;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTjC,OAAA,CAACJ,MAAM;YACNiC,OAAO,EAAEA,CAAA,KAAMb,eAAe,CAAC,IAAI,CAAE;YACrC0B,KAAK,EAAE;cAAEtB,MAAM,EAAE;YAAqC,CAAE;YAAAQ,QAAA,EACxD;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAWF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;IACL;EAAA;AAEF,CAAC;AAAC/B,EAAA,CA7FID,cAAc;AAAA0C,EAAA,GAAd1C,cAAc;AA+FpB,eAAeA,cAAc;AAAC,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}