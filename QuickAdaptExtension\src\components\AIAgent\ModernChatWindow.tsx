import React, { useState, useRef, useEffect, useContext } from 'react';
import './ModernChatWindow.css';
import { AccountContext } from '../../components/login/AccountContext';
import { ai, airobot, micicon, micicon_hover, upload, upload_hover, send } from "../../assets/icons/icons";
import CloseIcon from '@mui/icons-material/Close';
// Import services
import { CreateInteraction } from '../../services/AIService';
import { startSpeechRecognition, stopSpeechRecognition, isSpeechRecognitionSupported } from '../../services/SpeechRecognitionService';
import useDrawerStore, { DrawerState } from '../../store/drawerStore';
import PerfectScrollbar from 'react-perfect-scrollbar';
import 'react-perfect-scrollbar/dist/css/styles.css';
import { IsOpenAIKeyEnabledForAccount } from '../../services/GuideListServices';
import { useSnackbar } from '../guideSetting/guideList/SnackbarContext';
import { useTranslation } from 'react-i18next';



interface ModernChatWindowProps {
    onClose: () => void;
    setIsPopupOpen: any;
    setCurrentGuideId: any;
    setIsLoggedIn: any;
    setIsTourPopupOpen: any;
    setIsDrawerClosed: any;
    setShowBannerenduser: any;
  setIsAIChatOpen?: any; // Make this optional
  setStepData?: any; // Optional function to set step data
  updatedGuideData?: any;
}

interface ChatMessage {
  text: string;
  isUser: boolean;
  timestamp: Date;
}

// Chat message type

const ModernChatWindow: React.FC<ModernChatWindowProps> = ({ onClose,updatedGuideData, setStepData, setIsPopupOpen, setCurrentGuideId, setIsLoggedIn, setIsTourPopupOpen, setIsDrawerClosed, setShowBannerenduser, setIsAIChatOpen }) => {
  const { t: translate } = useTranslation();
    // Extract all needed functions from the drawer store
    const {
		setSelectedTemplate,
		setSelectedTemplateTour,
		setSteps,
		SetGuideName,
		setBannerPopup,
		setCurrentStep,
		TooltipGuideDetails,
		HotspotGuideDetails,
		setTooltipCount,
		tooltipCount,
		setElementSelected,
      setSelectedStepTypeHotspot,
      setCreateWithAI,
      setIsAIGuidePersisted,
      setInteractionData,
      generateSteps,
      setBannerButtonSelected,
      syncAITooltipDataForPreview,
      syncAIAnnouncementDataForPreview,
      setIsUnSavedChanges,
      setOpenWarning
	} = useDrawerStore((state: DrawerState) => state);
    const [isChatOpen, setIsChatOpen] = useState(false);  // New state for chat visibility
    const { accountId } = useContext(AccountContext);
    const [inputText, setInputText] = useState('');
    const [messages, setMessages] = useState<ChatMessage[]>([
        {
        text: translate("Hello There! Need an banner, tour, or anything else to engage your users? Let me know, and I’ll get it done fast!"),
        isUser: false,
        timestamp: new Date()
        }
    ]);
  const [isLoading, setIsLoading] = useState(false);
  // State for UI and error handling
  const [error, setError] = useState<string | null>(null);
  const [isMicHovered, setIsMicHovered] = useState(false);
  const [isUploadHovered, setIsUploadHovered] = useState(false);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [speechSupported, setSpeechSupported] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const [isOpenAIKeyProvided, setIsOpenAIKeyProvided] = useState(true);
  const { openSnackbar } = useSnackbar();

  useEffect(() => {
    if (chatContainerRef.current) {
      // Scroll to the bottom of the chat container
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }

    setShowScrollButton(false);
  }, [messages, isLoading]);


  const handleScroll = () => {
    if (chatContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = chatContainerRef.current;

      // Show scroll button when not at the bottom
      const isAtBottom = scrollHeight - scrollTop - clientHeight < 20;
      setShowScrollButton(!isAtBottom);
    }
  };

  const scrollToBottom = () => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
      setShowScrollButton(false);
    }
  };

  // Focus input on component mount
  useEffect(() => {
    inputRef.current?.focus();
    IsOpenAIKeyEnabledForAccount(openSnackbar,accountId,setIsOpenAIKeyProvided);
  }, []);

  // Check if speech recognition is supported
  useEffect(() => {
    setSpeechSupported(isSpeechRecognitionSupported());
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputText(e.target.value);
    setError(null);

    // Auto-resize the textarea
    if (inputRef.current) {
      inputRef.current.style.height = 'auto'; // Reset height first
      inputRef.current.style.height = `${Math.min(inputRef.current.scrollHeight, 80)}px`; // Grow to scrollHeight, max 100px
    }
  };
  useEffect(() => {
    if (inputRef.current && inputText === '') {
      inputRef.current.style.height = '45px';
    }
  }, [inputText]);


  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey && isOpenAIKeyProvided) {
      e.preventDefault();
      handleCreateInteraction(inputText);
    }
  };

  // Check if browser is Microsoft Edge
  const isEdgeBrowser = (): boolean => {
    return navigator.userAgent.indexOf("Edg") !== -1;
  };

  // Handle speech recognition
  const handleSpeechRecognition = () => {
    if (isListening) {
      stopSpeechRecognition();
      setIsListening(false);
      return;
    }

    // Get the current text in the input field
    const initialText = inputText.trim();

    // Show Edge-specific message if needed
    if (isEdgeBrowser() && !speechSupported) {
      setError(translate("Speech recognition may not work in this version of Edge. Try using Chrome for better results."));
      setTimeout(() => setError(null), 5000);
    }

    setIsListening(true);
    startSpeechRecognition({
      pauseDuration: 5000, // 5 seconds pause before stopping
      onStart: () => {
        setIsListening(true);
        setError(null); // Clear any previous errors
      },
      onResult: (text: string, isFinal: boolean) => {
        // The text parameter now contains the full accumulated transcript
        // If there was already text in the input field, append the speech recognition result
        // Otherwise, just set the text directly
        if (initialText) {
          setInputText(initialText + ' ' + text);
        } else {
          setInputText(text);
        }
      },
      onEnd: () => {
        setIsListening(false);
      },
      onError: (error: any) => {
        console.error("Speech recognition error:", error);
        setIsListening(false);

        // Provide more helpful error messages
        if (isEdgeBrowser()) {
          if (error === 'not-allowed') {
            setError(translate("Microphone access denied. Please allow microphone access in your browser settings."));
          } else if (error === 'network') {
            setError(translate("Network error occurred. Speech recognition might not work well in Edge. Try Chrome for better results."));
          } else {
            setError(`${translate("Speech recognition failed in Edge:")} ${error}. ${translate("Try using Chrome for better results.")}`);
          }
        } else {
          setError(`${translate("Speech recognition failed:")} ${error}. ${translate("Please try again.")}`);
        }

        // Clear error after 5 seconds
        setTimeout(() => setError(null), 5000);
      }
    });
  };

  // Removed unused test function

//   const handleSendMessage = async () => {
//     if (!inputText.trim()) return;

//     // Add user message to chat
//     const userMessage: ChatMessage = {
//       text: inputText,
//       isUser: true,
//       timestamp: new Date()
//     };

//     setMessages(prev => [...prev, userMessage]);
//     setInputText('');
//     setIsLoading(true);

//     // Reset textarea height
//     if (inputRef.current) {
//       inputRef.current.style.height = 'auto';
//     }

//     try {
//       // For testing purposes, if the message is "test", add multiple messages
//       if (inputText.toLowerCase() === "test") {
//         addTestMessages();
//         return;
//       }

//       // Add user message to conversation history
//       const updatedConversation = [...conversation, { role: 'user' as const, content: inputText }];
//       setConversation(updatedConversation);

//       // Call OpenAI API
//       const response = await callOpenAI(updatedConversation);

//       // Add AI response to chat
//       const aiResponse: ChatMessage = {
//         text: response,
//         isUser: false,
//         timestamp: new Date()
//       };

//       // Add AI response to conversation history
//       setConversation([...updatedConversation, { role: 'assistant' as const, content: response }]);

//       // Update UI with AI response
//       setMessages(prev => [...prev, aiResponse]);
//       setIsLoading(false);

//     } catch (err) {
//       console.error("Error creating interaction:", err);
//       setIsLoading(false);

//       // Show error message in chat
//       const errorMessage: ChatMessage = {
//         text: "Sorry, I encountered an error while processing your request.",
//         isUser: false,
//         timestamp: new Date()
//       };

//       setMessages(prev => [...prev, errorMessage]);
//     }
//   };
  // Function to check if the user prompt matches any of the tour creation patterns
  const isTourCreationPrompt = (prompt: string): boolean => {
    const tourPatterns = [
      /create\s+a\s+tour\s+with\s+steps\s*:/i,
      /generate\s+a\s+tour\s+with\s*:/i,
      /create\s+a\s+user\s+onboarding\s+tour\s+with/i,
      /make\s+a\s+training\s+tour\s+with/i,
      /create\s+a\s+tour/i,
      /generate\s+a\s+tour/i,
      /create\s+tour\s+with\s+step/i,
      /create\s+tour\s+having\s+step/i
    ];

    return tourPatterns.some(pattern => pattern.test(prompt));
  };

  // Function to parse the user prompt and determine the steps to include in the tour
  const parseTourSteps = (prompt: string): { stepType: string, title?: string, content?: string, buttonName?: string, buttonAction?: string, buttonActionValue?: string }[] => {
    const steps: { stepType: string, title?: string, content?: string, buttonName?: string, buttonAction?: string, buttonActionValue?: string }[] = [];

    // Check for specific step patterns with stepType explicitly mentioned
    // First pattern: step1 as stepType having "Announcement"
    const stepTypePattern1 = /step\s*(\d+)\s*(?:as|having)\s*(?:stepType|step type)\s*(?:as|having|with)?\s*["']?([a-zA-Z]+)["']?/gi;
    // Second pattern: step1 as announcement
    const stepTypePattern2 = /step\s*(\d+)\s*(?:as|having|with)\s*["']?([a-zA-Z]+)["']?/gi;

    let match;

    // Try the first pattern
    while ((match = stepTypePattern1.exec(prompt)) !== null) {
      const stepNumber = parseInt(match[1]);
      let stepType = match[2].trim();

      // Normalize step type (handle misspellings like "Annoucement")
      if (stepType.toLowerCase().includes("announ") || stepType.toLowerCase().includes("annoucement")) {
        stepType = "Announcement";
      } else if (stepType.toLowerCase().includes("bann")) {
        stepType = "Banner";
      } else if (stepType.toLowerCase().includes("tool")) {
        stepType = "Tooltip";
      } else if (stepType.toLowerCase().includes("hot")) {
        stepType = "Hotspot";
      } else {
        // Default to Announcement if type is not recognized
        stepType = "Announcement";
      }

      // Ensure first letter is capitalized
      stepType = stepType.charAt(0).toUpperCase() + stepType.slice(1);

      steps.push({
        stepType: stepType,
        title: `Step ${stepNumber}`,
        content: `<p>step ${stepNumber}</p>`
      });
    }

    // If no matches found with the first pattern, try the second pattern
    if (steps.length === 0) {
      while ((match = stepTypePattern2.exec(prompt)) !== null) {
        const stepNumber = parseInt(match[1]);
        let stepType = match[2].trim();

        // Normalize step type (handle misspellings like "Annoucement")
        if (stepType.toLowerCase().includes("announ") || stepType.toLowerCase().includes("annoucement")) {
          stepType = "Announcement";
        } else if (stepType.toLowerCase().includes("bann")) {
          stepType = "Banner";
        } else if (stepType.toLowerCase().includes("tool")) {
          stepType = "Tooltip";
        } else if (stepType.toLowerCase().includes("hot")) {
          stepType = "Hotspot";
        } else {
          // Default to Announcement if type is not recognized
          stepType = "Announcement";
        }

        // Ensure first letter is capitalized
        stepType = stepType.charAt(0).toUpperCase() + stepType.slice(1);

        steps.push({
          stepType: stepType,
          title: `Step ${stepNumber}`,
          content: `<p>step ${stepNumber}</p>`
        });
      }
    }

    // Check for specific step patterns like "1. Announcement titled 'Welcome!'"
    if (steps.length === 0) {
      const numberedStepPattern = /(\d+)\s*\.\s*(announcement|banner|tooltip|hotspot)\s*(?:titled|saying)?\s*['"]?([^'"]+)['"]?/gi;

      while ((match = numberedStepPattern.exec(prompt)) !== null) {
        steps.push({
          stepType: match[2].charAt(0).toUpperCase() + match[2].slice(1),
          title: match[3].trim(),
          content: `<p>${match[3].trim()}</p>`
        });
      }
    }

    // Check for button properties and text content
    const buttonPattern = /step\s*(\d+).*?button\s*(?:as|having)\s*buttonName\s*(?:as|having|with)?\s*["']([^"']+)["']\s*and\s*button\s*actions\s*as\s*([a-zA-Z]+)\s*and\s*actionvalue\s*as\s*["']?([^"']+)["']?/gi;
    let buttonMatch;
    while ((buttonMatch = buttonPattern.exec(prompt)) !== null) {
      const stepNumber = parseInt(buttonMatch[1]);
      const buttonName = buttonMatch[2].trim();
      const buttonAction = buttonMatch[3].trim().toLowerCase();
      const buttonActionValue = buttonMatch[4].trim();

      // Find the step with this number
      const stepIndex = steps.findIndex(step => {
        const stepTitle = step.title || '';
        return stepTitle.includes(`${stepNumber}`);
      });

      if (stepIndex !== -1) {
        // Update existing step with button properties
        steps[stepIndex].buttonName = buttonName;
        steps[stepIndex].buttonAction = buttonAction;
        steps[stepIndex].buttonActionValue = buttonActionValue;
      }
    }

    // Check for text content
    const textPattern = /step\s*(\d+).*?(?:with|having)\s*text\s*(?:as|having|with)?\s*["']([^"']+)["']/gi;
    let textMatch;
    while ((textMatch = textPattern.exec(prompt)) !== null) {
      const stepNumber = parseInt(textMatch[1]);
      const textContent = textMatch[2].trim();

      // Find the step with this number
      const stepIndex = steps.findIndex(step => {
        const stepTitle = step.title || '';
        return stepTitle.includes(`${stepNumber}`);
      });

      if (stepIndex !== -1) {
        // Update existing step with text content
        steps[stepIndex].content = `<p>${textContent}</p>`;
      }
    }

    // If no specific steps found, check for general patterns
    if (steps.length === 0) {
      // Check for "Announcement, Banner, Announcement" pattern
      const stepTypesPattern = /(announcement|banner|tooltip|hotspot)(?:\s*,\s*|\s+and\s+|\s+)/gi;
      const stepTypes: string[] = [];

      while ((match = stepTypesPattern.exec(prompt)) !== null) {
        stepTypes.push(match[1].charAt(0).toUpperCase() + match[1].slice(1));
      }

      if (stepTypes.length > 0) {
        stepTypes.forEach((type, index) => {
          steps.push({
            stepType: type,
            title: `Step ${index + 1}`,
            content: `<p>step ${index + 1}</p>`
          });
        });
      } else {
        // Default to 3 steps: Announcement, Banner, Announcement
        steps.push(
          { stepType: "Announcement", title: "Step 1", content: "<p>step1</p>" },
          { stepType: "Banner", title: "Step 2", content: "<p>step 2 banner</p>" },
          { stepType: "Announcement", title: "Step 3", content: "<p>step 3</p>" }
        );
      }
    }

    // Sort steps by step number if they have numeric titles
    steps.sort((a, b) => {
      const aNum = a.title ? parseInt(a.title.replace(/\D/g, '')) : 0;
      const bNum = b.title ? parseInt(b.title.replace(/\D/g, '')) : 0;
      return aNum - bNum;
    });

    return steps;
  };


  const [dataNew, setDataNew] = useState<any>();
  const[stepDataNew,setStepDataNew]=useState<any>();
  useEffect(() =>
  {
      setElementSelected(false);

},[])
  const handleCreateInteraction = async (value: string) => {
    setIsUnSavedChanges(true);
    setError(null);
    setCreateWithAI(true);
    setIsAIGuidePersisted(false);
    if (value.length < 10) {
      setError(translate("Text must be at least 10 characters long"));
    } else {
        // Add user message to chat
        const userMessage: ChatMessage = {
          text: value,
          isUser: true,
          timestamp: new Date()
        };
        setMessages(prev => [...prev, userMessage]);
        setInputText('');
        setIsLoading(true);
        try {
          // Check if the prompt is for creating a tour


          const aiResponse: ChatMessage = {
            text: translate("Creating your interaction..."),
            isUser: false,
            timestamp: new Date()
          };
          setMessages(prev => [...prev, aiResponse]);

          const data = await CreateInteraction(value, accountId, window.location.href);

          setCurrentGuideId(data?.GuideId);
          data.TargetUrl = window.location.href;
          if(data?.GuideStep[0]?.StepType === "Banner" && data?.GuideStep[0]?.ButtonSection?.length>0){
            setBannerButtonSelected(true);
          }
          setInteractionData(data);
          setOpenWarning(false); // Reset openWarning when starting new AI creation
          updatedGuideData = data;

          generateSteps(data?.GuideStep || []);
          setInputText("");

          if (onClose) onClose();

          if (data) {
            setDataNew(data);
            if (data?.GuideType.toLowerCase() === "announcement") {
              setIsPopupOpen(true);
              setCurrentGuideId(data?.GuideId);
              setSelectedTemplate("Announcement");
              setBannerPopup(false);
              SetGuideName(data?.Name);
              if (setStepData) setStepData({ type: "Announcement" });
              TooltipGuideDetails();

              // Synchronize AI announcement data with announcementGuideMetaData
              setTimeout(() => {
                syncAIAnnouncementDataForPreview(false); // Allow setting global state during initial setup
              }, 100);
            }
            else if (data?.GuideType.toLowerCase() === "banner") {
              setSelectedTemplate("Banner", true);
              setBannerPopup(true);
              setIsPopupOpen(false);
              setCurrentGuideId(data?.GuideId);
              SetGuideName(data?.Name);
              if (setStepData) setStepData({ type: "Banner" });
              // Ensure the builder screen is displayed
              TooltipGuideDetails();
            }
            else if (data?.GuideType.toLowerCase() === "hotspot") {
              setElementSelected(false);
              setSelectedTemplate("Hotspot", true);
              setIsPopupOpen(false);
              SetGuideName(data?.Name);
              setIsLoggedIn(true);
              setIsTourPopupOpen(false);
              setIsDrawerClosed(true);
              setShowBannerenduser(false);
              if (setStepData) setStepData({ type: "Hotspot" });
              // Ensure the builder screen is displayed
              HotspotGuideDetails();

            }
            else if (data?.GuideType.toLowerCase() === "tooltip") {
              setElementSelected(false);
              setSelectedTemplate("Tooltip", true);
              setIsPopupOpen(false);
              SetGuideName(data?.Name);
              setIsLoggedIn(true);
              setIsTourPopupOpen(false);
              setIsDrawerClosed(true);
              setShowBannerenduser(false);
              if (setStepData) setStepData({ type: "Tooltip" });
              // Ensure the builder screen is displayed
              TooltipGuideDetails();
              setTooltipCount(tooltipCount + 1);

            }
            else if (data?.GuideType.toLowerCase() === "tour") {
              setSelectedTemplate("Tour", true);
              setCurrentGuideId(data?.GuideId);
              SetGuideName(data?.Name);
              setIsPopupOpen(true);
              setBannerPopup(false);
              setIsLoggedIn(true);
              setIsTourPopupOpen(true);
              setIsDrawerClosed(false);
              setShowBannerenduser(false);

              // Generate steps for the drawer
              if (data?.GuideStep && data.GuideStep.length > 0) {
                const stepsData = data.GuideStep.map((step: any, index: number) => ({
                  id: step.StepId,
                  name: step.StepTitle || `Step ${index + 1}`,
                  stepType: step.StepType,
                  stepCount: index + 1
                }));
                setSteps(stepsData);

                // Set the current step to the first step
                if (stepsData.length > 0) {
                  setCurrentStep(1);

                  // Handle different step types based on the first step
                  const firstStep = data.GuideStep[0];
                  if (firstStep && firstStep.StepType) {
                    const stepType = firstStep.StepType;
                    setStepDataNew(stepType);

                    // Set the selected template tour to the first step's type
                    setSelectedTemplateTour(stepType);

                    // Handle specific step types
                    if (stepType.toLowerCase() === "announcement") {
                      TooltipGuideDetails();
                      setSelectedTemplateTour("Announcement");
                      setSelectedTemplate("Tour", true);

                      // Synchronize AI announcement data with announcementGuideMetaData
                      setTimeout(() => {
                        syncAIAnnouncementDataForPreview(false); // Allow setting global state during initial setup
                      }, 100);

                      if (setStepData) setStepData({ type: "Announcement" });
                    }
                    else if (stepType.toLowerCase() === "banner") {
                      TooltipGuideDetails();
                      setSelectedTemplate("Tour", true);
                      setSelectedTemplateTour("Banner");
                      if (setStepData) setStepData({ type: "Banner" });
                      setBannerPopup(true);
                    }
                    else if (stepType.toLowerCase() === "tooltip") {
                      setElementSelected(false);

                      TooltipGuideDetails();
                      setSelectedTemplateTour("Tooltip");
                      setSelectedTemplate("Tour", true);

                      // Synchronize AI tooltip data with tooltipguidemetadata
                      setTimeout(() => {
                        syncAITooltipDataForPreview();
                      }, 100);

                      if (setStepData) setStepData({ type: "Tooltip" });
                      setTooltipCount(tooltipCount + 1);
                    }
                    else if (stepType.toLowerCase() === "hotspot") {
                      setElementSelected(false);
                      HotspotGuideDetails();
                      setSelectedTemplateTour("Hotspot");
                      setSelectedTemplate("Tour", true);
                      setSelectedStepTypeHotspot(true);
                      if (setStepData) setStepData({ type: "Hotspot" });
                      setTooltipCount(tooltipCount + 1);
                    }
                  }
                }
              }
            }
            else if (data?.GuideType.toLowerCase() === "checklist") {
              // Handle checklist type if needed
              setSelectedTemplate("Checklist", true);
              setCurrentGuideId(data?.GuideId);
              SetGuideName(data?.Name);
              if (setStepData) setStepData({ type: "Checklist" });
            }
          }

        } catch (error) {
          console.error("Error creating interaction:", error);
          const errorMessage: ChatMessage = {
            text: translate("Sorry, I encountered an error while processing your request."),
            isUser: false,
            timestamp: new Date()
          };
          setMessages(prev => [...prev, errorMessage]);
        } finally {
          setIsLoading(false);
          setError(null);
        }
    }
};
  return (
    <>
    <div className="qadpt-chat-window">
      <div className="qadpt-chat-header">
        <div className="qadpt-chat-title">
          <span className="qadpt-chat-icon" dangerouslySetInnerHTML={{ __html: ai }} />
            <span>{translate("AI Chatbot")}</span>
        </div>
          <button className="qadpt-close-btn" onClick={onClose} aria-label={translate("Close")}> {/* accessibility */}
          <CloseIcon fontSize="small" />
        </button>
      </div>
          <div className="qadpt-chat-container">
        <div className="qadpt-messages" ref={chatContainerRef} onScroll={handleScroll}>
          {messages.map((message, index) => (
            <div
              key={index}
              className={`qadpt-message ${message.isUser ? 'user-message' : 'ai-message'}`}
            >
              {!message.isUser && (
                <div className="ai-avatar">
                  <span dangerouslySetInnerHTML={{ __html: airobot }} />
                </div>
              )}
              <div className="message-content">
                <p>{message.text}</p>
              </div>
            </div>
          ))}
          {isLoading && (
            <div className="qadpt-chat-message ai-message">
              <div className="ai-avatar">
                <span dangerouslySetInnerHTML={{ __html: airobot }} />
              </div>
              <div className="message-content">
                <div className="typing-indicator">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />

          {showScrollButton && (
              <button className="scroll-to-bottom" onClick={scrollToBottom} aria-label={translate("Scroll to bottom")}> {/* accessibility */}
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 16L6 10L7.4 8.6L12 13.2L16.6 8.6L18 10L12 16Z" fill="currentColor"/>
              </svg>
            </button>
          )}
          </div>
          </div>

      <div className="qadpt-input">
          {error && <div className="error-message">{translate(error)}</div>}

            <div className="input-with-icons">
            <div className='qadpt-txtcont'>

            <textarea
              ref={inputRef}
              value={inputText}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              placeholder={isOpenAIKeyProvided ? translate("Enter your prompt....") : translate("Please Add OpenAI Key At Setting -> Agents")}
              disabled={isLoading}
              rows={1}
                />
           </div>
            <div className="input-icons">
              <div>
              <button
                className="icon-btn upload-btn"
                onMouseEnter={() => setIsUploadHovered(true)}
                onMouseLeave={() => setIsUploadHovered(false)}
                  aria-label={translate("Upload")}
              >
                <span dangerouslySetInnerHTML={{ __html: isUploadHovered ? upload_hover : upload }} />
                </button>
                </div>
              <button
                className="icon-btn send-btn"
                onClick={() => handleCreateInteraction(inputText)}
                disabled={!isOpenAIKeyProvided || isLoading || !inputText.trim()}
                aria-label={translate("Send")}
              >
                <span dangerouslySetInnerHTML={{ __html: send }} />
              </button>
            </div>
          </div>

          </div>

    </div>
    </>
  );
};

export default ModernChatWindow;
