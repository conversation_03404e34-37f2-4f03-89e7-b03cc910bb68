import React, { useEffect, useState } from "react";
import {
	Button,
	Tooltip,
	Box,
	LinearProgress,
	Typography,
	tooltipClasses,
	TooltipProps,
	MobileStepper,
	Breadcrumbs,
} from "@mui/material";
//import { CustomIconButton } from "../../components/Button";
import { CustomIconButton } from "../Button";
import CloseIcon from "@mui/icons-material/Close";
import { styled } from "@mui/material/styles";
import useDrawerStore, { DrawerState } from "../../../store/drawerStore";
interface ButtonAction {
	Action: string;
	ActionValue: string;
	TargetUrl: string;
}

interface ButtonProperties {
	Padding: number;
	Width: number;
	Font: number;
	FontSize: number;
	ButtonTextColor: string;
	ButtonBackgroundColor: string;
}

interface ButtonData {
	ButtonStyle: string;
	ButtonName: string;
	Alignment: string;
	BackgroundColor: string;
	ButtonAction: ButtonAction;
	Padding: {
		Top: number;
		Right: number;
		Bottom: number;
		Left: number;
	};
	ButtonProperties: ButtonProperties;
}
interface Step {
	xpath: string;
	content: string | JSX.Element;
	targetUrl: string;
	imageUrl: string;
	buttonData: ButtonData[];
	overlay: boolean;
	positionXAxisOffset: string;
	positionYAxisOffset: string;
}

interface TooltipGuideProps {
	steps: Step[];
	currentUrl: string;
	onClose: () => void;
	tooltipConfig: any;
}

const CustomWidthTooltip = styled(({ className, ...props }: TooltipProps) => (
	<Tooltip
		{...props}
		classes={{ popper: className }}
		id="Tooltip-unique"
	/>
))({
	[`& .${tooltipClasses.tooltip}`]: {
		//maxWidth: TOOLTIP_MX_WIDTH,
		backgroundColor: "#d58fcf",
		color: "black",
		fontSize: "14px",
		padding: "12px 16px",
		borderRadius: "8px",
		boxShadow: "0px 4px 12px rgba(0, 0, 0, 0.2)",
	},
	[`& .${tooltipClasses.arrow}`]: {
		color: "#d58fcf",
	},
	[`&.MuiTooltip-popper`]: {
		zIndex: 11000,
	},
});

const TooltipGuide: React.FC<TooltipGuideProps> = ({ steps, currentUrl, onClose, tooltipConfig }) => {
	let rect: any;
	const [currentStepIndex, setCurrentStepIndex] = useState(0);
	const [targetElement, setTargetElement] = useState<HTMLElement | null>(null);
	const [tooltipPosition, setTooltipPosition] = useState({ top: 0, left: 0 });

	const currentStep = steps[currentStepIndex];

	const getElementByXPath = (xpath: string): HTMLElement | null => {
		const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
		const node = result.singleNodeValue;
		if (node instanceof HTMLElement) {
			return node;
		} else if (node?.parentElement) {
			return node.parentElement;
		} else {
			return null;
		}
	};
	const

		{ProgressColor

		}= useDrawerStore((state: DrawerState) => state);

	// const updateTooltipPosition = (element: HTMLElement | null) => {
	//   if (element) {
	//     const rect = element.getBoundingClientRect();
	//     const xOffset = parseFloat(currentStep?.positionXAxisOffset || "0");
	//     const yOffset = parseFloat(currentStep?.positionYAxisOffset || "0");
	//     setTooltipPosition({
	//       top: rect.top + window.scrollY + yOffset,
	//       left: rect.left + window.scrollX + xOffset,
	//     });
	//   }
	// };
	const progress = ((currentStepIndex + 1) / steps.length) * 100;

	useEffect(() => {
		const updateTargetAndPosition = () => {
			if (currentUrl === currentStep?.targetUrl) {
				const element = getElementByXPath(currentStep?.xpath);
				if (element) {
					const rect = element.getBoundingClientRect();
					const xOffset = parseFloat(currentStep?.positionXAxisOffset || "0");
					const yOffset = parseFloat(currentStep?.positionYAxisOffset || "0");

					setTargetElement(element);
					setTooltipPosition({
						top: rect.top + window.scrollY + yOffset,
						left: rect.left + window.scrollX + xOffset,
					});

					element.style.backgroundColor = "blue"; // Optional: Highlight the element
				}
			} else {
				setTargetElement(null);
			}
		};

		updateTargetAndPosition();
	}, [currentStep, currentUrl]); // Ensure all dependencies are included

	const enableProgress = tooltipConfig.EnableProgress || false;
	const progressTemplate = tooltipConfig.ProgressTemplate || "dots";
	const handleNext = () => setCurrentStepIndex((prev) => Math.min(prev + 1, steps.length - 1));
	const handlePrevious = () => setCurrentStepIndex((prev) => Math.max(prev - 1, 0));

	const renderContent = () => {
		const hasImage = currentStep?.imageUrl.startsWith("data:image/") || currentStep?.imageUrl.startsWith("http");
		const hasText = typeof currentStep?.content === "string" || React.isValidElement(currentStep?.content);

		return (
			<Box>
				{hasImage && (
					<Box
						component="img"
						src={currentStep.imageUrl}
						alt="Step Image"
						width="100%"
					/>
				)}
				{hasText && (
					<Box
						dangerouslySetInnerHTML={{
							__html: typeof currentStep.content === "string" ? currentStep.content : "",
						}}
					/>
				)}
			</Box>
		);
	};
	const renderProgress = () => {
		if (!enableProgress) return null;

		if (progressTemplate === "dots") {
			return (
				<MobileStepper
					variant="dots"
					steps={steps.length}
					position="static"
					activeStep={currentStepIndex + 1}
					sx={{ backgroundColor: "transparent",  "& .MuiMobileStepper-dotActive": {
                        backgroundColor: ProgressColor, // Active dot
                      }, }}
					backButton={<Button style={{ visibility: "hidden" }} />}
					nextButton={<Button style={{ visibility: "hidden" }} />}
				/>
			);
		}

		// if (progressTemplate === "breadcrumbs") {
		// 	return (
		// 		<Breadcrumbs
		// 			aria-label="breadcrumb"
		// 			sx={{ marginTop: "10px" }}
		// 		>
		// 			{steps.map((_, index) => (
		// 				<Typography
		// 					key={index}
		// 					color={index === currentStepIndex+1 ? "primary" : "text.secondary"}
		// 				>
		// 					Step {index + 1} of {steps.length}
		// 				</Typography>
		// 			))}
		// 		</Breadcrumbs>
		// 	);
		// }
		if (progressTemplate === "BreadCrumbs") {
			return (
                <Box sx={{display: "flex",
					alignItems: "center",
					placeContent: "center",
					gap: "5px",padding:"8px"}}>
                  {/* Custom Step Indicators */}

                    {Array.from({ length: steps.length }).map((_, index) => (
                      <div
                        key={index}
                        style={{
                          width: '14px',
                          height: '4px',
                          backgroundColor: index === currentStepIndex+1 ? ProgressColor : '#e0e0e0', // Active color and inactive color
                          borderRadius: '100px',
                        }}
                      />
                    ))}

                </Box>
              );
		}

		if (progressTemplate === "breadcrumbs") {
			return (
				<Box sx={{paddingTop:"8px"}}>
					<Typography
						variant="body2"
						sx={{ padding: "8px", color: ProgressColor}}
					>
						Step {currentStepIndex+1} of {steps.length}
					</Typography>
				</Box>
			);
		}

		if (progressTemplate === "linear") {
			return (
				<Box sx={{paddingTop:"8px"}}>
					<Typography variant="body2">
						<LinearProgress
							variant="determinate"
							value={progress}
							sx={{'& .MuiLinearProgress-bar': {
                                backgroundColor: ProgressColor, // progress bar color
                              },}}
						/>
					</Typography>
				</Box>
			);
		}

		return null;
	};

	const renderButtons = () => {
		return currentStep?.buttonData?.length > 0
			? currentStep.buttonData.map((button, index) => {
					const buttonStyle = {
						backgroundColor: button.ButtonProperties.ButtonBackgroundColor,
						color: button.ButtonProperties.ButtonTextColor,
						padding: `${button.Padding.Top}px ${button.Padding.Right}px ${button.Padding.Bottom}px ${button.Padding.Left}px`,
						width: button.ButtonProperties.Width ? `${button.ButtonProperties.Width}px` : "auto",
						fontSize: button.ButtonProperties.FontSize ? `${button.ButtonProperties.FontSize}px` : "14px",
						fontFamily: button.ButtonProperties.Font ? `"Font-${button.ButtonProperties.Font}` : "Arial",
						borderRadius: "15px",
						textTransform: "none",
						boxShadow: "none !important", // Remove box shadow in normal state
						"&:hover": {
							boxShadow: "none !important", // Remove box shadow in hover state
							backgroundColor: button.ButtonProperties.ButtonBackgroundColor, // Keep the same background color on hover
							opacity: 0.9, // Slightly reduce opacity on hover for visual feedback
						},
					};
					const handleClick = () => {
						if (button.ButtonAction.Action === "Open Url" && button.ButtonAction.TargetUrl) {
							window.location.href = button.ButtonAction.TargetUrl;
						} else if (button.ButtonAction.Action === "Next") {
							handleNext();
						} else if (button.ButtonAction.Action === "previous") {
							handlePrevious();
						} else if (button.ButtonAction.Action === "Restart") {
							// Reset to the first step
							setCurrentStepIndex(0);
							// Check if we need to navigate to a different page (multi-page) or stay on current page (single-page)
							if (steps[0]?.targetUrl && steps[0].targetUrl !== window.location.href) {
								// Multi-page: Navigate to the first step's URL
								window.location.href = steps[0].targetUrl;
							} else {
								// Single-page: Just scroll to the first step's element
								if (steps[0]?.xpath) {
									const firstStepElement = getElementByXPath(steps[0].xpath);
									if (firstStepElement) {
										firstStepElement.scrollIntoView({ behavior: 'smooth' });
									}
								}
							}
						} else if (button.ButtonAction.Action === "Open-Url") {
							if (button.ButtonAction.ActionValue === "next-tab") {
								window.open(button.ButtonAction.TargetUrl, "_blank");
							} else if (button.ButtonAction.ActionValue === "same-tab") {
								window.location.href = button.ButtonAction.TargetUrl;
							}
						}
					};
					return (
						<Button
							key={index}
							variant="contained"
							sx={buttonStyle}
							onClick={handleClick}
						>
							{button.ButtonName}
						</Button>
					);
			  })
			: null;
	};

	const TooltipContent = (
		<Box>
			<Box
				position="absolute"
				top={8}
				right={8}
				display="flex"
				justifyContent="center"
				alignItems="center"
			>
				<CustomIconButton
					sx={{ color: "white" }}
					onClick={onClose}
				>
					<CloseIcon sx={{ fontSize: "medium" }} />
				</CustomIconButton>
			</Box>
			<Box mb={2}>{renderContent()}</Box>

			<Box
				mb={2}
				display="flex"
				justifyContent="space-between"
			>
				{renderButtons()}
			</Box>
			{enableProgress && <Box mb={2}>{renderProgress()}</Box>}
		</Box>
	);

	const overlayStyle = {
		position: "fixed",
		top: 0,
		left: 0,
		width: "100%",
		height: "100%",
		zIndex: 9999,
	};
	const overlay = currentStep?.overlay || "";
	const targetRectStyle = targetElement
		? {
				position: "absolute",
				top: targetElement.offsetTop,
				left: targetElement.offsetLeft,
				width: targetElement.offsetWidth,
				height: targetElement.offsetHeight,
				zIndex: 10000,
		  }
		: {};

	const overlaySectionStyle = {
		position: "absolute",
		backgroundColor: "rgba(0, 0, 0, 0.5)",
	};

	const topStyle = targetElement
		? {
				...overlaySectionStyle,
				top: 0,
				left: 0,
				width: "100%",
				height: targetElement.offsetTop,
		  }
		: {};

	const bottomStyle = targetElement
		? {
				...overlaySectionStyle,
				top: targetElement.offsetTop + targetElement.offsetHeight,
				left: 0,
				width: "100%",
				height: `calc(100% - ${targetElement.offsetTop + targetElement.offsetHeight}px)`,
		  }
		: {};

	const leftStyle = targetElement
		? {
				...overlaySectionStyle,
				top: targetElement.offsetTop,
				left: 0,
				width: targetElement.offsetLeft,
				height: targetElement.offsetHeight,
		  }
		: {};

	const rightStyle = targetElement
		? {
				...overlaySectionStyle,
				top: targetElement.offsetTop,
				left: targetElement.offsetLeft + targetElement.offsetWidth,
				width: `calc(100% - ${targetElement.offsetLeft + targetElement.offsetWidth}px)`,
				height: targetElement.offsetHeight,
		  }
		: {};

	return (
		<>
			{overlay && (
				<Box sx={overlayStyle}>
					<Box sx={topStyle} />
					<Box sx={bottomStyle} />
					<Box sx={leftStyle} />
					<Box sx={rightStyle} />
					<Box sx={targetRectStyle} />
				</Box>
			)}
			{targetElement && (
				<CustomWidthTooltip
					open
					title={TooltipContent}
					placement="top"
					arrow
					PopperProps={{
						anchorEl: targetElement,
					}}
					sx={{
						position: "absolute",
						top: `${tooltipPosition.top}px`,
						left: `${tooltipPosition.left}px`,
						transform: "translate(-50%, -100%)",
					}}
				>
					<Box
						sx={{
							position: "absolute",
							top: targetElement.offsetTop,
							left: targetElement.offsetLeft,
							width: targetElement.offsetWidth,
							height: targetElement.offsetHeight,
						}}
					/>
				</CustomWidthTooltip>
			)}
		</>
	);
};

export default TooltipGuide;
