import '@fontsource/poppins'; 

export const fonts = [
	{ label: "Poppins", value: "Poppins, sans-serif" },
	{ label: "Arial", value: "Arial, sans-serif" },
	{ label: "Times New Roman", value: '"Times New Roman", serif' },
	{ label: "Georgia", value: "Georgia, serif" },
	{ label: "Courier New", value: '"Courier New", monospace' },
	{ label: "Verdana", value: "Verdana, sans-serif" },
	{ label: "Tahoma", value: "Tahoma, sans-serif" },
	{ label: "Trebuchet MS", value: '"Trebuchet MS", sans-serif' },
	{ label: "Impact", value: "Impact, sans-serif" },
	{ label: "Comic Sans MS", value: '"Comic Sans MS", cursive, sans-serif' },
	{ label: "Lucida Sans", value: '"Lucida Sans", sans-serif' },
	{ label: "Palatino Linotype", value: '"Palatino Linotype", serif' },
	{ label: "<PERSON><PERSON><PERSON>", value: "<PERSON><PERSON>mond, serif" },
	{ label: "Bookman", value: "Bookman, serif" },
	{ label: "Helvetica", value: "Helvetica, sans-serif" },
	{ label: "Rockwell", value: "Rockwell, serif" },
	{ label: "Gill Sans", value: "Gill Sans, sans-serif" },
	{ label: "Franklin Gothic", value: "Franklin Gothic, sans-serif" },
	{ label: "Baskerville", value: "Baskerville, serif" },
	{ label: "Cambria", value: "Cambria, serif" },
	{ label: "Candara", value: "Candara, sans-serif" },
	{ label: "Didot", value: "Didot, serif" },
	{ label: "Optima", value: "Optima, sans-serif" },
	{ label: "Futura", value: "Futura, sans-serif" },
	{ label: "Century Gothic", value: "Century Gothic, sans-serif" },
	{ label: "Avant Garde", value: '"Avant Garde", sans-serif' },
	// { label: 'Gill Sans', value: 'Gill Sans, sans-serif' },
	{ label: "Merriweather", value: "Merriweather, serif" },
	{ label: "Lobster", value: "Lobster, cursive" },
	{ label: "Raleway", value: "Raleway, sans-serif" },
	{ label: "Montserrat", value: "Montserrat, sans-serif" },
	{ label: "Open Sans", value: '"Open Sans", sans-serif' },
	{ label: "Playfair Display", value: '"Playfair Display", serif' },
	{ label: "Roboto", value: '"Roboto", sans-serif' },
	{ label: "Ubuntu", value: "Ubuntu, sans-serif" },
	{ label: "Nunito", value: "Nunito, sans-serif" },
	{ label: "Lora", value: "Lora, serif" },
	{ label: "Droid Serif", value: '"Droid Serif", serif' },
	{ label: "PT Serif", value: '"PT Serif", serif' },
	{ label: "Work Sans", value: '"Work Sans", sans-serif' },
	{ label: "Oswald", value: '"Oswald", sans-serif' },
	{ label: "Source Sans Pro", value: '"Source Sans Pro", sans-serif' },
	{ label: "Muli", value: '"Muli", sans-serif' },
	{ label: "Zilla Slab", value: '"Zilla Slab", serif' },
	{ label: "Abril Fatface", value: '"Abril Fatface", serif' },
	{ label: "Exo 2", value: '"Exo 2", sans-serif' },
	{ label: "Josefin Sans", value: '"Josefin Sans", sans-serif' },
	{ label: "Indie Flower", value: '"Indie Flower", cursive' },
	{ label: "Pacifico", value: "Pacifico, cursive" },
	{ label: "Shadows Into Light", value: '"Shadows Into Light", cursive' },
];
