import React from 'react';
import { ai } from '../../assets/icons/icons';
import './EnableAIButton.css';

interface EnableAIButtonProps {
  onClick: () => void;
}

const EnableAIButton: React.FC<EnableAIButtonProps> = ({ onClick }) => {
  const handleClick = () => {
    // Check if we're in development mode (localhost)
    const isDevelopmentMode = window.location.hostname === 'localhost';

    // Check if we're in extension context
    const isExtensionContext = !!(window.chrome?.runtime?.id);

    if (isDevelopmentMode && !isExtensionContext) {
      // In development mode without extension context, just call onClick directly
      console.log('Development mode detected, skipping extension communication');
      onClick();
      return;
    }

    // Try to close the extension UI if available
    if (window.chrome?.runtime?.sendMessage) {
      try {
        if (isExtensionContext) {
          // We're in extension context, send message directly
          window.chrome.runtime.sendMessage({ action: "closeExtension" }, (response) => {
            if (window.chrome.runtime.lastError) {
              console.warn('Extension communication error:', window.chrome.runtime.lastError.message);
              onClick();
            } else if (response?.success) {
              onClick();
            } else {
              onClick();
            }
          });
        } else {
          // We're in webpage context, just proceed without extension communication
          console.log('Webpage context detected, proceeding without extension communication');
          onClick();
        }
      } catch (error) {
        console.warn('Extension communication failed:', error);
        onClick();
      }
    } else {
      // Chrome API not available, just call onClick
      onClick();
    }
  };

  return (
    <button className="enable-ai-button" onClick={handleClick}>
      <span className="enable-ai-icon" dangerouslySetInnerHTML={{ __html: ai }} />
      <span className="enable-ai-text">Start Training</span>
    </button>
  );
};

export default EnableAIButton;