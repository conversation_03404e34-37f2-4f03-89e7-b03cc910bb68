import React, { useState } from "react";
import { Box, Typography, TextField, Button, IconButton, Tooltip } from "@mui/material";
import ArrowBackIosIcon from "@mui/icons-material/ArrowBackIos";
import CloseIcon from "@mui/icons-material/Close";
// import Draggable from "react-draggable";

const PageTrigger = () => {
	const [isOpen, setIsOpen] = useState(true);
	const [timedDelay, setTimedDelay] = useState("");
	const [onScroll, setOnScroll] = useState("");

	const handleClose = () => {
		setIsOpen(false);
	};

	const handleTimedDelayChange = (e: any) => {
		const value = e.target.value;
		if (/^\d*$/.test(value)) {
			// Regex to allow only digits
			setTimedDelay(value);
		}
	};

	const handleOnScrollChange = (e: any) => {
		const value = e.target.value;
		if (/^\d*$/.test(value)) {
			// Regex to allow only digits
			setOnScroll(value);
		}
	};

	if (!isOpen) return null;

	return (
		//<Draggable>
		<div
			id="qadpt-designpopup"
			className="qadpt-designpopup"
		>
			<div className="qadpt-content">
				<div className="qadpt-design-header">
					<div className="qadpt-title">Page Trigger</div>
					<IconButton
						size="small"
						aria-label="close"
						onClick={handleClose}
					>
						<CloseIcon />
					</IconButton>
				</div>

				<div className="qadpt-controls">
					{/* Timed Delay Control */}
					<Box className="qadpt-control-box">
						<Typography className="qadpt-control-label">Timed Delay</Typography>
						<Box className="qadpt-input-box">
							<TextField
								value={timedDelay}
								onChange={handleTimedDelayChange}
								variant="outlined"
								size="small"
								className="qadpt-control-input"
								inputProps={{
									style: { textAlign: "center" },
									endAdornment: "px",
									type: "number",
								}}
							/>
						</Box>
					</Box>

					{/* On Scroll Control */}
					<Box className="qadpt-control-box">
						<Typography className="qadpt-control-label">On Scroll</Typography>
						<Box className="qadpt-input-box">
							<TextField
								value={onScroll}
								onChange={handleOnScrollChange}
								variant="outlined"
								size="small"
								className="qadpt-control-input"
								inputProps={{
									style: { textAlign: "center" },
									endAdornment: "px",
									type: "number",
								}}
							/>
						</Box>
					</Box>
				</div>

				<Box
					sx={{
						backgroundColor: "var(--back-light-color)",
						borderRadius: "10px",
						padding: "12px",
					}}
				>
					<Typography sx={{ fontSize: "14px", marginBottom: "8px", color: "rgba(0, 0, 0, 0.38)" }}>
						Click Element
					</Typography>

					<Tooltip
						title="Coming Soon"
						PopperProps={{ sx: { zIndex: 9999 } }}
					>
						<span>
							<Button
								disabled
								variant="outlined"
								sx={{
									width: "100%",
									textTransform: "none",
									borderRadius: "8px",
									borderColor: "var(--border-color)",
									color: "var(--primarycolor)",
								}}
							>
								Choose element
							</Button>
						</span>
					</Tooltip>
				</Box>
			</div>
		</div>
		//</Draggable>
	);
};

export default PageTrigger;
