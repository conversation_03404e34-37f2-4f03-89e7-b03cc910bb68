.qadpt-chat-window {
    position: fixed;
    left: 0;
    top: 0;
    width: 400px;
    height: 100vh;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    z-index: 9999999;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    border-radius: 0;
  }

  .qadpt-chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
    background-color: #fff;
  }

  .qadpt-chat-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #04417F;
  }

  .qadpt-chat-icon {
    display: flex;
    align-items: center;
    color: #0066cc;
  }

  .qadpt-chat-icon svg {
    width: 20px;
    height: 20px;
  }

  .qadpt-close-btn {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
  }

  .qadpt-close-btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
  .qadpt-chat-container {  
    height: calc(100vh - 130px); 
    overflow: auto;
    padding: 20px;
    /* padding-bottom: 20px; */
  }
  .qadpt-messages {
   
    display: flex;
    flex-direction: column;
    background-color: #fff;
    position: relative;
    min-height: 100%;
    place-content: flex-end;
  }
  .qadpt-message {
    display: flex;
    max-width: 85%;
    position: relative;
    animation: fadeIn 0.3s ease-in-out;
   /* padding: 10px 0 0 20px; */
    }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  .user-message {
    align-self: flex-end;
    flex-direction: row;
    /* padding-right: 20px; */
    white-space: nowrap;
    word-break: break-word;
  }
  .user-message .message-content {
    background-color: #f0f0f0;
    color: #333;
    border-bottom-right-radius: 4px; 
  }
  .ai-message {
    align-self: flex-start;
  }
  .ai-message .message-content {
    color: #333;
    border-bottom-left-radius: 4px;
  }
 

  .ai-avatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    background-color: #f0f0f0;
    flex-shrink: 0;
  }
  .ai-avatar svg{
    display: flex;
  }

  .message-content {
    padding: 5px 16px 12px 5px;
        border-radius: 18px;
    position: relative;
    font-size: 14px;
    line-height: 1.5;
    text-align: left;
  }

 


  .message-content p {
    margin: 0;
    white-space: pre-wrap;
  }

  .typing-indicator {
    display: flex;
    align-items: center;
    gap: 4px;
    padding-left: 20px;
  }

  .typing-indicator span {
    width: 6px;
    height: 6px;
    background-color: #aaa;
    border-radius: 50%;
    display: inline-block;
    animation: typing 1.4s infinite ease-in-out both;
  }

  .typing-indicator span:nth-child(1) {
    animation-delay: 0s;
  }

  .typing-indicator span:nth-child(2) {
    animation-delay: 0.2s;
  }

  .typing-indicator span:nth-child(3) {
    animation-delay: 0.4s;
  }

  @keyframes typing {
    0%, 80%, 100% { transform: scale(0.6); opacity: 0.6; }
    40% { transform: scale(1); opacity: 1; }
  }

  .qadpt-input {
    padding: 15px;
    border-top: 1px solid #f0f0f0;
    background-color: white;
    position: relative;
  }

  .error-message {
    color: #d32f2f;
    font-size: 14px;
    margin-bottom: 10px;
    padding: 8px 12px;
    background-color: #ffebee;
    border-radius: 4px;
    border-left: 3px solid #d32f2f;
  }

  .qadpt-input-box {
    display: flex;
    flex-direction: column;
    position: relative;
  }

  .input-with-icons {
    display: flex;
    align-items: center;
    flex-direction: column;
    border: 1px solid #e0e0e0; 
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 10px 12px;
    border-radius: 12px;
  }
  .input-with-icons:focus-within {
    border-color: #a0a0a0 !important;
  }
  .qadpt-txtcont{
    width: 100%;
  }
  .qadpt-txtcont textarea {
    width: 100%;
    border-radius: 4px;
    font-size: 14px;
    resize: none; 
    min-height: 45px;
    max-height: 100px;
    align-content: center;
    outline: none;
    transition: border-color 0.3s;
    font-family: inherit;
    color: #333;
    padding: 0 !important;
    border: none !important;
  }
  

  .input-icons {
    display: flex;
    align-items: center;
    width: 100%;
    place-content: space-between;
  }
  .input-icons div {
    display: flex ;
        gap: 8px;
  }

  .icon-btn {
    background: none;
    border: none;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .icon-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .icon-btn span {
    display: flex;
  }

  .mic-btn, .upload-btn {
    width: 30px;
    height: 30px;
  }

  .scroll-to-bottom {
    position: fixed;
    bottom: 100px;
    right: 20px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #0066cc;
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 10000;
    transition: all 0.2s ease;
  }

  .scroll-to-bottom:hover {
    background-color: #0055b3;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }
