



import React, { useEffect, useMemo, useState } from 'react';
import ChecklistCircle from "./ChecklistCheckIcon";
import useDrawerStore from '../../store/drawerStore';
import LauncherSettings from './LauncherSettings';
import ImageCarousel from "./ImageCarousel";
import VideoPlayer from "./VideoPlayer";
import {  chkdefault, closepluginicon, maximize } from '../../assets/icons/icons';
import { GetGudeDetailsByGuideId } from '../../services/GuideListServices';
import { useTranslation } from 'react-i18next';
import '../../styles/rtl_styles.scss';

// Function to modify the color of an SVG icon
const modifySVGColor = (base64SVG: any, color: any) => {
	if (!base64SVG) {
		return "";
	}

	try {
		// Check if the string is a valid base64 SVG
		if (!base64SVG.includes("data:image/svg+xml;base64,")) {
			return base64SVG; // Return the original if it's not an SVG
		}

		const decodedSVG = atob(base64SVG.split(",")[1]);

		// Check if this is primarily a stroke-based or fill-based icon
		const hasStroke = decodedSVG.includes('stroke="');
		const hasColoredFill = /fill="(?!none)[^"]+"/g.test(decodedSVG);

		let modifiedSVG = decodedSVG;

		if (hasStroke && !hasColoredFill) {
			// This is a stroke-based icon (like chkicn2-6) - only change stroke color
			modifiedSVG = modifiedSVG.replace(/stroke="[^"]+"/g, `stroke="${color}"`);
		} else if (hasColoredFill) {
			// This is a fill-based icon (like chkicn1) - only change fill color
			modifiedSVG = modifiedSVG.replace(/fill="(?!none)[^"]+"/g, `fill="${color}"`);
		} else {
			// No existing fill or stroke, add fill to make it visible
			modifiedSVG = modifiedSVG.replace(/<path(?![^>]*fill=)/g, `<path fill="${color}"`);
			modifiedSVG = modifiedSVG.replace(/<svg(?![^>]*fill=)/g, `<svg fill="${color}"`);
		}

		const modifiedBase64 = `data:image/svg+xml;base64,${btoa(modifiedSVG)}`;
		return modifiedBase64;
	} catch (error) {
		console.error("Error modifying SVG color:", error);
		return base64SVG; // Return the original if there's an error
	}
};
interface CheckListPopupProps {
    isOpen: any;
    onClose: () => void;
    onRemainingCountUpdate: (formattedCount: string) => void;
    data: any;
    guideDetails: any;
    isRightPanelVisible: any;
    setIsRightPanelVisible: any;
  }
  const ChecklistPreview: React.FC<CheckListPopupProps> = ({
		isOpen,
		onClose,
		onRemainingCountUpdate,
		data,
		guideDetails,
		isRightPanelVisible,
		setIsRightPanelVisible,
	}) => {
	  const { t: translate } = useTranslation();
		const { checklistGuideMetaData, createWithAI, interactionData } = useDrawerStore((state: any) => state);

		const [isMaximized, setIsMaximized] = useState(false);
		const [completedStatus, setCompletedStatus] = useState<{ [key: string]: boolean }>({});

		const checkpointslistData =
			checklistGuideMetaData[0]?.checkpoints?.checkpointsList?.map((checkpoint: any, index: number) => ({
				...checkpoint,
				completed: index === 0 ? true : false,
			})) || [];

		const [checklistItems, setChecklistItems] = useState(checkpointslistData);
		const [activeItem, setActiveItem] = useState(checkpointslistData[0]?.id || "");

		useEffect(() => {
			if (Object.keys(completedStatus).length === 0) {
				const initialCompletedStatus: { [key: string]: boolean } = {};

				checkpointslistData.forEach((item: any, index: number) => {
					initialCompletedStatus[item.id] = index === 0;
				});

				setCompletedStatus(initialCompletedStatus);
			}
		}, []);
		const checklistColor = checklistGuideMetaData[0]?.canvas?.primaryColor;
		const selectedItem = checkpointslistData?.find((item: any) => item.id === activeItem);

		useEffect(() => {
			document.documentElement.style.setProperty("--chkcolor", checklistColor);
		}, [checklistColor]);
		const [isPublished, setIsPublished] = useState(true);
		useEffect(() => {
			const fetchGuideDetails = async () => {
				if (!selectedItem?.id) return; // Ensure there's a valid ID

				try {
					const res = await GetGudeDetailsByGuideId(selectedItem.id, createWithAI, interactionData);
					if (res?.GuideDetails?.GuideStatus === "InActive" || res?.GuideDetails?.GuideStatus === "Draft") {
						setIsPublished(false);
					} else {
						setIsPublished(true);
					}
				} catch (error) {}
			};

			fetchGuideDetails();
		}, [selectedItem, activeItem]);

		useEffect(() => {
			if (checkpointslistData) setActiveItem(checkpointslistData[0]?.id);
		}, [checkpointslistData?.length == 1]);
		useEffect(() => {
			if (checklistGuideMetaData[0]?.length > 0) {
				const checkpointList = checklistGuideMetaData[0]?.checkpoints?.checkpointlist || [];

				const formattedChecklist = checkpointList.map((checkpoint: any, index: number) => ({
					id: checkpoint.id || index + 1,
					title: checkpoint.title || `Step ${index + 1}`,
					description: checkpoint.description || "No description provided",
					redirectURL: checkpoint.redirectURL || "",
					icon: checkpoint.icon || "",
					supportingMedia: checkpoint.supportingMedia || "",
					mediaTitle: checkpoint.mediaTitle || "",
					mediaDescription: checkpoint.mediaDescription || "",
				}));
				setChecklistItems(formattedChecklist);
				const initialCompletedStatus: { [key: string]: boolean } = {};
				formattedChecklist.forEach((item: any) => {
					initialCompletedStatus[item.id] = false;
				});

				setCompletedStatus(initialCompletedStatus);
			}
		}, [checklistGuideMetaData[0]]); // Update when checklistGuideMetaData changes

		const totalItems = checkpointslistData.length || 1;
		const progress = Object.values(completedStatus).filter((status) => status).length || 1;

		// We'll let the ChecklistPopup component update the count based on completed status
		// This useEffect is no longer needed as we're getting the count from ChecklistPopup

		const toggleItemCompletion = (id: string) => {
			setCompletedStatus((prevStatus) => ({
				...prevStatus,
				[id]: !prevStatus[id],
			}));
		};

		const handleMarkAsCompleted = (id: string) => {
			setCompletedStatus((prevStatus) => ({
				...prevStatus,
				[id]: true,
			}));
		};

		const handleSelect = (id: any) => {
			setActiveItem(id);
			setIsRightPanelVisible(true);
		};

		const handleClose = () => {
			if (isRightPanelVisible) {
				setIsRightPanelVisible(false);
			} else {
				onClose();
			}
		};
		const handleMinimize = () => {
			setIsMaximized(false);
		};

		if (!isOpen) return null;

		if (!isOpen) return null;

		const handleNavigate = () => {
			// window.open("http://localhost:3000/", '_blank');
		};

		const handleMaximize = () => {
			setIsMaximized(true);
	  };
// 	  const xOffset = parseInt(checklistGuideMetaData[0]?.launcher?.launcherposition?.xaxisOffset || "10");
// const xOffsetWithUnit = `${xOffset + 30}px`;
	  		const isRTL = 
  document.documentElement.getAttribute('dir') === 'rtl' ||
  document.body.getAttribute('dir') === 'rtl';

		return (
			<>
				{isOpen && (
					<div
						style={{
							position: "fixed",
							inset: 0,
							display: "flex",
							alignItems: "center",
							// justifyContent: 'center',
							zIndex: '99999',						}}
					>
						<div
							style={{
								position: "absolute",
								inset: 0,
								backgroundColor: "rgba(0, 0, 0, 0.3)",
							}}
							onClick={handleClose}
						></div>

						<div
							style={{
								boxShadow: "rgba(0, 0, 0, 0.1) 0px 20px 25px -5px, rgba(0, 0, 0, 0.04) 0px 10px 10px -5px",
								zIndex: 9,
								marginTop: "auto",
								marginBottom: `${parseInt(checklistGuideMetaData[0]?.launcher?.launcherposition?.yaxisOffset || "10") + 70}px`,
								marginLeft: checklistGuideMetaData[0]?.launcher.launcherposition.left === true ? `${parseInt(checklistGuideMetaData[0]?.launcher?.launcherposition?.xaxisOffset || "10") + 30}px` : "auto",
								marginRight: checklistGuideMetaData[0]?.launcher.launcherposition.left === true ? "auto" : `${parseInt(checklistGuideMetaData[0]?.launcher?.launcherposition?.xaxisOffset || "10") + 30}px`,
							}}
							className="qadpt-chkpopup"					>
							<div
								style={{
									backgroundColor: checklistGuideMetaData[0]?.canvas?.backgroundColor,
									border: `${checklistGuideMetaData[0]?.canvas?.borderWidth}px solid ${checklistGuideMetaData[0]?.canvas?.borderColor}`,
									borderRadius: `${checklistGuideMetaData[0]?.canvas?.cornerRadius}px`,
									width: isRightPanelVisible ? `${checklistGuideMetaData[0]?.canvas?.width || 930}px` : "350px",
									height: `${checklistGuideMetaData[0]?.canvas?.height || 500}px`,
								}}
							>
								<div
									style={{
										display: "flex",
										height: "100%",
										width: "100%",
										overflow: "auto hidden",
									}}
									className="qadpt-chkcontent"
								>
									{/* Left side - Checklist items */}
									<div
										style={{
											width: isRightPanelVisible ? "40%" : "100%",
											borderRight: "1px solid #e5e7eb",

											textAlign: isRTL ? "right" : "left",
										}}
										className="qadpt-chkrgt"
									>
										<div
											style={{
												display: "flex",
												flexDirection: "column",
												gap: "16px",
												borderBottom: "1px solid #E8E8E8",
												padding: "24px 24px 16px 24px",
											}}
										>
											<div
												style={{
													display: "flex",
													flexDirection: "column",
													gap: "6px",
												}}
											>
												<div style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}>
													<div
														style={{
															fontSize: "20px",
															fontWeight: checklistGuideMetaData[0]?.TitleSubTitle?.titleBold ? "bold" : "normal",
															fontStyle: checklistGuideMetaData[0]?.TitleSubTitle?.titleItalic ? "italic" : "normal",
															color: checklistGuideMetaData[0]?.TitleSubTitle?.titleColor || "#333",
															display: "block",
															textOverflow: "ellipsis",
															whiteSpace: "nowrap",
															wordBreak: "break-word",
															overflow: "hidden",
														}}
													>
														{translate(checklistGuideMetaData[0]?.TitleSubTitle?.title || "Checklist Title")}
													</div>
													<div>
														{!isRightPanelVisible && (
															<span
																dangerouslySetInnerHTML={{ __html: closepluginicon }}
																onClick={handleClose}
																style={{
																	background: "#e8e8e8",
																	borderRadius: "50%",
																	padding: "8px",
																	display: "flex",
																	cursor: "pointer",
																}}
															/>
														)}
													</div>
												</div>
												<div
													style={{
														fontSize: "14px",
														fontWeight: checklistGuideMetaData[0]?.TitleSubTitle?.subTitleBold ? "bold" : "normal",
														fontStyle: checklistGuideMetaData[0]?.TitleSubTitle?.subTitleItalic ? "italic" : "normal",
														color: checklistGuideMetaData[0]?.TitleSubTitle?.subTitleColor || "#8D8D8D",
													}}
													className="qadpt-subtl"
												>
													{translate(checklistGuideMetaData[0]?.TitleSubTitle?.subTitle || "Context about the tasks in the checklist below users should prioritize completing.")}
												</div>
											</div>

											<div>
												<div
													style={{
														display: "flex",
														alignItems: "center",
														justifyContent: "space-between",
														marginBottom: "8px",
													}}
												>
													<span style={{ fontSize: "14px", color: "#6b7280" }}>
														{progress}/{totalItems}
													</span>
												</div>
												<div
													style={{
														height: "8px",
														backgroundColor: "#e5e7eb",
														borderRadius: "9999px",
														overflow: "hidden",
													}}
												>
													<div
														style={{
															height: "100%",
															backgroundColor: checklistGuideMetaData[0]?.canvas?.primaryColor,
															borderRadius: "9999px",
															width: `${(progress / totalItems) * 100}%`,
														}}
													></div>
												</div>
											</div>
										</div>

										<div
											style={{
													maxHeight: `calc(${checklistGuideMetaData[0]?.canvas?.height || 500}px - 190px)`,
													overflow: "auto",
													}}
											className="qadpt-chklist"
										>
											{checkpointslistData?.map((item: any) => (
												<div className={`${activeItem === item.id ? "qadpt-chkstp" : ""}`}>
													<div
														key={item.id}
														style={{
															display: "flex",
															flexDirection: "column",
															padding: "10px 16px 10px 10px",
															cursor: "pointer",
															// borderLeft: activeItem === item.id ? `4px solid ${checklistGuideMetaData[0]?.canvas?.primaryColor}` : '4px solid transparent', // Straight left border highlight
															borderBottom: "1px solid #E8E8E8",
														}}
														onClick={() => handleSelect(item.id)}
													>
														{/* Title Section */}
														<div style={{ paddingLeft: "10px", display: "flex", gap: "6px", flexDirection: "column" }}>
															<div
																style={{
																	display: "flex",
																	alignItems: "center",
																	justifyContent: "space-between",
																	width: "100%",
																}}
															>
																<div
																	style={{
																		display: "flex",
																		alignItems: "center",
																		gap: "10px",
																		flexDirection: "row",
																		width: "calc(100% - 60px)",
																	}}
																>
																	{item.icon && typeof item.icon === "string" ? (
																		<img
																			src={modifySVGColor(item.icon, checklistGuideMetaData[0]?.checkpoints?.checkpointsIcons || "#333")}
																			alt="icon"
																			style={{ width: "20px", height: "20px" }}
																		/>
																	) : (
																		<div
																			style={{
																				width: "20px",
																				height: "20px",
																				display: "flex",
																				alignItems: "center",
																				justifyContent: "center",
																			}}
																		>
																			<span style={{ width: "16px", height: "16px" }}></span>
																		</div>
																	)}

																	<span
																		style={{
																			color: checklistGuideMetaData[0]?.checkpoints?.checkpointTitles || "#333",
																			overflow: "hidden",
																			textOverflow: "ellipsis",
																			whiteSpace: "nowrap",
																			wordBreak: "break-word",
																		}}
																	>
																		{item.title}
																	</span>
																</div>
																<div>
																	<ChecklistCircle
																		key={item.id}
																		completed={completedStatus[item.id]}
																		onClick={() => {}}
																		size="sm"
																	/>
																</div>
															</div>
															<div>
																<p
																	style={{
																		fontSize: "14px",
																		color: checklistGuideMetaData[0]?.checkpoints?.checkpointsDescription,
																	}}
																	className="qadpt-chkpopdesc"
																>
																	{item.description}
																</p>
															</div>
														</div>
													</div>
												</div>
											))}
										</div>
									</div>

									{/* Right side - Selected item details - only show when an item is selected */}
									{/* {activeItem && ( */}
									{isRightPanelVisible && (
										<div
											style={{
												width: "60%",
												padding: "20px 20px 0 20px",
											}}
											className="qadpt-chklft"
										>
											<div
													style={{
														display: "flex",
														alignItems: "center",

														placeContent: "end",
														width: "100%",
														gap: "6px",
													}}
												>
													<span
														dangerouslySetInnerHTML={{ __html: maximize }}
														style={{
															background: "#e8e8e8",
															borderRadius: "50%",
															padding: "6px",
															display: "flex",
															cursor: "pointer",
														}}
														onClick={handleMaximize}
													/>
													<span
														dangerouslySetInnerHTML={{ __html: closepluginicon }}
														onClick={handleClose}
														style={{
															background: "#e8e8e8",
															borderRadius: "50%",
															padding: "8px",
															display: "flex",
															cursor: "pointer",
														}}
													/>
													</div>
											<div
									style={{
										display: "flex",
										alignItems: "center",
										flexDirection: "column",
										gap: "10px",    height: "calc(100% - 90px)",
										
									}}
								>
												
													<div style={{
    									overflow: "hidden auto",display: "flex",
										alignItems: "center",
										flexDirection: "column",width:"-webkit-fill-available"}} >
												{selectedItem?.supportingMedia?.length > 0 && (
													<>
														{selectedItem.supportingMedia.some((file: any) =>
															file?.Base64?.startsWith("data:image")
														) && (
															<ImageCarousel
																selectedItem={selectedItem}
																activeItem={activeItem}
																images={selectedItem.supportingMedia
																	.filter((file: any) => file?.Base64?.startsWith("data:image"))
																	.map((file: any) => file.Base64)}
																isMaximized={isMaximized}
															/>
														)}

														{selectedItem.supportingMedia.some((file: any) => file?.Base64?.startsWith("data:video")) &&
															selectedItem.supportingMedia
																.filter((file: any) => file?.Base64?.startsWith("data:video"))
																.map((file: any, index: number) => (
																	<VideoPlayer
																		key={index}
																		videoFile={file.Base64}
																		isMaximized={isMaximized}
																	/>
																))}
													</>
												)}
												{(selectedItem?.supportingMedia?.length === 0 || !selectedItem?.supportingMedia) && (
													<div style={{ width: "auto", height: "244px" }}>
														<span dangerouslySetInnerHTML={{ __html: chkdefault }} />
														<div style={{ color: "#8D8D8D" }}>{translate("Check tasks, stay organized, and finish strong!")}</div>
													</div>
												)}

												<div
													style={{ width: "100%", marginTop: "10px" }}
													className="qadpt-chkdesc"
												>
													{selectedItem && (
														<div style={{ height: "100%", display: "flex", flexDirection: "column" }}>
															<div
																style={{
																	textAlign: isRTL ? "right" : "left",
																	display: "flex",
																	flexDirection: "column",
																	gap: "12px",
																}}
															>
																<div
																	style={{
																		fontSize: "16px",
																		fontWeight: 600,
																		color: "#333",
																		overflow: "hidden",
																		textOverflow: "ellipsis",
																		whiteSpace: "nowrap",
																		wordBreak: "break-word",
																	}}
																>
																	{selectedItem.mediaTitle}
																</div>

																<div
																	className="qadpt-desc"
																	style={{ color: "#8D8D8D" }}
																>
																	{selectedItem.mediaDescription}
																</div>
															</div>
														</div>
													)}
												</div>
												</div>
												</div>
											<div
												style={{
													display: "flex",
													gap: "12px",
													alignItems: "center",
													placeContent: "end",
													paddingBottom: "20px",
												}}
												className="qadpt-btnsec"
											>
												<button
													style={{
														backgroundColor: checklistGuideMetaData[0].canvas?.primaryColor,
														borderRadius: "10px",
														padding: "9px 16px",
														color: "#fff",
														border: "none",
														cursor: isPublished ? "pointer" : "not-allowed",
													}}
													disabled={!isPublished}
												>
													{isPublished ? translate("Take Tour") : translate("Interaction Not Available")}
												</button>
												{selectedItem?.supportingMedia?.length > 0 && (
													<button
														style={{
															borderRadius: "10px",
															padding: "9px 16px",
															color: checklistGuideMetaData[0]?.canvas?.primaryColor,
															border: "none",
															background: "#D3D9DA",
															cursor: "pointer",
														}}
														onClick={(e: any) => handleMarkAsCompleted(selectedItem?.id)}
													>
														{translate("Mark as Completed")}
													</button>
												)}
												</div>
												</div>
										
									)}
									{/* )} */}
								</div>
							</div>
						</div>
					</div>
				)}

				{isMaximized && (
					<div
						style={{
							position: "fixed",
							top: 0,
							left: 0,
							right: 0,
							bottom: 0,
							backgroundColor: "rgba(0, 0, 0, 0.5)",
							zIndex: 99999,
						}}
					>
						<div
							style={{
								position: "fixed",
								inset: 0,
								display: "flex",
								alignItems: "center",
								// justifyContent: 'center',
								zIndex: 50,
							}}
						>
							<div
								style={{
									boxShadow: "rgba(0, 0, 0, 0.1) 0px 20px 25px -5px, rgba(0, 0, 0, 0.04) 0px 10px 10px -5px",
									zIndex: 9,
									marginTop: "8%",
									marginBottom: "5%",
									// marginLeft: 'auto',
									//       marginRight: '100px',
									display: "flex",
									alignItems: "center",
									placeContent: "center",
									width: "100%",
								}}
								className="qadpt-chkpopup"
							>
								<div
									style={{
										backgroundColor: checklistGuideMetaData[0]?.canvas?.backgroundColor,
										border: `${checklistGuideMetaData[0]?.canvas?.borderWidth}px solid ${checklistGuideMetaData[0]?.canvas?.borderColor}`,
										borderRadius: `${checklistGuideMetaData[0]?.canvas?.cornerRadius}px`,
										width: "calc(-250px + 100vw)",
										height: "calc(100vh - 140px)",
										overflow: "auto",
									}}
								>
									<div
										style={{
											display: "flex",
											height: "100%",
											width: "100%",
										}}
										className="qadpt-chkcontent"
									>
										{/* Left side - Checklist items */}

										{/* Right side - Selected item details - only show when an item is selected */}
										{/* {activeItem && ( */}

										<div
											style={{
												width: "100%",
												padding: "20px 20px 0 20px",
											}}
											className="qadpt-chklft"
										>
											<div style={{ display: "flex", alignItems: "center", flexDirection: "column", gap: "10px" }}>
												<div
													style={{
														display: "flex",
														alignItems: "center",
														placeContent: "end",
														width: "100%",
														gap: "6px",
													}}
													onClick={handleMinimize}
												>
													<span
														dangerouslySetInnerHTML={{ __html: closepluginicon }}
														style={{
															background: "#e8e8e8",
															borderRadius: "50%",
															padding: "8px",
															display: "flex",
															cursor: "pointer",
														}}
													/>
												</div>
												{selectedItem?.supportingMedia?.length === 0 && (
													<div style={{ width: "auto", height: "244px" }}>
														<span dangerouslySetInnerHTML={{ __html: chkdefault }} />
														<div style={{ color: "#8D8D8D" }}>{translate("Check tasks, stay organized, and finish strong!")}</div>
													</div>
												)}
												{selectedItem?.supportingMedia?.length > 0 && (
													<>
														{selectedItem.supportingMedia.some((file: any) =>
															file?.Base64?.startsWith("data:image")
														) && (
															<ImageCarousel
																selectedItem={selectedItem}
																activeItem={activeItem}
																images={selectedItem.supportingMedia
																	.filter((file: any) => file?.Base64?.startsWith("data:image"))
																	.map((file: any) => file.Base64)}
																isMaximized={isMaximized}
															/>
														)}

														{selectedItem.supportingMedia.some((file: any) => file?.Base64?.startsWith("data:video")) &&
															selectedItem.supportingMedia
																.filter((file: any) => file?.base64?.startsWith("data:video"))
																.map((file: any, index: number) => (
																	<VideoPlayer
																		key={index}
																		videoFile={file.base64}
																		isMaximized={isMaximized}
																	/>
																))}
													</>
												)}
												<div
													style={{ width: "100%", marginTop: "10px" }}
													className="qadpt-chkdesc"
												>
													{selectedItem && (
														<div style={{ height: "100%", display: "flex", flexDirection: "column" }}>
															<div
																style={{
																	textAlign: isRTL ? "right" : "left",
																	display: "flex",
																	flexDirection: "column",
																	gap: "12px",
																	width: "100%",
																}}
															>
																<div
																	style={{
																		fontSize: "16px",
																		fontWeight: 600,
																		color: "#333",
																		overflow: "hidden",
																		textOverflow: "ellipsis",
																		whiteSpace: "nowrap",
																		wordBreak: "break-word",
																	}}
																>
																	{selectedItem.mediaTitle}
																</div>

																<div className="qadpt-desc">{selectedItem.mediaDescription}</div>
															</div>

															<div
																style={{
																	display: "flex",
																	gap: "12px",
																	alignItems: "center",
																	placeContent: "end",
																	paddingBottom: "20px",
																}}
																className="qadpt-btnsec"
															>
																<button
																	style={{
																		backgroundColor: checklistGuideMetaData[0]?.canvas?.primaryColor,
																		borderRadius: "10px",
																		padding: "9px 16px",
																		color: "#fff",
																		border: "none",
																		cursor: isPublished ? "pointer" : "not-allowed",
																	}}
																	onClick={handleNavigate}
																	disabled={!isPublished}
																>
																	{isPublished ? translate("Take Tour") : translate("Interaction Not Available")}
																</button>
																{selectedItem?.supportingMedia?.length > 0 && (
													<button
														style={{
															borderRadius: "10px",
															padding: "9px 16px",
															color: checklistGuideMetaData[0]?.canvas?.primaryColor,
															border: "none",
															background: "#D3D9DA",
															cursor: "pointer",
														}}
														onClick={(e: any) => handleMarkAsCompleted(selectedItem?.id)}
													>
																		{translate("Mark as Completed")}
													</button>
												)}
															</div>
														</div>
													)}
												</div>
											</div>
										</div>

										{/* )} */}
									</div>
								</div>
							</div>
						</div>
					</div>
				)}
			</>
		);
	};
export default ChecklistPreview;