import React from 'react';
import { check } from '../../assets/icons/icons';
import useDrawerStore from '../../store/drawerStore';

interface ChecklistCircleProps {
  completed?: boolean;
  onClick: (e: any) => void;
  size?: 'sm' | 'md' | 'lg';
  isPopupOpen?: boolean;
}

const ChecklistCircle: React.FC<ChecklistCircleProps> = ({ 
  completed = false, 
  onClick, 
  size = 'sm',  // Changed default to small
  isPopupOpen = false
}) => {
  // Size variants in pixels
  const sizeMap: any = {
    sm: '22px',
    md: '32px',
    lg: '40px'
  };
  const {
    checklistGuideMetaData,

  } = useDrawerStore((state: any) => state);
  // Simplified styles without hover effects
  const getCircleStyles = () => {
    const baseStyles = {
      height: sizeMap[size],
      width: sizeMap[size],
      borderRadius: '50%',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      border: '1px solid',
      cursor: 'pointer'
    };
    
    if (isPopupOpen) {
      return {
        ...baseStyles,
        backgroundColor: '#ef4444', // red-500 for cross icon
        color: 'white',
        borderColor: 'transparent'
      };
    } else if (completed) {
      return {
        ...baseStyles,
        backgroundColor: checklistGuideMetaData[0]?.canvas?.primaryColor ||'#14b8a6', // teal-500
        color: 'white',
        borderColor: 'transparent'
      };
    } else {
      return {
        ...baseStyles,
        backgroundColor: 'white',
        color: '#9ca3af', // gray-400
        borderColor: '#d1d5db' // gray-300
      };
    }
  };

  return (
    <div 
      style={getCircleStyles()}
      onClick={onClick}
    >
      {completed ? (
      
          <span dangerouslySetInnerHTML={{ __html: check }} style={{ zoom: 1 ,display:"flex"}} />

       
      ) : (
        null
      )}
    </div>
  );
};


export default ChecklistCircle;