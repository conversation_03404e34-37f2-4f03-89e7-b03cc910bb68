// Scraper module for content.js
// This file exports the scraping functionality for the content script

/**
 * Get XPath for an element
 */
const generateXPath = (element) => {
  if (!element) return '';
  if (element.id) return `//*[@id="${element.id}"]`;

  let path = '';
  let current = element;

  while (current && current.nodeType === Node.ELEMENT_NODE) {
    let index = 1;
    let sibling = current.previousElementSibling;

    while (sibling) {
      if (sibling.nodeName === current.nodeName) {
        index++;
      }
      sibling = sibling.previousElementSibling;
    }

    const tagName = current.nodeName.toLowerCase();
    path = `/${tagName}[${index}]${path}`;
    current = current.parentElement;
  }

  return path;
};

/**
 * Get CSS selector for an element
 */
const generateCSSSelector = (element) => {
  if (!element) return '';
  if (element.id) return `#${element.id}`;

  let selector = element.tagName.toLowerCase();

  // Handle different types of className (string, SVGAnimatedString, etc.)
  if (element.className) {
    let classStr = '';

    // Handle SVGAnimatedString or other object types
    if (typeof element.className === 'object' && element.className.baseVal !== undefined) {
      classStr = element.className.baseVal;
    }
    // Handle string className
    else if (typeof element.className === 'string') {
      classStr = element.className;
    }

    if (classStr) {
      const classes = classStr.split(' ').filter(c => c);
      if (classes.length > 0) {
        selector += `.${classes.join('.')}`;
      }
    }
  }

  return selector;
};

/**
 * Check if an element is visible
 */
function isVisible(element) {
  const style = window.getComputedStyle(element);
  return (
    style.display !== 'none' &&
    style.visibility !== 'hidden' &&
    style.opacity !== '0' &&
    element.offsetParent !== null
  );
}

/**
 * Recursively scrape the DOM and collect content up to a given depth
 */
export function scrapeDOM(maxDepth = 3) {
  const results = [];

  const excludedTags = ['SCRIPT', 'STYLE', 'NOSCRIPT', 'IFRAME'];

  function traverse(node, depth) {
    if (depth > maxDepth) return;
    if (node.nodeType !== Node.ELEMENT_NODE || excludedTags.includes(node.tagName)) return;

    const el = node;
    if (!isVisible(el)) return;

    const tag = el.tagName.toLowerCase();
    const attributes = {};
    for (let attr of Array.from(el.attributes)) {
      attributes[attr.name] = attr.value;
    }

    let text = '';

    if (tag === 'input' || tag === 'textarea') {
      text = el.value || '';
    } else if (tag === 'select') {
      const selected = el.selectedOptions[0];
      text = selected?.text || '';
    } else {
      text = el.innerText?.trim();
    }

    if (text) {
      results.push({
        tag,
        text,
        attributes,
        xpath: generateXPath(el),
        cssPath: generateCSSSelector(el)
      });
    }

    for (let child of Array.from(el.children)) {
      traverse(child, depth + 1);
    }
  }

  traverse(document.body, 0);
  return Promise.resolve(results);
}

// Export the scraping functions
export default {
  scrapeDOM
};
