// scraper.ts - Contains functions for DOM traversal and element data extraction

/**
 * Interface for element data
 */


let observer: MutationObserver | null = null;
export interface ElementData {
  tagName: string;
  id: string;
  className: string;
  text: string;
  attributes: Record<string, string>;
  xpath: string;
  cssSelector: string;
  rect: {
    top: number;
    left: number;
    width: number;
    height: number;
  };
  children: ElementData[];
}

/**
 * Interface for scraped page data
 */
export interface ScrapedPageData {
  url: string;
  title: string;
  timestamp: string;
  elements: ElementData[];
}

// Track continuous scraping
let continuousScrapingInterval: number | null = null;

/**
 * Get XPath for an element
 */
function generateXPath(el: Element): string {
  if (el.id) return `//*[@id="${el.id}"]`;

  const path = [];
  while (el && el.nodeType === Node.ELEMENT_NODE) {
    let index = 1;
    let sibling = el.previousElementSibling;
    while (sibling) {
      if (sibling.nodeName === el.nodeName) index++;
      sibling = sibling.previousElementSibling;
    }
    const tagName = el.nodeName.toLowerCase();
    path.unshift(`${tagName}[${index}]`);
    el = el.parentElement!;
  }
  return '/' + path.join('/');
}

function generateCSSSelector(el: Element): string {
  if (el.id) return `#${el.id}`;
  const parts: string[] = [];
  while (el && el.nodeType === Node.ELEMENT_NODE) {
    let selector = el.nodeName.toLowerCase();
    if (el.className) {
      const classList = el.className.trim().split(/\s+/).join('.');
      if (classList) selector += `.${classList}`;
    }
    parts.unshift(selector);
    el = el.parentElement!;
  }
  return parts.join(' > ');
}

/**
 * Extract data from a DOM element
 */
export const extractElementData = (element: Element, depth: number = 0, maxDepth: number = 3): ElementData | null => {
  if (!element) return null;

  try {
    // Get element's bounding rectangle
    const rect = element.getBoundingClientRect();

    // Get all attributes
    const attributes: Record<string, string> = {};
    Array.from(element.attributes).forEach(attr => {
      attributes[attr.name] = attr.value;
    });

    // Extract text content, trimming whitespace
    const text = element.textContent?.trim() || '';

    // Create element data object
    const data: ElementData = {
      tagName: element.tagName.toLowerCase(),
      id: element.id || '',
      className: element.className || '',
      text: text,
      attributes: attributes,
      xpath: generateXPath(element),
      cssSelector: generateCSSSelector(element),
      rect: {
        top: rect.top,
        left: rect.left,
        width: rect.width,
        height: rect.height
      },
      children: []
    };

    // Process children if we haven't reached max depth
    if (depth < maxDepth) {
      Array.from(element.children).forEach(child => {
        const childData = extractElementData(child, depth + 1, maxDepth);
        if (childData) {
          data.children.push(childData);
        }
      });
    }

    return data;
  } catch (error) {
    console.error('Error extracting element data:', error);
    return null;
  }
};

/**
 * Get all visible elements on the page
 */
const getAllVisibleElements = (): Element[] => {
  // Get all elements in the document
  const allElements = document.querySelectorAll('*');
  const visibleElements: Element[] = [];

  // Filter for visible elements
  allElements.forEach(element => {
    // Skip script, style, meta, and other non-visible elements
    const tagName = element.tagName.toLowerCase();
    if (tagName === 'script' || tagName === 'style' || tagName === 'meta' ||
        tagName === 'head' || tagName === 'link' || tagName === 'noscript') {
      return;
    }

    // Check if element is visible
    const style = window.getComputedStyle(element);
    const isVisible = style.display !== 'none' &&
                      style.visibility !== 'hidden' &&
                      style.opacity !== '0' &&
                      element.getBoundingClientRect().width > 0 &&
                      element.getBoundingClientRect().height > 0;

    if (isVisible) {
      visibleElements.push(element);
    }
  });

  return visibleElements;
};

/**
 * Get all text nodes that are directly visible (not just container elements)
 */
const getVisibleTextElements = (): Element[] => {
  const allElements = document.querySelectorAll('*');
  const textElements: Element[] = [];

  allElements.forEach(element => {
    // Skip non-visible elements
    const style = window.getComputedStyle(element);
    if (style.display === 'none' || style.visibility === 'hidden' || style.opacity === '0') {
      return;
    }

    // Check if this element has direct text content (not just from children)
    const hasDirectText = Array.from(element.childNodes)
      .some(node => node.nodeType === Node.TEXT_NODE && node.textContent?.trim());

    if (hasDirectText) {
      textElements.push(element);
    }
  });

  return textElements;
};

/**
 * Scrape all elements from the page
 * @param maxDepth Maximum depth to traverse the DOM
 */
export const scrapeAllElements = (maxDepth: number = 3): ScrapedPageData => {
  // Get all visible elements
  const visibleElements = getAllVisibleElements();
  // Get elements with direct text content
  const textElements = getVisibleTextElements();

  // Combine and deduplicate
  const uniqueElements = new Set([...visibleElements, ...textElements]);
  const scrapedData: ElementData[] = [];

  // Process each element
  uniqueElements.forEach(element => {
    const elementData = extractElementData(element, 0, maxDepth);
    if (elementData) {
      // Only include elements that have some content (text, id, class, etc.)
      if (elementData.text || elementData.id || elementData.className ||
          Object.keys(elementData.attributes).length > 0) {
        scrapedData.push(elementData);
      }
    }
  });

  // Create the final data object
  return {
    url: window.location.href,
    title: document.title,
    timestamp: new Date().toISOString(),
    elements: scrapedData
  };
};
function isVisible(element: HTMLElement): boolean {
  const style = window.getComputedStyle(element);
  return (
    style &&
    style.display !== 'none' &&
    style.visibility !== 'hidden' &&
    style.opacity !== '0' &&
    element.offsetParent !== null
  );
}

/**
 * Recursively scrape the DOM and collect content up to a given depth
 */
export function scrapeDOM(maxDepth: number = 3): Promise<any[]> {
  const results: any[] = [];

  const excludedTags = ['SCRIPT', 'STYLE', 'NOSCRIPT', 'IFRAME'];

  function isVisible(element: HTMLElement): boolean {
    const style = window.getComputedStyle(element);
    return (
      style.display !== 'none' &&
      style.visibility !== 'hidden' &&
      style.opacity !== '0' &&
      element.offsetParent !== null
    );
  }

  function traverse(node: Element, depth: number) {
    if (depth > maxDepth) return;
    if (node.nodeType !== Node.ELEMENT_NODE || excludedTags.includes(node.tagName)) return;

    const el = node as HTMLElement;
    if (!isVisible(el)) return;

    const tag = el.tagName.toLowerCase();
    const attributes: { [key: string]: string } = {};
    for (let attr of Array.from(el.attributes)) {
      attributes[attr.name] = attr.value;
    }

    let text = '';

    if (tag === 'input' || tag === 'textarea') {
      text = (el as HTMLInputElement).value || '';
    } else if (tag === 'select') {
      const selected = (el as HTMLSelectElement).selectedOptions[0];
      text = selected?.text || '';
    } else {
      text = el.innerText?.trim();
    }

    if (text) {
      results.push({
        tag,
        text,
        attributes,
        xpath: generateXPath(el),
        cssPath: generateCSSSelector(el)
      });
    }

    for (let child of Array.from(el.children)) {
      traverse(child, depth + 1);
    }
  }

  traverse(document.body, 0);
  return Promise.resolve(results);
}


/**
 * Start scraping the page
 * @param maxDepth Maximum depth to traverse the DOM
 */
export const startScraping = (maxDepth: number = 3): Promise<ScrapedPageData> => {
  return new Promise((resolve) => {
    // Use requestAnimationFrame to avoid blocking the UI
    requestAnimationFrame(() => {
      const data = scrapeAllElements(maxDepth);
      resolve(data);
    });
  });
};

/**
 * Start continuous scraping
 * @param interval Interval in milliseconds between scrapes
 * @param maxDepth Maximum depth to traverse the DOM
 */




/**
 * Initialize scraping listeners
 */
export const initScraper = (): void => {
  console.log('[Scraper] Initializing...');

  if (typeof chrome !== 'undefined' && chrome.runtime) {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      if (message.action === 'startScraping') {
        console.log('[Scraper] One-time scrape + continuous + mutation watching');

        const maxDepth = message.maxDepth || 3;

        scrapeDOM(maxDepth).then(data => {
          chrome.runtime.sendMessage({
            action: 'scrapingComplete',
            data,
            continuous: false,
            append: false
          });

          startContinuousScraping(5000, maxDepth);
          startDOMObserver(maxDepth);

          sendResponse({ success: true });
        });

        return true;
      }

      if (message.action === 'stopScraping') {
        console.log('[Scraper] Stopping...');
        stopContinuousScraping();
        stopDOMObserver();
        sendResponse({ success: true });
        return true;
      }
    });
  }

  const userEvents = ['click', 'input', 'change', 'keyup', 'scroll'];
  userEvents.forEach(eventType => {
    document.addEventListener(eventType, () => {
      if (continuousScrapingInterval !== null) {
        console.log(`[Scraper] User interaction: ${eventType}`);
      }
    });
  });
};

export function startContinuousScraping(interval = 5000, maxDepth = 3) {
  stopContinuousScraping(); // Prevent double intervals
  continuousScrapingInterval = window.setInterval(() => {
    scrapeDOM(maxDepth).then(data => {
      chrome.runtime.sendMessage({
        action: 'startScraping',
        data,
        append: true
      });
    });
  }, interval);

  console.log(`[Scraper] Continuous scraping started every ${interval}ms`);
}

export function stopContinuousScraping() {
  if (continuousScrapingInterval !== null) {
    clearInterval(continuousScrapingInterval);
    continuousScrapingInterval = null;
    console.log('[Scraper] Continuous scraping stopped');
  }
}

function startDOMObserver(maxDepth = 3) {
  stopDOMObserver(); // Prevent duplicate observers

  observer = new MutationObserver((mutations) => {
    let hasNewContent = mutations.some(m =>
      m.type === 'childList' && (m.addedNodes.length > 0 || m.removedNodes.length > 0)
    );

    if (hasNewContent) {
      console.log('[Scraper] DOM changed - rescanning');
      scrapeDOM(maxDepth).then(data => {
        chrome.runtime.sendMessage({
          action: 'startScraping',
          data,
          append: true
        });
      });
    }
  });

  observer.observe(document.documentElement, {
    childList: true,
    subtree: true
  });

  console.log('[Scraper] MutationObserver active');
}

function stopDOMObserver() {
  if (observer) {
    observer.disconnect();
    observer = null;
    console.log('[Scraper] MutationObserver disconnected');
  }
}

