import axios, { AxiosRequestConfig, AxiosError } from "axios";
import useInfoStore from "../store/UserInfoStore";
import { useNavigate } from "react-router-dom";

export const adminUrl = process.env.REACT_APP_ADMIN_API;
export const userUrl = process.env.REACT_APP_USER_API;
export const idsUrl = process.env.REACT_APP_IDS_API;

const adminApiService = axios.create({
  baseURL: adminUrl,
});

const userApiService = axios.create({
  baseURL: userUrl,
});

const idsApiService = axios.create({
  baseURL: idsUrl,
});

const getAccessToken = () => useInfoStore.getState().accessToken;
const addAuthInterceptor = (service: any) => {
  service.interceptors.request.use(
    async (config: AxiosRequestConfig) => {
      const token = getAccessToken();
      if (token && config.headers) {
        config.headers["Authorization"] = `Bearer ${token}`;
      }
      return config;
    },
      (error: AxiosError) => Promise.reject(error)
    );
  };
    
  export const setupInterceptors = (navigate: ReturnType<typeof useNavigate>) => {
    idsApiService.interceptors.response.use(
      (response: any) => {
      return response;
      },
      (error: AxiosError) => {
      if (error.response && (error.response.status === 401 || error.response.status === 403)) {
        localStorage.removeItem('access_token');
        localStorage.removeItem('user_info');
        navigate("/login");
      }
      return Promise.reject(error);
      }
    );
  
    adminApiService.interceptors.response.use(
      (response: any) => {
        return response;
        },
      (error: AxiosError) => {
        if (error.response && (error.response.status === 401 || error.response.status === 403)) {
          localStorage.removeItem('access_token');
          localStorage.removeItem('user_info');
          navigate("/login");
        }
        return Promise.reject(error);
      }
    );
  
    userApiService.interceptors.response.use(
      (response: any) => {
        return response;
        },
      (error: AxiosError) => {
        if (error.response && (error.response.status === 401 || error.response.status === 403)) {
          localStorage.removeItem('access_token');
          localStorage.removeItem('user_info');
          navigate("/login");
        }
        return Promise.reject(error);
      }
    );
  };
// Apply interceptors to each service
addAuthInterceptor(adminApiService);
addAuthInterceptor(userApiService);
addAuthInterceptor(idsApiService);

export { userApiService, adminApiService, idsApiService };
