import React from "react";
import { Button, ButtonProps, IconButton, IconButtonProps } from "@mui/material";

type TCustomIconButton = IconButtonProps & {
	children: React.ReactNode;
};

type TCustomButton = ButtonProps & {
	children: React.ReactNode;
};
const CustomIconButton = ({ children, ...props }: TCustomIconButton) => {
	return <IconButton {...props}>{children}</IconButton>;
};
const CustomButton = ({ children, sx, ...props }: TCustomButton) => {
	return (
		<Button
			sx={{ ...sx }}
			{...props}
		>
			{children}
		</Button>
	);
};

export { CustomIconButton, CustomButton };
