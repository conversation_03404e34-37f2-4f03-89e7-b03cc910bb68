{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\drawer\\\\ThemeDropdown.tsx\",\n  _s = $RefreshSig$();\nimport React, { useContext, useEffect, useState } from \"react\";\nimport { Box, Button, Menu, MenuItem, Typography, CircularProgress } from \"@mui/material\";\nimport ArrowDropDownIcon from \"@mui/icons-material/ArrowDropDown\";\n// import { AccountContext } from \"../login/AccountContext\";\n// import { useAuth } from \"../auth/AuthProvider\";\nimport useDrawerStore from \"../../store/drawerStore\";\nimport { AccountContext } from \"../login/AccountContext\";\nimport { useAuth } from \"../auth/AuthProvider\";\nimport useInfoStore from \"../../store/UserInfoStore\";\nimport { GetOrganizationThemesByAccountId } from \"../../services/OrganizationService\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DETACHED_THEME = {\n  Id: \"detached-theme\",\n  organizationId: \"\",\n  accountId: \"\",\n  userId: \"\",\n  ThemeName: \"Theme Detached\",\n  themeDescription: \"Unsaved changes\",\n  level: \"Interaction\",\n  IsActive: false,\n  ThemeStyles: undefined\n};\nconst ThemeDropdown = () => {\n  _s();\n  var _selectedThemes$Theme, _selectedThemes$Theme2;\n  const [themes, setThemes] = useState([]);\n  const [selectedThemes, setSelectedThemes] = useState(null);\n  const [anchorEl, setAnchorEl] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const {\n    setSelectedTheme,\n    isUnSavedChanges,\n    setIsThemeChanges,\n    isThemeChanges\n  } = useDrawerStore();\n  const {\n    accountId\n  } = useContext(AccountContext);\n  const {\n    userDetails\n  } = useAuth();\n  const orgDetails = useInfoStore(state => state.orgDetails || \"{}\");\n  const organizationId = orgDetails.OrganizationId;\n  useEffect(() => {\n    const fetchThemes = async () => {\n      setLoading(true);\n      try {\n        const accId = accountId;\n        const orgId = organizationId;\n        const level = \"Interaction\";\n        const data = await GetOrganizationThemesByAccountId(accId, orgId, level);\n\n        // ✅ Separate active and inactive themes\n        const activeThemes = data.filter(t => t.IsActive);\n        const inactiveThemes = data.filter(t => !t.IsActive);\n\n        // ✅ Merge active first, then inactive\n        let sortedThemes = [...activeThemes, ...inactiveThemes];\n        if (isThemeChanges) {\n          sortedThemes = [DETACHED_THEME, ...sortedThemes];\n          setSelectedThemes(DETACHED_THEME);\n          setSelectedTheme(DETACHED_THEME);\n        } else {\n          const selected = activeThemes.find(t => {\n            var _t$ThemeStyles;\n            return (_t$ThemeStyles = t.ThemeStyles) === null || _t$ThemeStyles === void 0 ? void 0 : _t$ThemeStyles.Button;\n          }) || sortedThemes[0];\n          setSelectedThemes(selected);\n          setSelectedTheme(selected);\n          setIsThemeChanges(false);\n        }\n        setThemes(sortedThemes); // Update list for dropdown\n\n        // ✅ Set first active theme, or fallback to first\n      } catch (err) {\n        console.error(\"❌ Theme fetch failed:\", err);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchThemes();\n  }, [isThemeChanges]);\n  const handleClick = event => {\n    setAnchorEl(event.currentTarget);\n  };\n  const handleClose = () => setAnchorEl(null);\n  const handleSelectTheme = theme => {\n    if (theme.Id !== \"detached-theme\") {\n      setThemes(prev => prev.filter(t => t.Id !== \"detached-theme\"));\n    }\n    setSelectedThemes(theme);\n    setSelectedTheme(theme);\n    handleClose();\n\n    // Apply or remove theme-custom class based on theme name\n    const bodyElement = document.body;\n    if (theme.ThemeName && theme.ThemeName !== \"Default\") {\n      bodyElement.classList.add(\"theme-custom\");\n    } else {\n      bodyElement.classList.remove(\"theme-custom\");\n    }\n\n    // Apply theme styles\n    if (theme.ThemeStyles) {\n      console.log('🎨 Applying Theme for:', theme.ThemeName);\n\n      // Apply theme styles as CSS variables\n      Object.entries(theme.ThemeStyles).forEach(([key, value]) => {\n        if (value) {\n          const cssVarName = `--Theme-${key}`;\n          document.documentElement.style.setProperty(cssVarName, value);\n        }\n      });\n\n      // Generate and apply opacity variants for primaryColor, secondaryColor, accentColor\n      const targetColors = ['primaryColor', 'secondaryColor', 'accentColor'];\n      const opacities = [10, 20, 30, 40, 50];\n      const themeStyles = theme.ThemeStyles;\n      targetColors.forEach(colorKey => {\n        const colorValue = themeStyles[colorKey];\n        if (colorValue && typeof colorValue === 'string') {\n          // Set the base color\n          document.documentElement.style.setProperty(`--${colorKey}`, colorValue);\n\n          // Get RGB values for color mixing\n          let baseR = 0,\n            baseG = 0,\n            baseB = 0;\n\n          // Convert color to RGB values\n          if (colorValue.startsWith('#')) {\n            const hex = colorValue.replace('#', '');\n            baseR = parseInt(hex.substring(0, 2), 16);\n            baseG = parseInt(hex.substring(2, 4), 16);\n            baseB = parseInt(hex.substring(4, 6), 16);\n          } else if (colorValue.startsWith('rgb')) {\n            const rgbMatch = colorValue.match(/rgba?\\((\\d+),\\s*(\\d+),\\s*(\\d+)(?:,\\s*([\\d.]+))?\\)/);\n            if (rgbMatch) {\n              baseR = parseInt(rgbMatch[1]);\n              baseG = parseInt(rgbMatch[2]);\n              baseB = parseInt(rgbMatch[3]);\n            }\n          }\n\n          // Generate opacity variants\n          opacities.forEach(opacity => {\n            const alpha = opacity / 100;\n            const rgbaValue = `rgba(${baseR}, ${baseG}, ${baseB}, ${alpha})`;\n            const varName = `--${colorKey}-${opacity}`;\n            document.documentElement.style.setProperty(varName, rgbaValue);\n          });\n\n          // Generate white-mixed variants (lighter tints)\n          opacities.forEach(whitePercent => {\n            const whiteMix = whitePercent / 100;\n            // Mix with white: newColor = originalColor * (1 - whiteMix) + white * whiteMix\n            const mixedR = Math.round(baseR * (1 - whiteMix) + 255 * whiteMix);\n            const mixedG = Math.round(baseG * (1 - whiteMix) + 255 * whiteMix);\n            const mixedB = Math.round(baseB * (1 - whiteMix) + 255 * whiteMix);\n            const whiteVariantValue = `rgb(${mixedR}, ${mixedG}, ${mixedB})`;\n            const whiteVarName = `--${colorKey}-white-${whitePercent}`;\n            document.documentElement.style.setProperty(whiteVarName, whiteVariantValue);\n          });\n        }\n      });\n    }\n\n    // Optional: Apply globally via context\n    // dispatch({ type: \"SET_THEME\", payload: theme.ThemeStyles });\n  };\n  const getBtnColor = (theme, key) => {\n    var _theme$ThemeStyles, _theme$ThemeStyles$Bu;\n    return (theme === null || theme === void 0 ? void 0 : (_theme$ThemeStyles = theme.ThemeStyles) === null || _theme$ThemeStyles === void 0 ? void 0 : (_theme$ThemeStyles$Bu = _theme$ThemeStyles.Button) === null || _theme$ThemeStyles$Bu === void 0 ? void 0 : _theme$ThemeStyles$Bu[key]) || \"#ccc\";\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    display: \"flex\",\n    alignItems: \"center\",\n    gap: 1,\n    children: [/*#__PURE__*/_jsxDEV(Button, {\n      variant: \"outlined\",\n      onClick: handleClick,\n      endIcon: /*#__PURE__*/_jsxDEV(ArrowDropDownIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 18\n      }, this),\n      startIcon: selectedThemes !== null && selectedThemes !== void 0 && (_selectedThemes$Theme = selectedThemes.ThemeStyles) !== null && _selectedThemes$Theme !== void 0 && (_selectedThemes$Theme2 = _selectedThemes$Theme.Button) !== null && _selectedThemes$Theme2 !== void 0 && _selectedThemes$Theme2.primaryBtnBg ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: 12,\n          height: 12,\n          borderRadius: \"50%\",\n          backgroundColor: selectedThemes.ThemeStyles.Button.primaryBtnBg\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 13\n      }, this) : null,\n      sx: {\n        textTransform: \"none\",\n        borderRadius: 3\n      },\n      disabled: loading,\n      children: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 18\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 20\n      }, this) : (selectedThemes === null || selectedThemes === void 0 ? void 0 : selectedThemes.ThemeName) || \"Select Theme\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 225,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Menu, {\n      anchorEl: anchorEl,\n      open: Boolean(anchorEl),\n      onClose: handleClose,\n      children: themes.map(theme => /*#__PURE__*/_jsxDEV(MenuItem, {\n        onClick: () => {\n          handleSelectTheme(theme);\n          setIsThemeChanges(false);\n        },\n        selected: (selectedThemes === null || selectedThemes === void 0 ? void 0 : selectedThemes.Id) === theme.Id,\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: 1,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            children: theme.ThemeName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            display: \"flex\",\n            gap: 0.5,\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                width: 12,\n                height: 12,\n                borderRadius: \"50%\",\n                backgroundColor: getBtnColor(theme, \"primaryBtnBg\")\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                width: 12,\n                height: 12,\n                borderRadius: \"50%\",\n                backgroundColor: getBtnColor(theme, \"secondaryBtnBg\")\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 13\n        }, this)\n      }, theme.Id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 224,\n    columnNumber: 5\n  }, this);\n};\n_s(ThemeDropdown, \"jT3jls/hKuv6JRbqqzA6PU5a9LE=\", false, function () {\n  return [useDrawerStore, useAuth, useInfoStore];\n});\n_c = ThemeDropdown;\nexport default ThemeDropdown;\nvar _c;\n$RefreshReg$(_c, \"ThemeDropdown\");", "map": {"version": 3, "names": ["React", "useContext", "useEffect", "useState", "Box", "<PERSON><PERSON>", "<PERSON><PERSON>", "MenuItem", "Typography", "CircularProgress", "ArrowDropDownIcon", "useDrawerStore", "AccountContext", "useAuth", "useInfoStore", "GetOrganizationThemesByAccountId", "jsxDEV", "_jsxDEV", "DETACHED_THEME", "Id", "organizationId", "accountId", "userId", "ThemeName", "themeDescription", "level", "IsActive", "ThemeStyles", "undefined", "ThemeDropdown", "_s", "_selectedThemes$Theme", "_selectedThemes$Theme2", "themes", "setThemes", "selectedThemes", "setSelectedThemes", "anchorEl", "setAnchorEl", "loading", "setLoading", "setSelectedTheme", "isUnSavedChanges", "setIsThemeChanges", "isThemeChanges", "userDetails", "orgDetails", "state", "OrganizationId", "fetchThemes", "accId", "orgId", "data", "activeThemes", "filter", "t", "inactiveThemes", "sortedThemes", "selected", "find", "_t$ThemeStyles", "err", "console", "error", "handleClick", "event", "currentTarget", "handleClose", "handleSelectTheme", "theme", "prev", "bodyElement", "document", "body", "classList", "add", "remove", "log", "Object", "entries", "for<PERSON>ach", "key", "value", "cssVarName", "documentElement", "style", "setProperty", "targetColors", "opacities", "themeStyles", "colorKey", "colorValue", "baseR", "baseG", "baseB", "startsWith", "hex", "replace", "parseInt", "substring", "rgbMatch", "match", "opacity", "alpha", "rgbaValue", "varName", "whitePercent", "whiteMix", "mixedR", "Math", "round", "mixedG", "mixedB", "whiteVariantValue", "whiteVarName", "getBtnColor", "_theme$ThemeStyles", "_theme$ThemeStyles$Bu", "display", "alignItems", "gap", "children", "variant", "onClick", "endIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "startIcon", "primaryBtnBg", "sx", "width", "height", "borderRadius", "backgroundColor", "textTransform", "disabled", "size", "open", "Boolean", "onClose", "map", "_c", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/drawer/ThemeDropdown.tsx"], "sourcesContent": ["import React, { useContext, useEffect, useState } from \"react\";\r\nimport {\r\n  Box,\r\n  Button,\r\n  Menu,\r\n  MenuItem,\r\n  Typography,\r\n  IconButton,\r\n  CircularProgress,\r\n} from \"@mui/material\";\r\nimport ArrowDropDownIcon from \"@mui/icons-material/ArrowDropDown\";\r\nimport PlayArrowIcon from \"@mui/icons-material/PlayArrow\";\r\n// import { AccountContext } from \"../login/AccountContext\";\r\n// import { useAuth } from \"../auth/AuthProvider\";\r\nimport useDrawerStore from \"../../store/drawerStore\";\r\nimport { AccountContext } from \"../login/AccountContext\";\r\nimport { useAuth } from \"../auth/AuthProvider\";\r\nimport useInfoStore from \"../../store/UserInfoStore\";\r\nimport { GetOrganizationThemesByAccountId } from \"../../services/OrganizationService\";\r\nimport { getThemesByAccountId } from \"../../services/OrganizationService\";\r\n\r\n\r\ninterface ThemeStyles {\r\n  Typography: {\r\n    fontFamily: string;\r\n    fontSize: number;\r\n    fontColor: string;\r\n  };\r\n  Canvas: {\r\n    canvasPadding: number;\r\n    canvasRadius: number;\r\n    canvasBorderSize: number;\r\n    canvasBorderColor: string;\r\n    canvasBgColor: string;\r\n  };\r\n  Button: {\r\n    shape: \"square\" | \"curved\" | \"round\";\r\n    primaryBtnBg: string;\r\n    primaryBtnColor: string;\r\n    secondaryBtnBg: string;\r\n    secondaryBtnColor: string;\r\n  };\r\n}\r\n\r\ninterface OrganizationTheme {\r\n  Id: string;\r\n  organizationId: string;\r\n  accountId: string;\r\n  userId: string;\r\n  ThemeName: string;\r\n  themeDescription: string;\r\n  level: string;\r\n  IsActive: boolean;\r\n  ThemeStyles?: ThemeStyles; // made optional for safety\r\n}\r\n\r\nconst DETACHED_THEME: OrganizationTheme = {\r\n    Id: \"detached-theme\",\r\n    organizationId: \"\",\r\n    accountId: \"\",\r\n    userId: \"\",\r\n    ThemeName: \"Theme Detached\",\r\n    themeDescription: \"Unsaved changes\",\r\n    level: \"Interaction\",\r\n    IsActive: false,\r\n    ThemeStyles: undefined,\r\n  };\r\n  \r\n\r\nconst ThemeDropdown = () => {\r\n  const [themes, setThemes] = useState<OrganizationTheme[]>([]);\r\n  const [selectedThemes, setSelectedThemes] = useState<OrganizationTheme | null>(null);\r\n  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);\r\n  const [loading, setLoading] = useState(false);\r\n  const { setSelectedTheme, isUnSavedChanges, setIsThemeChanges , isThemeChanges } = useDrawerStore();\r\n  const { accountId } = useContext(AccountContext);\r\n  const { userDetails } = useAuth();\r\n  const orgDetails = useInfoStore((state) => state.orgDetails || \"{}\");\r\n\tconst organizationId = orgDetails.OrganizationId;\r\n\r\n  useEffect(() => {\r\n    const fetchThemes = async () => {\r\n      setLoading(true);\r\n      try {\r\n        const accId = accountId;\r\n        const orgId = organizationId;\r\n        const level = \"Interaction\";\r\n  \r\n        const data: OrganizationTheme[] = await GetOrganizationThemesByAccountId(accId, orgId, level);\r\n  \r\n        // ✅ Separate active and inactive themes\r\n        const activeThemes = data.filter(t => t.IsActive);\r\n        const inactiveThemes = data.filter(t => !t.IsActive);\r\n  \r\n        // ✅ Merge active first, then inactive\r\n        let sortedThemes = [...activeThemes, ...inactiveThemes];\r\n        if (isThemeChanges) {\r\n            sortedThemes = [DETACHED_THEME, ...sortedThemes];\r\n            setSelectedThemes(DETACHED_THEME);\r\n            setSelectedTheme(DETACHED_THEME);\r\n            \r\n          } else {\r\n            const selected = activeThemes.find(t => t.ThemeStyles?.Button) || sortedThemes[0];\r\n            setSelectedThemes(selected);\r\n            setSelectedTheme(selected);\r\n            setIsThemeChanges(false);\r\n\r\n\r\n          }\r\n        setThemes(sortedThemes); // Update list for dropdown\r\n\r\n        // ✅ Set first active theme, or fallback to first\r\n        \r\n  \r\n      } catch (err) {\r\n        console.error(\"❌ Theme fetch failed:\", err);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n  \r\n    fetchThemes();\r\n  }, [isThemeChanges]);\r\n  \r\n\r\n  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {\r\n    setAnchorEl(event.currentTarget);\r\n  };\r\n\r\n  const handleClose = () => setAnchorEl(null);\r\n\r\n  const handleSelectTheme = (theme: OrganizationTheme) => {\r\n\r\n    if (theme.Id !== \"detached-theme\") {\r\n        setThemes(prev => prev.filter(t => t.Id !== \"detached-theme\"));\r\n      }\r\n    setSelectedThemes(theme);\r\n    setSelectedTheme(theme);\r\n    handleClose();\r\n\r\n    // Apply or remove theme-custom class based on theme name\r\n    const bodyElement = document.body;\r\n    if (theme.ThemeName && theme.ThemeName !== \"Default\") {\r\n      bodyElement.classList.add(\"theme-custom\");\r\n    } else {\r\n      bodyElement.classList.remove(\"theme-custom\");\r\n    }\r\n\r\n    // Apply theme styles\r\n    if (theme.ThemeStyles) {\r\n      console.log('🎨 Applying Theme for:', theme.ThemeName);\r\n\r\n      // Apply theme styles as CSS variables\r\n      Object.entries(theme.ThemeStyles).forEach(([key, value]) => {\r\n        if (value) {\r\n          const cssVarName = `--Theme-${key}`;\r\n          document.documentElement.style.setProperty(cssVarName, value as string);\r\n        }\r\n      });\r\n\r\n      // Generate and apply opacity variants for primaryColor, secondaryColor, accentColor\r\n      const targetColors = ['primaryColor', 'secondaryColor', 'accentColor'];\r\n      const opacities = [10, 20, 30, 40, 50];\r\n      const themeStyles = theme.ThemeStyles as any;\r\n\r\n      targetColors.forEach(colorKey => {\r\n        const colorValue = themeStyles[colorKey];\r\n        if (colorValue && typeof colorValue === 'string') {\r\n          // Set the base color\r\n          document.documentElement.style.setProperty(`--${colorKey}`, colorValue);\r\n\r\n          // Get RGB values for color mixing\r\n          let baseR = 0, baseG = 0, baseB = 0;\r\n\r\n          // Convert color to RGB values\r\n          if (colorValue.startsWith('#')) {\r\n            const hex = colorValue.replace('#', '');\r\n            baseR = parseInt(hex.substring(0, 2), 16);\r\n            baseG = parseInt(hex.substring(2, 4), 16);\r\n            baseB = parseInt(hex.substring(4, 6), 16);\r\n          } else if (colorValue.startsWith('rgb')) {\r\n            const rgbMatch = colorValue.match(/rgba?\\((\\d+),\\s*(\\d+),\\s*(\\d+)(?:,\\s*([\\d.]+))?\\)/);\r\n            if (rgbMatch) {\r\n              baseR = parseInt(rgbMatch[1]);\r\n              baseG = parseInt(rgbMatch[2]);\r\n              baseB = parseInt(rgbMatch[3]);\r\n            }\r\n          }\r\n\r\n          // Generate opacity variants\r\n          opacities.forEach(opacity => {\r\n            const alpha = opacity / 100;\r\n            const rgbaValue = `rgba(${baseR}, ${baseG}, ${baseB}, ${alpha})`;\r\n            const varName = `--${colorKey}-${opacity}`;\r\n            document.documentElement.style.setProperty(varName, rgbaValue);\r\n          });\r\n\r\n          // Generate white-mixed variants (lighter tints)\r\n          opacities.forEach(whitePercent => {\r\n            const whiteMix = whitePercent / 100;\r\n            // Mix with white: newColor = originalColor * (1 - whiteMix) + white * whiteMix\r\n            const mixedR = Math.round(baseR * (1 - whiteMix) + 255 * whiteMix);\r\n            const mixedG = Math.round(baseG * (1 - whiteMix) + 255 * whiteMix);\r\n            const mixedB = Math.round(baseB * (1 - whiteMix) + 255 * whiteMix);\r\n\r\n            const whiteVariantValue = `rgb(${mixedR}, ${mixedG}, ${mixedB})`;\r\n            const whiteVarName = `--${colorKey}-white-${whitePercent}`;\r\n            document.documentElement.style.setProperty(whiteVarName, whiteVariantValue);\r\n          });\r\n        }\r\n      });\r\n    }\r\n\r\n    // Optional: Apply globally via context\r\n    // dispatch({ type: \"SET_THEME\", payload: theme.ThemeStyles });\r\n  };\r\n\r\n  const getBtnColor = (theme: OrganizationTheme, key: \"primaryBtnBg\" | \"secondaryBtnBg\") =>\r\n    theme?.ThemeStyles?.Button?.[key] || \"#ccc\";\r\n\r\n\r\n\r\n  return (\r\n    <Box display=\"flex\" alignItems=\"center\" gap={1}>\r\n      <Button\r\n        variant=\"outlined\"\r\n        onClick={handleClick}\r\n        endIcon={<ArrowDropDownIcon />}\r\n        startIcon={\r\n          selectedThemes?.ThemeStyles?.Button?.primaryBtnBg ? (\r\n            <Box\r\n              sx={{\r\n                width: 12,\r\n                height: 12,\r\n                borderRadius: \"50%\",\r\n                backgroundColor: selectedThemes.ThemeStyles.Button.primaryBtnBg,\r\n              }}\r\n            />\r\n          ) : null\r\n        }\r\n        sx={{ textTransform: \"none\", borderRadius: 3 }}\r\n        disabled={loading}\r\n      >\r\n        {loading ? <CircularProgress size={18} /> : selectedThemes?.ThemeName || \"Select Theme\"}\r\n      </Button>\r\n\r\n      \r\n\r\n      <Menu anchorEl={anchorEl} open={Boolean(anchorEl)} onClose={handleClose}>\r\n        {themes.map((theme) => (\r\n          <MenuItem\r\n            key={theme.Id}\r\n            onClick={() => \r\n                {handleSelectTheme(theme)\r\n                    setIsThemeChanges(false);\r\n\r\n                }\r\n            }\r\n            selected={selectedThemes?.Id === theme.Id}\r\n          >\r\n            <Box display=\"flex\" alignItems=\"center\" gap={1}>\r\n              <Typography>{theme.ThemeName}</Typography>\r\n              <Box display=\"flex\" gap={0.5}>\r\n                <Box\r\n                  sx={{\r\n                    width: 12,\r\n                    height: 12,\r\n                    borderRadius: \"50%\",\r\n                    backgroundColor: getBtnColor(theme, \"primaryBtnBg\"),\r\n                  }}\r\n                />\r\n                <Box\r\n                  sx={{\r\n                    width: 12,\r\n                    height: 12,\r\n                    borderRadius: \"50%\",\r\n                    backgroundColor: getBtnColor(theme, \"secondaryBtnBg\"),\r\n                  }}\r\n                />\r\n              </Box>\r\n            </Box>\r\n          </MenuItem>\r\n        ))}\r\n      </Menu>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default ThemeDropdown;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC9D,SACEC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,UAAU,EAEVC,gBAAgB,QACX,eAAe;AACtB,OAAOC,iBAAiB,MAAM,mCAAmC;AAEjE;AACA;AACA,OAAOC,cAAc,MAAM,yBAAyB;AACpD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,OAAOC,YAAY,MAAM,2BAA2B;AACpD,SAASC,gCAAgC,QAAQ,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAsCtF,MAAMC,cAAiC,GAAG;EACtCC,EAAE,EAAE,gBAAgB;EACpBC,cAAc,EAAE,EAAE;EAClBC,SAAS,EAAE,EAAE;EACbC,MAAM,EAAE,EAAE;EACVC,SAAS,EAAE,gBAAgB;EAC3BC,gBAAgB,EAAE,iBAAiB;EACnCC,KAAK,EAAE,aAAa;EACpBC,QAAQ,EAAE,KAAK;EACfC,WAAW,EAAEC;AACf,CAAC;AAGH,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EAC1B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG/B,QAAQ,CAAsB,EAAE,CAAC;EAC7D,MAAM,CAACgC,cAAc,EAAEC,iBAAiB,CAAC,GAAGjC,QAAQ,CAA2B,IAAI,CAAC;EACpF,MAAM,CAACkC,QAAQ,EAAEC,WAAW,CAAC,GAAGnC,QAAQ,CAAqB,IAAI,CAAC;EAClE,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM;IAAEsC,gBAAgB;IAAEC,gBAAgB;IAAEC,iBAAiB;IAAGC;EAAe,CAAC,GAAGjC,cAAc,CAAC,CAAC;EACnG,MAAM;IAAEU;EAAU,CAAC,GAAGpB,UAAU,CAACW,cAAc,CAAC;EAChD,MAAM;IAAEiC;EAAY,CAAC,GAAGhC,OAAO,CAAC,CAAC;EACjC,MAAMiC,UAAU,GAAGhC,YAAY,CAAEiC,KAAK,IAAKA,KAAK,CAACD,UAAU,IAAI,IAAI,CAAC;EACrE,MAAM1B,cAAc,GAAG0B,UAAU,CAACE,cAAc;EAE/C9C,SAAS,CAAC,MAAM;IACd,MAAM+C,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9BT,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF,MAAMU,KAAK,GAAG7B,SAAS;QACvB,MAAM8B,KAAK,GAAG/B,cAAc;QAC5B,MAAMK,KAAK,GAAG,aAAa;QAE3B,MAAM2B,IAAyB,GAAG,MAAMrC,gCAAgC,CAACmC,KAAK,EAAEC,KAAK,EAAE1B,KAAK,CAAC;;QAE7F;QACA,MAAM4B,YAAY,GAAGD,IAAI,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC7B,QAAQ,CAAC;QACjD,MAAM8B,cAAc,GAAGJ,IAAI,CAACE,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAAC7B,QAAQ,CAAC;;QAEpD;QACA,IAAI+B,YAAY,GAAG,CAAC,GAAGJ,YAAY,EAAE,GAAGG,cAAc,CAAC;QACvD,IAAIZ,cAAc,EAAE;UAChBa,YAAY,GAAG,CAACvC,cAAc,EAAE,GAAGuC,YAAY,CAAC;UAChDrB,iBAAiB,CAAClB,cAAc,CAAC;UACjCuB,gBAAgB,CAACvB,cAAc,CAAC;QAElC,CAAC,MAAM;UACL,MAAMwC,QAAQ,GAAGL,YAAY,CAACM,IAAI,CAACJ,CAAC;YAAA,IAAAK,cAAA;YAAA,QAAAA,cAAA,GAAIL,CAAC,CAAC5B,WAAW,cAAAiC,cAAA,uBAAbA,cAAA,CAAevD,MAAM;UAAA,EAAC,IAAIoD,YAAY,CAAC,CAAC,CAAC;UACjFrB,iBAAiB,CAACsB,QAAQ,CAAC;UAC3BjB,gBAAgB,CAACiB,QAAQ,CAAC;UAC1Bf,iBAAiB,CAAC,KAAK,CAAC;QAG1B;QACFT,SAAS,CAACuB,YAAY,CAAC,CAAC,CAAC;;QAEzB;MAGF,CAAC,CAAC,OAAOI,GAAG,EAAE;QACZC,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEF,GAAG,CAAC;MAC7C,CAAC,SAAS;QACRrB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDS,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACL,cAAc,CAAC,CAAC;EAGpB,MAAMoB,WAAW,GAAIC,KAA0C,IAAK;IAClE3B,WAAW,CAAC2B,KAAK,CAACC,aAAa,CAAC;EAClC,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM7B,WAAW,CAAC,IAAI,CAAC;EAE3C,MAAM8B,iBAAiB,GAAIC,KAAwB,IAAK;IAEtD,IAAIA,KAAK,CAAClD,EAAE,KAAK,gBAAgB,EAAE;MAC/Be,SAAS,CAACoC,IAAI,IAAIA,IAAI,CAAChB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACpC,EAAE,KAAK,gBAAgB,CAAC,CAAC;IAChE;IACFiB,iBAAiB,CAACiC,KAAK,CAAC;IACxB5B,gBAAgB,CAAC4B,KAAK,CAAC;IACvBF,WAAW,CAAC,CAAC;;IAEb;IACA,MAAMI,WAAW,GAAGC,QAAQ,CAACC,IAAI;IACjC,IAAIJ,KAAK,CAAC9C,SAAS,IAAI8C,KAAK,CAAC9C,SAAS,KAAK,SAAS,EAAE;MACpDgD,WAAW,CAACG,SAAS,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3C,CAAC,MAAM;MACLJ,WAAW,CAACG,SAAS,CAACE,MAAM,CAAC,cAAc,CAAC;IAC9C;;IAEA;IACA,IAAIP,KAAK,CAAC1C,WAAW,EAAE;MACrBmC,OAAO,CAACe,GAAG,CAAC,wBAAwB,EAAER,KAAK,CAAC9C,SAAS,CAAC;;MAEtD;MACAuD,MAAM,CAACC,OAAO,CAACV,KAAK,CAAC1C,WAAW,CAAC,CAACqD,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;QAC1D,IAAIA,KAAK,EAAE;UACT,MAAMC,UAAU,GAAG,WAAWF,GAAG,EAAE;UACnCT,QAAQ,CAACY,eAAe,CAACC,KAAK,CAACC,WAAW,CAACH,UAAU,EAAED,KAAe,CAAC;QACzE;MACF,CAAC,CAAC;;MAEF;MACA,MAAMK,YAAY,GAAG,CAAC,cAAc,EAAE,gBAAgB,EAAE,aAAa,CAAC;MACtE,MAAMC,SAAS,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MACtC,MAAMC,WAAW,GAAGpB,KAAK,CAAC1C,WAAkB;MAE5C4D,YAAY,CAACP,OAAO,CAACU,QAAQ,IAAI;QAC/B,MAAMC,UAAU,GAAGF,WAAW,CAACC,QAAQ,CAAC;QACxC,IAAIC,UAAU,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;UAChD;UACAnB,QAAQ,CAACY,eAAe,CAACC,KAAK,CAACC,WAAW,CAAC,KAAKI,QAAQ,EAAE,EAAEC,UAAU,CAAC;;UAEvE;UACA,IAAIC,KAAK,GAAG,CAAC;YAAEC,KAAK,GAAG,CAAC;YAAEC,KAAK,GAAG,CAAC;;UAEnC;UACA,IAAIH,UAAU,CAACI,UAAU,CAAC,GAAG,CAAC,EAAE;YAC9B,MAAMC,GAAG,GAAGL,UAAU,CAACM,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;YACvCL,KAAK,GAAGM,QAAQ,CAACF,GAAG,CAACG,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;YACzCN,KAAK,GAAGK,QAAQ,CAACF,GAAG,CAACG,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;YACzCL,KAAK,GAAGI,QAAQ,CAACF,GAAG,CAACG,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;UAC3C,CAAC,MAAM,IAAIR,UAAU,CAACI,UAAU,CAAC,KAAK,CAAC,EAAE;YACvC,MAAMK,QAAQ,GAAGT,UAAU,CAACU,KAAK,CAAC,mDAAmD,CAAC;YACtF,IAAID,QAAQ,EAAE;cACZR,KAAK,GAAGM,QAAQ,CAACE,QAAQ,CAAC,CAAC,CAAC,CAAC;cAC7BP,KAAK,GAAGK,QAAQ,CAACE,QAAQ,CAAC,CAAC,CAAC,CAAC;cAC7BN,KAAK,GAAGI,QAAQ,CAACE,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC/B;UACF;;UAEA;UACAZ,SAAS,CAACR,OAAO,CAACsB,OAAO,IAAI;YAC3B,MAAMC,KAAK,GAAGD,OAAO,GAAG,GAAG;YAC3B,MAAME,SAAS,GAAG,QAAQZ,KAAK,KAAKC,KAAK,KAAKC,KAAK,KAAKS,KAAK,GAAG;YAChE,MAAME,OAAO,GAAG,KAAKf,QAAQ,IAAIY,OAAO,EAAE;YAC1C9B,QAAQ,CAACY,eAAe,CAACC,KAAK,CAACC,WAAW,CAACmB,OAAO,EAAED,SAAS,CAAC;UAChE,CAAC,CAAC;;UAEF;UACAhB,SAAS,CAACR,OAAO,CAAC0B,YAAY,IAAI;YAChC,MAAMC,QAAQ,GAAGD,YAAY,GAAG,GAAG;YACnC;YACA,MAAME,MAAM,GAAGC,IAAI,CAACC,KAAK,CAAClB,KAAK,IAAI,CAAC,GAAGe,QAAQ,CAAC,GAAG,GAAG,GAAGA,QAAQ,CAAC;YAClE,MAAMI,MAAM,GAAGF,IAAI,CAACC,KAAK,CAACjB,KAAK,IAAI,CAAC,GAAGc,QAAQ,CAAC,GAAG,GAAG,GAAGA,QAAQ,CAAC;YAClE,MAAMK,MAAM,GAAGH,IAAI,CAACC,KAAK,CAAChB,KAAK,IAAI,CAAC,GAAGa,QAAQ,CAAC,GAAG,GAAG,GAAGA,QAAQ,CAAC;YAElE,MAAMM,iBAAiB,GAAG,OAAOL,MAAM,KAAKG,MAAM,KAAKC,MAAM,GAAG;YAChE,MAAME,YAAY,GAAG,KAAKxB,QAAQ,UAAUgB,YAAY,EAAE;YAC1DlC,QAAQ,CAACY,eAAe,CAACC,KAAK,CAACC,WAAW,CAAC4B,YAAY,EAAED,iBAAiB,CAAC;UAC7E,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;;IAEA;IACA;EACF,CAAC;EAED,MAAME,WAAW,GAAGA,CAAC9C,KAAwB,EAAEY,GAAsC;IAAA,IAAAmC,kBAAA,EAAAC,qBAAA;IAAA,OACnF,CAAAhD,KAAK,aAALA,KAAK,wBAAA+C,kBAAA,GAAL/C,KAAK,CAAE1C,WAAW,cAAAyF,kBAAA,wBAAAC,qBAAA,GAAlBD,kBAAA,CAAoB/G,MAAM,cAAAgH,qBAAA,uBAA1BA,qBAAA,CAA6BpC,GAAG,CAAC,KAAI,MAAM;EAAA;EAI7C,oBACEhE,OAAA,CAACb,GAAG;IAACkH,OAAO,EAAC,MAAM;IAACC,UAAU,EAAC,QAAQ;IAACC,GAAG,EAAE,CAAE;IAAAC,QAAA,gBAC7CxG,OAAA,CAACZ,MAAM;MACLqH,OAAO,EAAC,UAAU;MAClBC,OAAO,EAAE3D,WAAY;MACrB4D,OAAO,eAAE3G,OAAA,CAACP,iBAAiB;QAAAmH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAC/BC,SAAS,EACP9F,cAAc,aAAdA,cAAc,gBAAAJ,qBAAA,GAAdI,cAAc,CAAER,WAAW,cAAAI,qBAAA,gBAAAC,sBAAA,GAA3BD,qBAAA,CAA6B1B,MAAM,cAAA2B,sBAAA,eAAnCA,sBAAA,CAAqCkG,YAAY,gBAC/CjH,OAAA,CAACb,GAAG;QACF+H,EAAE,EAAE;UACFC,KAAK,EAAE,EAAE;UACTC,MAAM,EAAE,EAAE;UACVC,YAAY,EAAE,KAAK;UACnBC,eAAe,EAAEpG,cAAc,CAACR,WAAW,CAACtB,MAAM,CAAC6H;QACrD;MAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,GACA,IACL;MACDG,EAAE,EAAE;QAAEK,aAAa,EAAE,MAAM;QAAEF,YAAY,EAAE;MAAE,CAAE;MAC/CG,QAAQ,EAAElG,OAAQ;MAAAkF,QAAA,EAEjBlF,OAAO,gBAAGtB,OAAA,CAACR,gBAAgB;QAACiI,IAAI,EAAE;MAAG;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,GAAG,CAAA7F,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEZ,SAAS,KAAI;IAAc;MAAAsG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjF,CAAC,eAIT/G,OAAA,CAACX,IAAI;MAAC+B,QAAQ,EAAEA,QAAS;MAACsG,IAAI,EAAEC,OAAO,CAACvG,QAAQ,CAAE;MAACwG,OAAO,EAAE1E,WAAY;MAAAsD,QAAA,EACrExF,MAAM,CAAC6G,GAAG,CAAEzE,KAAK,iBAChBpD,OAAA,CAACV,QAAQ;QAEPoH,OAAO,EAAEA,CAAA,KACL;UAACvD,iBAAiB,CAACC,KAAK,CAAC;UACrB1B,iBAAiB,CAAC,KAAK,CAAC;QAE5B,CACH;QACDe,QAAQ,EAAE,CAAAvB,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEhB,EAAE,MAAKkD,KAAK,CAAClD,EAAG;QAAAsG,QAAA,eAE1CxG,OAAA,CAACb,GAAG;UAACkH,OAAO,EAAC,MAAM;UAACC,UAAU,EAAC,QAAQ;UAACC,GAAG,EAAE,CAAE;UAAAC,QAAA,gBAC7CxG,OAAA,CAACT,UAAU;YAAAiH,QAAA,EAAEpD,KAAK,CAAC9C;UAAS;YAAAsG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC1C/G,OAAA,CAACb,GAAG;YAACkH,OAAO,EAAC,MAAM;YAACE,GAAG,EAAE,GAAI;YAAAC,QAAA,gBAC3BxG,OAAA,CAACb,GAAG;cACF+H,EAAE,EAAE;gBACFC,KAAK,EAAE,EAAE;gBACTC,MAAM,EAAE,EAAE;gBACVC,YAAY,EAAE,KAAK;gBACnBC,eAAe,EAAEpB,WAAW,CAAC9C,KAAK,EAAE,cAAc;cACpD;YAAE;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACF/G,OAAA,CAACb,GAAG;cACF+H,EAAE,EAAE;gBACFC,KAAK,EAAE,EAAE;gBACTC,MAAM,EAAE,EAAE;gBACVC,YAAY,EAAE,KAAK;gBACnBC,eAAe,EAAEpB,WAAW,CAAC9C,KAAK,EAAE,gBAAgB;cACtD;YAAE;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GA7BD3D,KAAK,CAAClD,EAAE;QAAA0G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA8BL,CACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAClG,EAAA,CAzNID,aAAa;EAAA,QAKkElB,cAAc,EAEzEE,OAAO,EACZC,YAAY;AAAA;AAAAiI,EAAA,GAR3BlH,aAAa;AA2NnB,eAAeA,aAAa;AAAC,IAAAkH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}