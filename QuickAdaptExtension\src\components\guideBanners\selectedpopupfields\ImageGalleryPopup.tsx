import React, { useState } from "react";
import { Box, Dialog, DialogTitle, Typography, TextField, IconButton, Grid } from "@mui/material";
import FolderIcon from "@mui/icons-material/Folder";
import SearchIcon from "@mui/icons-material/Search";
import FilterListIcon from "@mui/icons-material/FilterList";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import { backicon } from "../../../assets/icons/icons";

const dummyImages = Array(9).fill({
	name: "Image.jpg",
	size: "12mb",
	src: "https://via.placeholder.com/150",
});

const ImageGalleryPopup: React.FC<{ open: boolean; onClose: () => void }> = ({ open, onClose }) => {
	const [searchQuery, setSearchQuery] = useState<string>("");

	const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
		setSearchQuery(event.target.value);
	};

	return (
		<Dialog
			open={open}
			onClose={onClose}
			fullWidth
			maxWidth="xs"
			PaperProps={{
				style: { borderRadius: "10px", padding: "10px", marginTop: "140px", width: "200px" },
			}}
		>
			<IconButton
				className="qadpt-banner-button qadpt-icon"
				onClick={onClose}
			>
				<span dangerouslySetInnerHTML={{ __html: backicon }} />
			</IconButton>
			<DialogTitle>
				<Typography
					variant="h6"
					textAlign="center"
				>
					Folder 1
				</Typography>
			</DialogTitle>

			<Box
				display="flex"
				alignItems="center"
				justifyContent="space-between"
				px={2}
				pb={1}
			>
				<TextField
					variant="outlined"
					size="small"
					placeholder="Search"
					value={searchQuery}
					onChange={handleSearchChange}
					InputProps={{
						startAdornment: (
							<IconButton>
								<SearchIcon />
							</IconButton>
						),
					}}
				/>
				<IconButton>
					<FilterListIcon />
				</IconButton>
			</Box>

			<Box p={2}>
				<Grid
					container
					spacing={2}
				>
					{dummyImages.map((image, index) => (
						<Grid
							item
							xs={6}
							key={index}
						>
							<Box
								display="flex"
								flexDirection="column"
								justifyContent="center"
								alignItems="center"
								sx={{ cursor: "pointer" }}
							>
								<img
									src={image.src}
									alt={image.name}
									style={{ width: "100%", borderRadius: "5px" }}
								/>
								<Typography>{image.name}</Typography>
								<Typography
									fontSize="small"
									color="textSecondary"
								>
									{image.size}
								</Typography>
							</Box>
						</Grid>
					))}
				</Grid>
			</Box>
		</Dialog>
	);
};

export default ImageGalleryPopup;
