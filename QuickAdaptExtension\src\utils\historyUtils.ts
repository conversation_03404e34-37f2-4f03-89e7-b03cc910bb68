import useHistoryStore, { HistoryEntry } from "../store/historyStore";
import useDrawerStore from "../store/drawerStore";
import { produce } from "immer";

// Helper function to create a deep clone of an object
export const deepClone = <T>(obj: T): T => {
  try {
    // Handle null, undefined, or primitive values
    if (obj === null || obj === undefined || typeof obj !== 'object') {
      return obj;
    }

    return JSON.parse(JSON.stringify(obj));
  } catch (error) {
    console.error('Error in deepClone:', error);
    // Return a safe fallback (empty object or array)
    return Array.isArray(obj) ? [] as unknown as T : {} as T;
  }
};

// Helper function to extract relevant state for history tracking
export const extractStateForHistory = (state: any, targetPath?: string[]) => {
  // If no specific path is provided, return a clone of the entire state
  if (!targetPath || targetPath.length === 0) {
    return deepClone(state);
  }

  // Extract only the specified path
  let result = state;
  for (const key of targetPath) {
    if (result && typeof result === 'object' && key in result) {
      result = result[key];
    } else {
      return undefined;
    }
  }

  return deepClone(result);
};

// Function to apply a history entry (either undo or redo)
export const applyHistoryEntry = (entry: HistoryEntry, isUndo: boolean) => {
  const drawerStore = useDrawerStore.getState();
  const set = useDrawerStore.setState;

  // Determine which state to apply based on whether we're undoing or redoing
  const stateToApply = isUndo ? entry.stateBefore : entry.stateAfter;

  // If the entry has a targetId, we need to update a specific part of the state
  if (entry.targetId) {
    switch (entry.actionType) {
      case 'CANVAS_UPDATE':
        // Apply canvas settings
        if (entry.metadata?.guideType === 'Announcement') {
          set(produce(drawerStore, draft => {
            const stepIndex = draft.announcementJson.GuideStep.findIndex(
              (step: any) => step.stepName === entry.metadata?.currentStep
            );

            if (stepIndex !== -1) {
              draft.announcementJson.GuideStep[stepIndex].Canvas = stateToApply;
            }
          }));
        } else if (entry.metadata?.guideType === 'Tooltip') {
          set(produce(drawerStore, draft => {
            const stepIndex = draft.currentStep - 1;
            if (draft.toolTipGuideMetaData[stepIndex]) {
              draft.toolTipGuideMetaData[stepIndex].canvas = stateToApply;
            }
          }));
        } else if (entry.metadata?.guideType === 'Banner') {
          set(produce(drawerStore, draft => {
            const stepIndex = draft.bannerJson.GuideStep.findIndex(
              (step: any) => step.stepName === entry.metadata?.currentStep
            );

            if (stepIndex !== -1) {
              draft.bannerJson.GuideStep[stepIndex].Canvas = stateToApply;
            }
          }));
        }
        break;

      case 'ELEMENT_UPDATE':
      case 'ELEMENT_BATCH_UPDATE':
        // Apply element settings
        set(produce(drawerStore, draft => {
          try {
            // Create a safe copy of the state to apply
            const safeStateToApply = stateToApply ? JSON.parse(JSON.stringify(stateToApply)) : {};

            if (safeStateToApply && typeof safeStateToApply === 'object' && safeStateToApply.dismissData) {
              // Handle new format with complete state
              draft.dismissData = safeStateToApply.dismissData;
              draft.progress = safeStateToApply.progress;
              draft.selectedOption = safeStateToApply.selectedOption;
              draft.dismiss = safeStateToApply.dismiss;

              // Handle progress color if it exists
              if (safeStateToApply.progressColor !== undefined) {
                draft.ProgressColor = safeStateToApply.progressColor;
              }

              // Also update the tooltip guide metadata if needed
              if (entry.metadata?.guideType === 'Tooltip' && entry.metadata?.currentStep) {
                const stepIndex = entry.metadata.currentStep - 1;
                if (draft.toolTipGuideMetaData && draft.toolTipGuideMetaData[stepIndex]) {
                  if (!draft.toolTipGuideMetaData[stepIndex].design) {
                    // Initialize with required properties
                    draft.toolTipGuideMetaData[stepIndex].design = {
                      gotoNext: {
                        ButtonId: '',
                        ElementPath: '',
                        NextStep: '',
                        ButtonName: ''
                      },
                      element: {
                        progress: '',
                        isDismiss: false
                      }
                    };
                  }
                  // Now update the element property
                  draft.toolTipGuideMetaData[stepIndex].design.element = {
                    progress: safeStateToApply.progress ? safeStateToApply.progress.toString() : '',
                    isDismiss: safeStateToApply.dismiss
                  };
                }
              }
            } else {
              // Handle legacy format
              draft.dismissData = safeStateToApply;
              draft.progress = entry.metadata?.progress;
              draft.selectedOption = entry.metadata?.selectedOption;
              draft.dismiss = entry.metadata?.dismiss;

              // Handle progress color if it exists in metadata
              if (entry.metadata?.progressColor !== undefined) {
                draft.ProgressColor = entry.metadata.progressColor;
              }
            }
          } catch (error) {
            console.error('Error applying element update:', error);
          }
        }));
        break;

      case 'OVERLAY_UPDATE':
      case 'OVERLAY_BATCH_UPDATE':
        // Apply overlay settings
        set(produce(drawerStore, draft => {
          if (typeof stateToApply === 'boolean') {
            // Handle legacy format
            draft.overlayEnabled = stateToApply;
          } else if (stateToApply && typeof stateToApply === 'object') {
            // Handle new format with both overlay and page interaction
            draft.overlayEnabled = stateToApply.overlayEnabled;
            if (stateToApply.pageinteraction !== undefined) {
              draft.pageinteraction = stateToApply.pageinteraction;
            }
          }
        }));
        break;

      case 'BANNER_CANVAS_UPDATE':
        // Apply banner canvas settings
        set(produce(drawerStore, draft => {
          // Update banner-specific properties
          draft.Bposition = stateToApply.Position;
          draft.backgroundC = stateToApply.BackgroundColor;
          draft.bpadding = stateToApply.Padding;
          draft.Bbordercolor = stateToApply.BorderColor;
          draft.BborderSize = stateToApply.BorderSize;
          if (stateToApply.sectionColor) {
            draft.sectionColor = stateToApply.sectionColor;
          }

          // Also update the guide data
          if (draft.bannerJson?.GuideStep?.[0]) {
            draft.bannerJson.GuideStep[0].Canvas = stateToApply;
          }
        }));
        break;

      case 'TOOLTIP_CANVAS_UPDATE':
        // Apply tooltip canvas settings
        set(produce(drawerStore, draft => {
          // Update tooltip-specific properties
          const stepIndex = entry.metadata?.currentStep - 1;
          if (stepIndex >= 0 && draft.toolTipGuideMetaData[stepIndex]) {
            // Update tooltip canvas settings
            draft.toolTipGuideMetaData[stepIndex].canvas = stateToApply;

            // Also update the individual properties in the store
            draft.tooltipPosition = stateToApply.position;
            draft.tooltipBackgroundcolor = stateToApply.backgroundColor;
            draft.tooltipWidth = stateToApply.width;
            draft.tooltipborderradius = stateToApply.borderRadius;
            draft.tooltippadding = stateToApply.padding;
            draft.tooltipBordercolor = stateToApply.borderColor;
            draft.tooltipbordersize = stateToApply.borderSize;
            draft.autoPosition = stateToApply.autoposition;
            draft.tooltipXaxis = stateToApply.xaxis;
            draft.tooltipYaxis = stateToApply.yaxis;
          }
        }));
        break;

      case 'CHECKLIST_CANVAS_UPDATE':
        // Apply checklist canvas settings
        set(produce(drawerStore, draft => {
          if (draft.checklistGuideMetaData && draft.checklistGuideMetaData[0]) {
            // Update checklist canvas settings
            draft.checklistGuideMetaData[0].canvas = stateToApply;
          }
        }));
        break;

      case 'BUTTON_UPDATE':
        // Apply button updates
        set(produce(drawerStore, draft => {
          const containerIndex = draft.buttonsContainer.findIndex(
            (container: any) => container.id === entry.metadata?.containerId
          );

          if (containerIndex !== -1) {
            const buttonIndex = draft.buttonsContainer[containerIndex].buttons.findIndex(
              (button: any) => button.id === entry.metadata?.buttonId
            );

            if (buttonIndex !== -1) {
              draft.buttonsContainer[containerIndex].buttons[buttonIndex] = stateToApply;
            }
          }
        }));
        break;

      case 'TEXT_UPDATE':
        // Apply text updates
        set(produce(drawerStore, draft => {
          const containerIndex = draft.rtesContainer.findIndex(
            (container: any) => container.id === entry.metadata?.containerId
          );

          if (containerIndex !== -1) {
            const rteIndex = draft.rtesContainer[containerIndex].rtes.findIndex(
              (rte: any) => rte.id === entry.metadata?.rteId
            );

            if (rteIndex !== -1) {
              draft.rtesContainer[containerIndex].rtes[rteIndex] = stateToApply;
            }
          }
        }));
        break;

      case 'IMAGE_UPDATE':
        // Apply image updates
        set(produce(drawerStore, draft => {
          const containerIndex = draft.imagesContainer.findIndex(
            (container: any) => container.id === entry.metadata?.containerId
          );

          if (containerIndex !== -1) {
            const imageIndex = draft.imagesContainer[containerIndex].images.findIndex(
              (image: any) => image.id === entry.metadata?.imageId
            );

            if (imageIndex !== -1) {
              draft.imagesContainer[containerIndex].images[imageIndex] = stateToApply;
            }
          }
        }));
        break;

      case 'STEP_ADD':
        // Restore steps for step addition/removal
        set(produce(drawerStore, draft => {
          draft.steps = stateToApply;
          if (entry.metadata?.guideType === 'Tooltip') {
            draft.toolTipGuideMetaData = entry.metadata?.toolTipGuideMetaData;
          } else if (entry.metadata?.guideType === 'Announcement') {
            draft.announcementJson.GuideStep = entry.metadata?.guideSteps;
          } else if (entry.metadata?.guideType === 'Banner') {
            draft.bannerJson.GuideStep = entry.metadata?.guideSteps;
          }
        }));
        break;

      case 'STEP_REMOVE':
        // Restore steps for step removal
        set(produce(drawerStore, draft => {
          draft.steps = stateToApply;
          if (entry.metadata?.guideType === 'Tooltip') {
            draft.toolTipGuideMetaData = entry.metadata?.toolTipGuideMetaData;
          } else if (entry.metadata?.guideType === 'Announcement') {
            draft.announcementJson.GuideStep = entry.metadata?.guideSteps;
          } else if (entry.metadata?.guideType === 'Banner') {
            draft.bannerJson.GuideStep = entry.metadata?.guideSteps;
          }

          // Restore current step if needed
          if (entry.metadata?.currentStep) {
            draft.currentStep = entry.metadata.currentStep;
          }
        }));
        break;

      case 'STEP_RENAME':
        // Apply step rename
        set(produce(drawerStore, draft => {
          const stepIndex = draft.steps.findIndex(
            (step: any) => step.id === entry.targetId
          );

          if (stepIndex !== -1) {
            draft.steps[stepIndex].name = stateToApply.name;
            draft.steps[stepIndex].stepDescription = stateToApply.description;

            // Update in guide metadata if needed
            if (draft.selectedTemplate === 'Tooltip' || draft.selectedTemplateTour === 'Tooltip') {
              if (draft.toolTipGuideMetaData[stepIndex]) {
                draft.toolTipGuideMetaData[stepIndex].stepName = stateToApply.name;
                draft.toolTipGuideMetaData[stepIndex].stepDescription = stateToApply.description;
              }
            }
          }
        }));
        break;

      default:
        console.warn(`Unknown action type: ${entry.actionType}`);
    }
  } else {
    // If no targetId, apply the entire state (careful with this!)
    set(stateToApply);
  }
};

// Function to record a change to the history
export const recordChange = (
  actionType: string,
  description: string,
  stateBefore: any,
  stateAfter: any,
  targetId?: string,
  metadata?: Record<string, any>
) => {
  try {
    const historyStore = useHistoryStore.getState();

    // Create safe copies of the state objects
    const safeStateBefore = stateBefore ? deepClone(stateBefore) : {};
    const safeStateAfter = stateAfter ? deepClone(stateAfter) : {};
    const safeMetadata = metadata ? deepClone(metadata) : {};

    historyStore.recordChange({
      actionType,
      description,
      stateBefore: safeStateBefore,
      stateAfter: safeStateAfter,
      targetId,
      metadata: safeMetadata
    });
  } catch (error) {
    console.error('Error recording change:', error);
  }
};
