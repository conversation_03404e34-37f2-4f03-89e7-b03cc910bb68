import React, { useState, useMemo, useEffect } from "react";
import TooltipGuide from "../Tooltips/Tooltips";
//import { IReponse, useFetch } from "../../hooks/useFetch";
import { useLocation } from "react-router-dom";
//import MenuBar from "../Tooltips/Menubarfortest";

import useDrawerStore, { DrawerState } from "../../../../store/drawerStore";

interface Guide {
	GuideId: string;
	DontShowAgain: boolean;
}
const TooltiplastUserview = ({
	guideStep,
	anchorEl,
	onClose,
	onPrevious,
	onContinue,
	title,
	text,
	imageUrl,
	videoUrl,
	previousButtonLabel,
	continueButtonLabel,
	currentStep,
	totalSteps,
	onDontShowAgain,
	progress,
	textFieldProperties,
	imageProperties,
	customButton,
	modalProperties,
	canvasProperties,
	htmlSnippet,
	previousButtonStyles,
	continueButtonStyles,
	OverlayValue,
	savedGuideData,
}: //hotspotProperties,
any) => {
	//const { setCurrentStep } = useDrawerStore((state: DrawerState) => state);
	// const location = useLocation();
	const [currentUrl, setCurrentUrl] = useState(window.location.href);
	const [showTooltip, setShowTooltip] = useState(true);
	// const [response, setResponse] = useState<IReponse>({
	// 	data: [],
	// 	loading: false,
	// 	error: {
	// 		message: "",
	// 		isError: false,
	// 	},
	// });
	// const [response] = useFetch({
	//   // url: "/Guide/GetGuideDetails?guideId=09102024-180802907-0097902d-ecb8-4333-a70a-ba151cc641a6",
	//   url: `/EndUserGuide/GetGuideListByTargetUrl?targetUrl=${currentUrl}`,
	// });
	// const [guideDetails] = useFetch({
	// 	url: `/EndUserGuide/GetGuideListByTargetUrl?targetUrl=${currentUrl}`,
	// });

	useEffect(() => {
		if (savedGuideData && savedGuideData && savedGuideData.length > 0) {
			savedGuideData = savedGuideData.filter((x: any) => x.GuideType === "Tooltip" && x.TargetUrl === currentUrl);

			// setResponse((prev) => ({
			// 	...prev,
			// 	data: savedGuideData.data,
			// 	loading: savedGuideData.loading,
			// 	error: savedGuideData.error,
			// }));
		}
	}, [savedGuideData, currentUrl]);

	const data = savedGuideData || "";
	const xpathUrl = data?.TargetUrl;
	// Check if GuideStep exists before calling map
	const steps = (data?.GuideStep || []).map((step: any, stepIndex: number) => {
		const stepData = {
			xpath: step.ElementPath,
			content: step.TextFieldProperties || "",
			imageUrl: step.ImageProperties?.[0]?.CustomImage?.[0]?.Url || "",
			buttonData: step.ButtonSection?.[0]?.CustomButtons || [],
			targetUrl: step.StepTargetURL || xpathUrl || "", // Use individual step URL if available, fallback to guide URL
			overlay: step.Overlay,
			positionXAxisOffset: step.Position.XAxisOffset,
			positionYAxisOffset: step.Position.YAxisOffset,
			canvas: step.Canvas,
			modal: step.Modal,
			imageproperties: step.ImageProperties?.[0]?.CustomImage?.[0] || "",
			autoposition: step.AutoPosition,
			elementclick: step.Design?.GotoNext,
			PossibleElementPath: step.PossibleElementPath,
		};

		// Debug logging for AI tooltip button click data
		if (step.Design?.GotoNext) {
			console.log(`🔍 Tooltip Preview Step ${stepIndex} - elementclick data:`, {
				stepIndex,
				elementclick: stepData.elementclick,
				hasButtonId: !!step.Design?.GotoNext?.ButtonId,
				hasNextStep: !!step.Design?.GotoNext?.NextStep,
				fullGotoNext: step.Design?.GotoNext
			});
		}

		return stepData;
	});

	const tooltipConfig = data?.GuideStep?.[currentStep - 1]?.Tooltip || {};

	const handleCloseTooltip = () => {
		setShowTooltip(false);
		const storedGuides = JSON.parse(localStorage.getItem("closedGuides_") || "[]");
		const updatedGuides = [
			...storedGuides,
			{
				GuideId: data.GuideId,
				DontShowAgain: true,
			},
		];
		localStorage.setItem("closedGuides_", JSON.stringify(updatedGuides));
	};
	useEffect(() => {
		const handleStorageChange = () => {
			const storedGuides: Guide[] = JSON.parse(localStorage.getItem("closedGuides_") || "[]");
			const isGuideClosed = storedGuides.some(
				(guide) => guide.GuideId === data?.GuideId && guide?.DontShowAgain === true
			);
			if (savedGuideData && data?.GuideStep?.length > 0 && isGuideClosed) {
				setShowTooltip(false);
			} else if (data?.GuideStep?.length > 0) {
				setShowTooltip(true);
			}
		};

		handleStorageChange();
		window.addEventListener("storage", handleStorageChange);
		return () => {
			window.removeEventListener("storage", handleStorageChange);
		};
	}, [savedGuideData, data?.GuideId, data?.GuideStep?.length]);

	return (
		<div>
			{showTooltip && (
				<TooltipGuide
					steps={steps}
					currentUrl={currentUrl}
					onClose={handleCloseTooltip}
					tooltipConfig={tooltipConfig}
					startStepIndex={currentStep}
					data={data}
				/>
			)}
		</div>
	);
};

export default TooltiplastUserview;
