import { adminApiService, userApiService } from "./APIService";

export const getLoginUserRoles = async (userId:string) => {
    try {
        const response = await adminApiService.get(`/User/GetLoginUserRoles`); // LoginUserRoles 
        return response.data;
    } catch (error) {
        console.error("Error fetching user roles", error);
        throw error;
    }
};

export const getRolesByUser = async () => {
    try {
        const response = await adminApiService.get(`/User/GetUserRoles`);
        return response.data;
    } catch (error) {
        console.error("Error fetching user roles", error);
        throw error;
    }
};






