import React, { useState, useEffect } from "react";
import { Popover, Button, Typography, Box, LinearProgress, DialogActions,IconButton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { PopoverOrigin } from "@mui/material";
import useDrawerStore, { DrawerState } from "../../store/drawerStore";
import { GuideData } from "../drawer/Drawer";
import AnnouncementPopup from "../GuidesPreview/AnnouncementPreview";
import HotspotPreview from "../GuidesPreview/HotspotPreview";
import TooltiplastUserview from "../GuidesPreview/tooltippreview/Tooltips/Tooltipuserview";
import BannerStepPreview from "./BannerStepPreview";
interface PopupProps {
    handlecloseBannerPopup: any;
    guideStep: any[];
    anchorEl: null | HTMLElement;
    onClose: () => void;
    onPrevious: () => void;
    onContinue: () => void;
    title: string;
    text: string;
    imageUrl?: string;
    videoUrl?: string;
    previousButtonLabel: string;
    continueButtonLabel: string;
    previousButtonStyles?: {
        backgroundColor?: string;
        textColor?: string;
        borderColor?: string;
    };
    continueButtonStyles?: {
        backgroundColor?: string;
        textColor?: string;
        borderColor?: string;
    };
    currentStep: number;
    totalSteps: number;
    onDontShowAgain: () => void;
    progress: number;
    textFieldProperties?: any;
    imageProperties?: any;
    customButton?: any;
    modalProperties?: { InteractionWithPopup?: boolean; IncludeRequisiteButtons?: boolean; DismissOption?: boolean; ModalPlacedOn?: string };
    canvasProperties?: {
        Position?: string;
        Padding?: string;
        Radius?: string;
        BorderSize?: string;
        BorderColor?: string;
        BackgroundColor?: string;
        Width?: string;
    };
    htmlSnippet: string;
    OverlayValue: boolean;
    backgroundC: any;
    Bposition: any;
    bpadding: any;
    Bbordercolor: any;
    BborderSize: any;
    savedGuideData: GuideData | null;
    selectedTemplate:any}



    const TourPreview: React.FC<PopupProps> = ({
        selectedTemplate,
        handlecloseBannerPopup,
        backgroundC,
        Bposition,
        bpadding,
        Bbordercolor,
        BborderSize,
        guideStep,
        anchorEl,
        onClose,
        onPrevious,
        onContinue,
        title,
        text,
        imageUrl,
        videoUrl,
        previousButtonLabel,
        continueButtonLabel,
        currentStep,
        totalSteps,
        onDontShowAgain,
        progress,
        textFieldProperties,
        imageProperties,
        customButton,
        modalProperties,
        canvasProperties,
        htmlSnippet,
        previousButtonStyles,
        continueButtonStyles,
        OverlayValue,
        savedGuideData
    }) => {
        const {
            setSelectedTemplate,
            setBannerPopup,
          setSelectedTemplateTour,
          setSteps,
          steps,
          setTooltipCount,
          tooltipCount,
            HotspotGuideDetails,
            announcementPreview, setAnnouncementPreview,
            bannerPreview, setBannerPreview,
            tooltipPreview, setTooltipPreview,
            hotspotPreview, setHotspotPreview,
            setCurrentStep,
            setOpenTooltip,
            ProgressColor,
            setProgressColor
        } = useDrawerStore((state: DrawerState) => state);

        const stepType = savedGuideData?.GuideStep?.[currentStep - 1]?.StepType;

        // Note: AI data synchronization is now handled in the store's setCurrentStep function
        // to avoid infinite loops and ensure proper timing

        useEffect(() => {
            // Clean up any existing hotspot before setting new preview
            const existingHotspot = document.getElementById("hotspotBlink");
            if (existingHotspot) {
                existingHotspot.style.display = "none";
                existingHotspot.remove();
            }
            // Reset all previews before setting the specific one
            setAnnouncementPreview(false);
            setBannerPreview(false);
            setTooltipPreview(false);
            setHotspotPreview(false);



            // Set the correct preview based on stepType
            if (stepType === "Announcement") {
                setAnnouncementPreview(true);
                resetHeightofBanner("",0,0,0)
            } else if (stepType === "Banner") {
                setBannerPreview(true);
                  // Get the current Canvas position from the step
                  const currentPosition = savedGuideData?.GuideStep?.[currentStep - 1]?.Canvas?.Position;
                  // Use the current position value to properly apply Push Down effect
                  resetHeightofBanner(
                      currentPosition,
                      parseInt(savedGuideData?.GuideStep?.[currentStep - 1]?.Canvas?.Padding||"0"),
                      parseInt(savedGuideData?.GuideStep?.[currentStep - 1]?.Canvas?.BorderSize||"0"),    0
                                  );
            } else if (stepType === "Tooltip") {
                setTooltipPreview(true);
                resetHeightofBanner("",0,0,0)
            } else if(stepType === "Hotspot") {
                setHotspotPreview(true);
                setOpenTooltip(false)
                resetHeightofBanner("",0,0,0)

                // Initialize tour hotspot metadata for proper functionality
                initializeTourHotspotMetadata();

                // Ensure pulse animation is enabled for tour hotspots
                // useDrawerStore.setState({ pulseAnimationsH: true });

                // Debug logging for tour hotspot initialization
                console.log("Tour hotspot initialized:", {
                    stepType,
                    currentStep,
                    hotspotData: savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot,
                    elementPath: savedGuideData?.GuideStep?.[currentStep - 1]?.ElementPath
                });
            }
        }, [stepType, currentStep]);  // Dependencies ensure effect runs when stepType or currentStep changes

        // Initialize tour hotspot metadata for HotspotPreview compatibility
        const initializeTourHotspotMetadata = () => {
            const currentStepData = savedGuideData?.GuideStep?.[currentStep - 1];
            if (currentStepData && currentStepData.StepType === "Hotspot") {
                const hotspotProps = (currentStepData as any).Hotspot || {};

                // Create metadata structure for HotspotPreview
                const tourHotspotMetadata = {
                    containers: [],
                    stepName: (currentStepData as any).StepTitle || `Step ${currentStep}`,
                    stepDescription: "",
                    stepType: "Hotspot" as const,
                    currentStep: currentStep,
                    stepId: (currentStepData as any).StepId || crypto.randomUUID(),
                    xpath: {
                        value: (currentStepData as any).ElementPath || "",
                        PossibleElementPath: (currentStepData as any).PossibleElementPath || "",
                        position: { x: 0, y: 0 }
                    },
                    id: crypto.randomUUID(),
                    hotspots: {
                        XPosition: (hotspotProps as any).XPosition || "4",
                        YPosition: (hotspotProps as any).YPosition || "4",
                        Type: (hotspotProps as any).Type || "Question",
                        Color: (hotspotProps as any).Color || "yellow",
                        Size: (hotspotProps as any).Size || "16",
                        PulseAnimation: (hotspotProps as any).PulseAnimation !== false,
                        stopAnimationUponInteraction: (hotspotProps as any).stopAnimationUponInteraction !== false,
                        ShowUpon: (hotspotProps as any).ShowUpon || "Clicking Hotspot",
                        ShowByDefault: (hotspotProps as any).ShowByDefault || false,
                    },
                    canvas: {
                        position: ((currentStepData as any).Canvas?.Position || "auto"),
                        autoposition: ((currentStepData as any).Canvas?.AutoPosition || false),
                        xaxis: ((currentStepData as any).Canvas?.XAxis || "0"),
                        yaxis: ((currentStepData as any).Canvas?.YAxis || "0"),
                        width: ((currentStepData as any).Canvas?.Width || "300px"),
                        padding: ((currentStepData as any).Canvas?.Padding || "16"),
                        borderRadius: ((currentStepData as any).Canvas?.BorderRadius || "8"),
                        borderSize: ((currentStepData as any).Canvas?.BorderSize || "1"),
                        borderColor: ((currentStepData as any).Canvas?.BorderColor || "transparent"),
                        backgroundColor: ((currentStepData as any).Canvas?.BackgroundColor || "#ffffff"),
                    },
                    design: {
                        gotoNext: {
                            ButtonId: ((currentStepData as any).Design?.GotoNext?.ButtonId || ""),
                            ElementPath: ((currentStepData as any).Design?.GotoNext?.ElementPath || ""),
                            NextStep: ((currentStepData as any).Design?.GotoNext?.NextStep || ""),
                            ButtonName: ((currentStepData as any).Design?.GotoNext?.ButtonName || ""),
                        },
                        element: {
                            progress: "",
                            isDismiss: false,
                            progressSelectedOption: 1,
                            progressColor: "var(--Theme-accentColor)",
                        },
                    },
                };

                // Update store with tour hotspot metadata
                const store = useDrawerStore.getState();
                const updatedMetadata = [...store.toolTipGuideMetaData];

                // Ensure array has enough entries
                while (updatedMetadata.length < currentStep) {
                    updatedMetadata.push({
                        containers: [],
                        stepName: `Step ${updatedMetadata.length + 1}`,
                        stepDescription: "",
                        stepType: "Hotspot",
                        currentStep: updatedMetadata.length + 1,
                        stepId: crypto.randomUUID(),
                        xpath: { value: "", PossibleElementPath: "", position: { x: 0, y: 0 } },
                        id: crypto.randomUUID(),
                        hotspots: {
                            XPosition: "4",
                            YPosition: "4",
                            Type: "Question",
                            Color: "yellow",
                            Size: "16",
                            PulseAnimation: true,
                            stopAnimationUponInteraction: true,
                            ShowUpon: "Clicking Hotspot",
                            ShowByDefault: false,
                        },
                        canvas: {
                            position: "auto",
                            autoposition: false,
                            xaxis: "0",
                            yaxis: "0",
                            width: "300px",
                            padding: "16",
                            borderRadius: "8",
                            borderSize: "1",
                            borderColor: "transparent",
                            backgroundColor: "#ffffff",
                        },
                        design: {
                            gotoNext: {
                                ButtonId: "",
                                ElementPath: "",
                                NextStep: "",
                                ButtonName: "",
                            },
                            element: {
                                progress: "",
                                isDismiss: false,
                                progressSelectedOption: 1,
                                progressColor: "var(--Theme-accentColor)",
                            },
                        },
                    });
                }

                // Set metadata for current step
                updatedMetadata[currentStep - 1] = tourHotspotMetadata as any;

                // Update store
                useDrawerStore.setState({
                    toolTipGuideMetaData: updatedMetadata,
                    elementSelected: true
                });
                // Use the function with skipOverlayReset to preserve user settings
                useDrawerStore.getState().setSelectedTemplateTour("Hotspot", true);
            }
        };

        const resetHeightofBanner = (position : any,padding: any = 0,border : any =0, top: any = 55) => {
            const styleExTag = document.getElementById("dynamic-body-style");
            if (styleExTag) {
                document.head.removeChild(styleExTag);
            }
            let styleTag = document.getElementById("dynamic-body-style") as HTMLStyleElement;
                const bodyElement = document.body;

                bodyElement.classList.add("dynamic-body-style");

                if (!styleTag) {
                    styleTag = document.createElement("style");
                    styleTag.id = "dynamic-body-style";

                    let styles = `
                    .dynamic-body-style {
                        padding-top: ${top}px !important;
                        max-height:calc(100% - 55px);
                    }

                    `;
                // Add styles for body and nested elements
                if (position === "Push Down")
                    {
                        // Inside the "Push Down" condition:
    const banner = document.getElementById("guide-popup");
    const bannerHeight = banner?.offsetHeight || 49;
    // Include padding and border in the height calculation
    const paddingValue = parseInt(padding.toString()) || 0;
    const borderValue = parseInt(border.toString()) || 0;
    // Only add additionalHeight if banner is null
    const additionalHeight = (paddingValue * 2) + (borderValue * 2);
    // Use bannerHeight + additionalHeight only if banner is null
    const height = banner ? bannerHeight : bannerHeight + additionalHeight;

                        styles = `
                        .dynamic-body-style {
                            padding-top: ${height}px !important;
                            max-height:calc(100% - 55px);
                        }
                       .dynamic-body-style header {
						top: ${height}px !important;
					}

                        		.dynamic-body-style .page-sidebar {
						padding-top: ${height}px !important;
					}
                        `;


                    }

                    styleTag.innerHTML = styles;
                    document.head.appendChild(styleTag);
                }
        }

        return (
            <div>
                {OverlayValue && !bannerPreview && (
                    <div style={{
                        position: 'fixed',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        backgroundColor: 'rgba(0, 0, 0, 0.5)',
                        zIndex: 998,
                        pointerEvents: "none"
                    }} />
                )}
      {bannerPreview  && (
                 <BannerStepPreview
                 showBannerenduser=""
                 setShowBannerenduser=""
                 initialGuideData={savedGuideData}
                 setInitialGuideData=""
                 onClose={handlecloseBannerPopup}
                 backgroundC={backgroundC}
                // Pass the current position from the current step
                Bposition={savedGuideData?.GuideStep?.[currentStep - 1]?.Canvas?.Position || "Cover Top"}                 bpadding={bpadding}
                 Bbordercolor={Bbordercolor}
                        BborderSize={BborderSize}
                        totalSteps={savedGuideData?.GuideStep?.length || 1}
                        ProgressColor={ProgressColor}
                        savedGuideData={savedGuideData}
                        progress={((currentStep) / (savedGuideData?.GuideStep?.length || 1)) * 100}
             />
            )}
                {announcementPreview &&(
                    <AnnouncementPopup
                        selectedTemplate={selectedTemplate}
                        handlecloseBannerPopup={handlecloseBannerPopup}
                        guideStep={guideStep}
                        anchorEl={document.body}
                        onClose={onClose}
                        onPrevious={() => {}}
                        onContinue={() => {}}
                        title={savedGuideData?.GuideStep?.[currentStep]?.StepTitle || ""}
                        text={savedGuideData?.GuideStep?.[currentStep]?.TextFieldProperties?.[0]?.Text || ""}
                        imageUrl={savedGuideData?.GuideStep?.[currentStep]?.ImageProperties?.[0]?.CustomImage?.[0]?.Url || ""}
                        previousButtonLabel="Back"
                        continueButtonLabel="Next"
                        currentStep={currentStep}
                        totalSteps={savedGuideData?.GuideStep?.length || 1}
                        onDontShowAgain={() => {}}
                        progress={((currentStep) / (savedGuideData?.GuideStep?.length || 1)) * 100}
                        textFieldProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.TextFieldProperties || []}
                        imageProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.ImageProperties || []}
                        customButton={
                            savedGuideData?.GuideStep?.[currentStep - 1]?.ButtonSection?.flatMap(section =>
                                section.CustomButtons.map(button => ({
                                    ...button,
                                    ContainerId: section.Id,
                                }))
                            ) || []
                        }
                        modalProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Modal || {}}
                        canvasProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Canvas || {}}
                        htmlSnippet={savedGuideData?.GuideStep?.[currentStep - 1]?.TextFieldProperties?.[0]?.Text || ""}
                        OverlayValue={OverlayValue}
                        savedGuideData={savedGuideData}
                        backgroundC={backgroundC}
                        Bposition={Bposition}
                        bpadding={bpadding}
                        Bbordercolor={Bbordercolor}
                        BborderSize={BborderSize}
                        ProgressColor={ProgressColor}
                    />
                )}

                {hotspotPreview && (
                   <HotspotPreview
                   isHotspotPopupOpen={true}
                   showHotspotenduser={true}
                   handleHotspotHover={() => {
                       // Enable hover functionality for tour preview
                       console.log("Tour hotspot hover detected");
                   }}
                   handleHotspotClick={() => {
                       // Enable click functionality for tour preview
                       console.log("Tour hotspot click detected");
                       setOpenTooltip(true);
                   }}
                   guideStep={guideStep}
                   onClose={onClose}
                   title={savedGuideData?.GuideStep?.[currentStep-1]?.StepType || ""}
                   text={savedGuideData?.GuideStep?.[currentStep]?.TextFieldProperties?.[0]?.Text || ""}
                   imageUrl={savedGuideData?.GuideStep?.[currentStep]?.ImageProperties?.[0]?.CustomImage?.[0]?.Url || ""}
                   onPrevious={() => {}}
                   onContinue={() => {}}
                   currentStep={currentStep} // Adjust currentStep for display (1-based index)
                   totalSteps={savedGuideData?.GuideStep?.length || 1}
                   onDontShowAgain={() => {}}
                   progress={((currentStep) / (savedGuideData?.GuideStep?.length || 1)) * 100}
                   textFieldProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.TextFieldProperties || []}
                   imageProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.ImageProperties || []}
                   customButton={
                       savedGuideData?.GuideStep?.[currentStep - 1]?.ButtonSection?.map((section) =>
                           section.CustomButtons.map((button) => ({
                               ...button,
                               ContainerId: section.Id, // Attach the container ID for grouping
                           }))
                       )?.reduce((acc, curr) => acc.concat(curr), []) || []
                   }
                   modalProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Modal || {}}
                   canvasProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Canvas || {}}
                   htmlSnippet={savedGuideData?.GuideStep?.[currentStep - 1]?.TextFieldProperties?.[0]?.Text || ""}
                   OverlayValue={OverlayValue}
                   savedGuideData={savedGuideData}
                   hotspotProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot || {}}
                   anchorEl={document.body}

               />
                )}


                {tooltipPreview && (


                    <TooltiplastUserview
				guideStep={guideStep}
				onClose={onClose}
				title={savedGuideData?.GuideStep?.[currentStep]?.StepTitle || ""}
				text={savedGuideData?.GuideStep?.[currentStep]?.TextFieldProperties?.[0]?.Text || ""}
				imageUrl={savedGuideData?.GuideStep?.[currentStep]?.ImageProperties?.[0]?.CustomImage?.[0]?.Url || ""}
				onPrevious={() => {}}
				onContinue={() => {}}
				currentStep={currentStep} // Adjust currentStep for display (1-based index)
				totalSteps={savedGuideData?.GuideStep?.length || 1}
				onDontShowAgain={() => {}}
				progress={((currentStep + 1) / (savedGuideData?.GuideStep?.length || 1)) * 100}
				textFieldProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.TextFieldProperties || []}
				imageProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.ImageProperties || []}
				customButton={
					savedGuideData?.GuideStep?.[currentStep - 1]?.ButtonSection?.map((section) =>
						section.CustomButtons.map((button) => ({
							...button,
							ContainerId: section.Id, // Attach the container ID for grouping
						}))
					)?.reduce((acc, curr) => acc.concat(curr), []) || []
				}
				modalProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Modal || {}}
				canvasProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Canvas || {}}
				htmlSnippet={savedGuideData?.GuideStep?.[currentStep - 1]?.TextFieldProperties?.[0]?.Text || ""}
				OverlayValue={OverlayValue}
				savedGuideData={savedGuideData}
				//hotspotProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot || {}}
				anchorEl={document.body}
				previousButtonLabel={""}
				continueButtonLabel={""}
			/>

)}


                {/* Add similar logic for Banner, Tooltip, and Hotspot if needed */}
            </div>
        );
    };

    export default TourPreview;
