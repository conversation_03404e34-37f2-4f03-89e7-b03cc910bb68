import React, { useState, useMemo, useEffect } from "react";
//import { useFetch } from "../../hooks/useFetch";
import { useLocation } from "react-router-dom";
import TooltipGuide from "../Tooltips/components/TooltipProgressBar";
//import MenuBar from "../Tooltips/Menubarfortest";
import useDrawerStore, { DrawerState } from "../../store/drawerStore";

const TooltipUserview = ({ initialGuideData }: any) => {
	const [currentUrl, setCurrentUrl] = useState(window.location.href);
	const [showTooltip, setShowTooltip] = useState(true);
	const {} = useDrawerStore((state: DrawerState) => state);
	const getElementByXPath = (xpath: string): HTMLElement | null => {
		const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
		const node = result.singleNodeValue;
		if (node instanceof HTMLElement) {
			return node;
		} else if (node?.parentElement) {
			return node.parentElement; // Return parent if it's a text node
		} else {
			return null;
		}
	};
	// const [response] = useFetch({
	//     url: `/EndUserGuide/GetGuideListByTargetUrl?targetUrl=${currentUrl}`,
	// });

	// const toolTips = useMemo(() => {
	// 	if (initialGuideData) {
	// 		return (
	// 			(initialGuideData as any[]).find(
	// 				(item) =>
	// 					item.GuideType === "Tooltip" && item.GuideStep?.length > 0 && item.TargetUrl === window.location.href
	// 			) || {}
	// 		);
	// 	}
	// 	return {}; // Return an empty object if there's no valid response
	// }, [initialGuideData]);
	useEffect(() => {
		const element = getElementByXPath(initialGuideData.GuideStep[0].ElementPath);
		// setTargetElement(element);
		if (element) {
			element.style.backgroundColor = "blue";
		}
	}, [initialGuideData]);

	const guideDataArray = useMemo(() => {
		if (Array.isArray(initialGuideData)) {
			return initialGuideData;
		}
		if (initialGuideData && typeof initialGuideData === "object") {
			// If it's an object, wrap it in an array
			return [initialGuideData];
		}
		// If it's null, undefined, or any other type, return an empty array
		return [];
	}, [initialGuideData]);

	const toolTips = useMemo(() => {
		return (
			guideDataArray.find(
				(item) => item.GuideType === "Tooltip" && item.GuideStep?.length > 0 && item.TargetUrl === window.location.href
			) || {}
		);
	}, [guideDataArray]);

	const data = toolTips;
	const xpathUrl = data.TargetUrl;

	// Check if GuideStep exists before calling map
	const steps = (data.GuideStep || []).map((step: any) => ({
		xpath: step.ElementPath,
		content: step.TextFieldProperties?.[0]?.Text || "",
		imageUrl: step.ImageProperties?.[0]?.CustomImage?.[0]?.Url || "",
		buttonData: step.ButtonSection?.[0]?.CustomButtons || [],
		targetUrl: step.StepTargetURL || xpathUrl || "", // Use individual step URL if available, fallback to guide URL
		overlay: step.Overlay,
	}));

	const tooltipConfig = data.GuideStep?.[0]?.Tooltip || {};

	const handleCloseTooltip = () => {
		setShowTooltip(false);
	};

	return (
		<div>
			{/* <MenuBar /> */}
			{/* <div
				id="tooltip-target"
				style={{ margin: "50px", padding: "10px", border: "1px solid black" }}
			>
				Hover over me
			</div>
			<div
				style={{ marginTop: "150px", textAlign: "center" }}
				id="tooltip-next"
			>
				<h1>TooltipTest</h1>
			</div>
			<div
				style={{ marginTop: "163px", textAlign: "center" }}
				id="tooltip-test"
			>
				<p>this is tooltip test</p>
			</div> */}

			{showTooltip && (
				<TooltipGuide
					steps={steps}
					currentUrl={currentUrl}
					onClose={handleCloseTooltip}
					tooltipConfig={tooltipConfig}
				/>
			)}
		</div>
	);
};

export default TooltipUserview;
