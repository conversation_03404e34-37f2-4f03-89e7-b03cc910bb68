import React, { useState } from "react";
import "./BannerHtmlElement.css";
import "../../guideDesign/Canvas.module.scss";
import useDrawerStore from "../../../store/drawerStore";

const BannerHtmlComponent = () => {
	//const [htmlCode, setHtmlCode] = useState("");
	const [isPopupVisible, setIsPopupVisible] = useState(false);
	const { htmlCode, setHtmlCode } = useDrawerStore((state) => state);

	const handleSave = () => {
		
		setIsPopupVisible(false); // Close popup after saving
	};

	const togglePopup = () => {
		setIsPopupVisible(!isPopupVisible);
	};

	return (
		<div>
			{/* Button to trigger the popup */}
			<button
				className="open-popup-btn"
				onClick={togglePopup}
			>
				Add HTML Code
			</button>

			{/* Popup container */}
			{isPopupVisible && (
				<div className="qadpt-designpopup qadpt-designpopup-right-htmlelement">
					<div className="html-code-container">
						<div className="header">
							<h3>Add HTML Code</h3>
							<button
								className="close-btn"
								onClick={togglePopup}
							>
								&times;
							</button>
						</div>

						<div className="textarea-section">
							<textarea
								value={htmlCode}
								onChange={(e) => setHtmlCode(e.target.value)}
								placeholder="Enter HTML Code here..."
							></textarea>
						</div>

						<div className="save-section">
							<button
								className="save-btn"
								onClick={handleSave}
							>
								Save
							</button>
						</div>
					</div>
				</div>
			)}
		</div>
	);
};

export default BannerHtmlComponent;
