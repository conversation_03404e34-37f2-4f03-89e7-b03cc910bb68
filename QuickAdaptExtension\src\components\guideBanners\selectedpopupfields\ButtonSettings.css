.properties-container {
    border: 1px solid #e0e0e0;
    padding: 10px;
    width: 250px;
    border-radius: 5px;
    background-color: #f9f9f9;
  }
  
  .properties-section {
    margin-bottom: 15px;
  }
  
  .properties-section label {
    display: block;
    font-size: 14px;
    color: #333;
    margin-bottom: 5px;
  }
  
  .properties-section input,
  .properties-section select {
    width: 100%;
    padding: 5px;
    font-size: 14px;
    border: 1px solid #ccc;
    border-radius: 3px;
    box-sizing: border-box;
  }
  
  .tab-btn {
    padding: 6px 12px;
    margin-right: 5px;
    font-size: 14px;
    border: 1px solid #ccc;
    border-radius: 3px;
    cursor: pointer;
    background-color: #fff;
  }
  
  .tab-btn.active {
    background-color: #4caf50;
    color: #fff;
  }
  
  .colors-section .color-picker {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
  }
  
  .colors-section .color-picker input[type="color"] {
    margin-right: 10px;
    border: none;
    padding: 0;
    width: 30px;
    height: 30px;
  }
  
  .position-selector {
    display: flex;
    justify-content: space-between;
  }
  
  .position-option {
    width: 30px;
    height: 30px;
    border: 1px solid #ccc;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    border-radius: 3px;
  }
  
  .position-option.active {
    border-color: #4caf50;
  }
  
  .position-icon {
    width: 10px;
    height: 10px;
    background-color: #333;
  }
  
  .position-icon.left {
    transform: translateX(-5px);
  }
  
  .position-icon.center {
    transform: translateX(0);
  }
  
  .position-icon.right {
    transform: translateX(5px);
  }
  