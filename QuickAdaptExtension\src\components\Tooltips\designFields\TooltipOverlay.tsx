import React, { useState, useEffect } from "react";
import {
	Box,
	Typography,
	ToggleButton,
	ToggleButtonGroup,
	IconButton,
	FormControlLabel,
	Switch,
	Button,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import ArrowBackIosNewOutlinedIcon from "@mui/icons-material/ArrowBackIosNewOutlined";
import useDrawerStore from "../../../store/drawerStore";
import "./Canvas.module.css";

interface OverlaySettingsProps {
	selectedTemplate: string;
	onStatusChange: (status: boolean) => void;
	setOverLays: (status: boolean) => void;
	anchorEl: HTMLElement | null;
	setDesignPopup: (status: boolean) => void;
}

const TooltipOverlaySettings = ({
	selectedTemplate,
	onStatusChange,
	setOverLays,
	anchorEl,
	setOverlaySettings,
	setDesignPopup,
	setPageInteraction, // We'll use the store's pageinteraction value directly
}: any) => {
	const [isOpen, setIsOpen] = useState(true);
	const { setOverlayEnabled, overlayEnabled, pageinteraction: storePageInteraction, selectedTemplateTour, setIsUnSavedChanges } = useDrawerStore((state) => state);

	// Initialize local state from store values
	const [interaction, setInteraction] = useState<boolean>(storePageInteraction);
	const [status, setStatus] = useState(overlayEnabled);

	// Keep local state in sync with store when store values change
	useEffect(() => {
		setStatus(overlayEnabled);
		setInteraction(storePageInteraction);
	}, [overlayEnabled, storePageInteraction]);

	const handleStatusChange = (event: any) => {
		const newStatus = event.target.checked;
		setStatus(newStatus);

		// Implement mutual exclusivity: when overlay changes, page interaction must be opposite
		if (newStatus === false) {
			// When overlay is disabled, automatically enable page interaction
			setInteraction(true);
		} else {
			// When overlay is enabled, automatically disable page interaction
			setInteraction(false);
		}
	};

	const handleApplyChanges = () => {
		// Apply the changes - the store will handle recording the change for undo/redo
		setOverlayEnabled(status);
		setPageInteraction(interaction);
		setIsOpen(false);
		setOverlaySettings(false);
		setIsUnSavedChanges(true);
	};

	const handleInteractionChange = (event: any) => {
		const newInteraction = event.target.checked;
		setInteraction(newInteraction);

		// Implement asymmetric mutual exclusivity: only disable overlay when page interaction is enabled
		if (newInteraction === true) {
			// When page interaction is enabled, automatically disable overlay
			setStatus(false);
		}
		// When page interaction is disabled, do NOT automatically enable overlay
		// This allows both options to be disabled simultaneously
	};

	const handleClose = () => {
		setIsOpen(false);
		setOverlaySettings(false);
	};
	if (!isOpen) return null;

	return (
		<div
			id="qadpt-designpopup"
			className="qadpt-designpopup"
		>
			<div className="qadpt-content">
				<div className="qadpt-design-header">
					<IconButton
						aria-label="close"
						onClick={handleClose}
					>
						<ArrowBackIosNewOutlinedIcon />
					</IconButton>

					<div className="qadpt-title"> {selectedTemplate !== "Banner" ? "Overlay" : "Shadow"}</div>
					<IconButton
						size="small"
						aria-label="close"
						onClick={handleClose}
					>
						<CloseIcon />
					</IconButton>
				</div>

				{/* Status Section */}
				<div className="qadpt-status-container">
					{/* Enable Shadow or Enable Overlay */}
					<div
						style={{
							display: "flex",
							alignItems: "center",
							justifyContent: "space-between",
							padding: "5px",
							borderRadius: "12px",
							backgroundColor: "var(--back-light-color)",
						}}
					>
						<div className="qadpt-label">{selectedTemplate === "Banner" || selectedTemplateTour==="Banner" ? "Enable Shadow" : "Enable Overlay"}</div>
						<Switch
							checked={status}
							onChange={handleStatusChange}
							name="toggleSwitch"
							color="primary"
							className="qadpt-toggle-group"
						/>
					</div>

					{!status && (
						<>
							{/* Interact with Page */}
							<div
								style={{
									display: "flex",
									alignItems: "center",
									justifyContent: "space-between",
									padding: "5px",
									borderRadius: "12px",
									backgroundColor: "var(--back-light-color)",
								}}
							>
								<div
									className="qadpt-label"
									//style={{ opacity: 0.5 }}
								>
									Interact with Page
								</div>
								<Switch
									//	disabled={!interaction}
									checked={interaction}
									onChange={handleInteractionChange}
									name="toggleSwitch"
									color="primary"
									className="qadpt-toggle-group"
								/>
							</div>
						</>
					)}
				</div>

				<div className="qadpt-drawerFooter">
					<Button
						variant="contained"
						onClick={handleApplyChanges}
						className={`qadpt-btn`}
					>
						Apply
					</Button>
				</div>
			</div>
		</div>
	);
};

export default TooltipOverlaySettings;
