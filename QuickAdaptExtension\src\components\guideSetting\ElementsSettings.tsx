import React, { useState, useEffect } from "react";
import {
	Box,
	Typography,
	IconButton,
	Switch,
	Grid,
	Button,
	Select,
	MenuItem,
	FormControl,
	Tooltip,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import ArrowBackIosNewOutlinedIcon from "@mui/icons-material/ArrowBackIosNewOutlined";
import useDrawerStore from "../../store/drawerStore";
import { useTranslation } from 'react-i18next';

interface ElementsSettingsProps {
	onDismissDataChange: (data: {
		Actions: string | number | null;
		DisplayType: string;
		Color: string;
		DontShowAgain: boolean;
		dismisssel: boolean;
	}) => void;
}
const Elementssettings = ({ setShowElementsSettings, setDesignPopup, setMenuPopup, resetHeightofBanner }: any) => {
	const { t: translate } = useTranslation();

	const {
		setDismissData,
		dismissData,
		selectedOption,
		setSelectedOption,
		selectedTemplate,
		setTooltipElementOptions,
		toolTipGuideMetaData,
		currentStep,
		updateprogressclick,
		progress,
		setProgress,
		dismiss,
		setDismiss,
		selectedTemplateTour,
		setIsUnSavedChanges,
		ProgressColor,
		setProgressColor,
		updateDesignelementInTooltip,
		Bposition
	} = useDrawerStore((state) => state);
	const [tempDismiss, setTempDismiss] = useState(dismiss); // Temporary state
	const [colorChange, setColorChange] = useState(ProgressColor);
	const [isOpen, setIsOpen] = useState(true);
	const [displayType, setDisplayType] = useState("Cross Icon");
	const [dontShowAgain, setDontShowAgain] = useState(true);
	const [colors, setColors] = useState("#ff0000");
	// Initialize local state with values from global state
	const [elementSettings, setElementsSettings] = useState({
		isProgress: progress,
		progressSelectedOption: Number(selectedOption) || 1, // Default to 1 if not set
	});

	// Add useEffect to synchronize local state with global state
	useEffect(() => {
		// Update local state when global state changes
		setElementsSettings({
			isProgress: progress,
			progressSelectedOption: Number(selectedOption) || 1,
		});
		setTempDismiss(dismiss);
		setColorChange(ProgressColor);
	}, [progress, selectedOption, dismiss, ProgressColor]);

	// Update handlers for each dismiss option
	const handleOptionSelect = (option: string | number) => {
		// Update only the local state
		const numOption = Number(option);
		setElementsSettings(eleSettings => {
			return { ...eleSettings, progressSelectedOption: numOption }
		});

		// Don't update global state until Apply is clicked
	};

	const handleDisplayTypeChange = (value: string) => {
		setDisplayType(value);
	};

	const handleBorderColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const color = e.target.value;
		setColors(color);
	};

	const handleDontShowAgainChange = (event: React.ChangeEvent<HTMLInputElement>, checked: boolean) => {
		setDontShowAgain(checked);
	};


	const handleApplyChanges = () => {
		// Store the previous progress state for comparison
		const previousProgressState = progress;
		const hasProgressChanged = previousProgressState !== elementSettings.isProgress;
		// Create a complete element settings object
		const updatedElement = {
			progress: elementSettings.isProgress,
        progressSelectedOption: elementSettings.progressSelectedOption,
        dismiss: tempDismiss,
        progressColor: colorChange
		};

		// Create a batch update function to record a single history entry
		const batchUpdate = useDrawerStore.getState().batchUpdate;
		const drawerStore = useDrawerStore.getState();

		// First update the progress state in the store
		// This ensures resetHeightofBanner will use the correct value
		if (hasProgressChanged) {
			// console.log("Setting progress state in store before batch update:", elementSettings.isProgress);
			drawerStore.setProgress(elementSettings.isProgress);
		}

		// Always update dismissData when Apply is clicked, regardless of whether it changed
		// This ensures the dismiss icon state is updated only when Apply is clicked
		setDismissData({
			...(dismissData || { Actions: "", DisplayType: "Cross Icon", Color: "#000000", DontShowAgain: true }),
			dismisssel: tempDismiss
		});

		// Use the batch update function to record a single history entry
		batchUpdate(
			() => {
				// Apply all changes at once
				updateDesignelementInTooltip(updatedElement);

				// Also update the individual state values to ensure everything is in sync
				drawerStore.setProgress(elementSettings.isProgress);
				drawerStore.setSelectedOption(elementSettings.progressSelectedOption);
				drawerStore.setDismiss(tempDismiss);
				drawerStore.setProgressColor(colorChange);

				// For AI-created tours, immediately apply global progress bar synchronization
				// This ensures all steps are updated when Apply is clicked
				if (drawerStore.createWithAI && drawerStore.selectedTemplate === "Tour") {
					console.log("🔄 handleApplyChanges: Applying global progress bar synchronization for AI tour", {
						isProgress: elementSettings.isProgress,
						progressSelectedOption: elementSettings.progressSelectedOption,
						dismiss: tempDismiss,
						progressColor: colorChange,
						currentStep: drawerStore.currentStep,
						totalSteps: drawerStore.interactionData?.GuideStep?.length || 0
					});

					// Apply the changes to all steps immediately
					drawerStore.syncGlobalProgressBarStateForAITour();
				}
			},
			'ELEMENT_BATCH_UPDATE',
			`Updated element settings`
		);
		// When progress setting changes are applied, update the banner height if needed
		if (selectedTemplate === "Tour" && selectedTemplateTour === "Banner" && Bposition === "Push Down" && resetHeightofBanner) {
			// Always update the height when Apply is clicked, regardless of whether progress changed
			// This ensures consistent behavior and fixes any potential state inconsistencies
			// console.log("Applying banner height changes. Progress state:", elementSettings.isProgress);

			// Get the current padding and border values from the store
			const currentPadding = drawerStore.bpadding || "12";
			const currentBorder = drawerStore.BborderSize || "2";

			// Force a complete recalculation of the banner height
			// The isFromApply=true parameter ensures all dimensions are included in the calculation
			resetHeightofBanner(
				"Push Down",
				parseInt(currentPadding),
				parseInt(currentBorder),
				true, // isFromApply
				0,    // oldPadding
				55    // top
			);
		}

		// Update the UI state
		setIsOpen(false);
		setShowElementsSettings(false);
		setDesignPopup(true);
		setMenuPopup(true);
		setIsUnSavedChanges(true);
	};
	useEffect(() => {
		if (dismissData?.dismisssel) {
			setDismiss(true);
			setColors(dismissData.Color);
		}
	}, [dismissData?.dismisssel]);


	// Early return if popup is closed
	if (!isOpen) return null;
	return (
		<div
			id="qadpt-designpopup"
			className="qadpt-designpopup"
		>
			<div className="qadpt-content">
				<div className="qadpt-design-header">
					<IconButton
						aria-label="close"
						onClick={() => {
							setIsOpen(false);
							setShowElementsSettings(false);
							setMenuPopup(true);
						}}
					>
						<ArrowBackIosNewOutlinedIcon />
					</IconButton>
					<div className="qadpt-title">{translate("Elements")}</div>
					<IconButton
						size="small"
						aria-label="close"
						onClick={() => {
							setIsOpen(false);
							setShowElementsSettings(false);
							setDesignPopup(true);
							setMenuPopup(true);
						}}
					>
						<CloseIcon />
					</IconButton>
				</div>

				<div
					className="qadpt-controls"
					//style={{ opacity: "0.5", height: "60px" }}
				>
					{(selectedTemplate!== "Hotspot" && selectedTemplate!== "Banner" ) && (
						<Box className="qadpt-control-box">
							<Typography
								className="qadpt-control-label"
							//sx={{ opacity: "0.5" }}
							>
								{translate("Progress")}
							</Typography>

							<span>
							<label className="toggle-switch">
    <input
        type="checkbox"
        checked={elementSettings.isProgress}
        onChange={(e) => {
			const newValue = e.target.checked;
			// Update only local state
			setElementsSettings(eleSettings => {
				return { ...eleSettings, isProgress: newValue }
			});

			// Don't update global state until Apply is clicked
        }}
        className="qadpt-progress-switch"
    />
    <span className="slider"></span>
</label>

							</span>
						</Box>

					)}

				{elementSettings.isProgress && (selectedTemplate!== "Hotspot" && selectedTemplate!== "Banner" ) && (
					<Box
						sx={{
							backgroundColor: "#EAE2E2",
							borderRadius: "8px",
							padding: "10px",
							marginBottom: "5px",
						}}
					>
						<Grid
							container
							spacing={2}
						>
							<Grid
								item
									xs={12}
							>
								<Box sx={{
										display: "flex",
										justifyContent: "space-between",
										alignItems: "center",
										padding: "5px 10px",
										borderRadius: "6px",
										cursor: "pointer"									}}>
										<Typography className="qadpt-control-label">{translate("Progress Color")}</Typography>
						<input
							type="color"
							value={colorChange}
							onChange={(e) => {
								const newColor = e.target.value;
								// Update only local state
								setColorChange(newColor);
								// Don't update global state until Apply is clicked
							}}
							className="qadpt-color-input"
						/>
					</Box>
							</Grid>
							<Grid
								item
									xs={12}
							>
								<Box
									onClick={() => handleOptionSelect(1)}
									sx={{
										display: "flex",
										justifyContent: "space-between",
										alignItems: "center",
										padding: "5px 10px",
										backgroundColor: Number(elementSettings.progressSelectedOption) === 1 ? "#5f9ea01a" : "#e5dada",
										borderRadius: "6px",
										cursor: "pointer",
										border:Number(elementSettings.progressSelectedOption) === 1 ? "1px solid var(--Theme-accentColor)" :"transparent"
									}}
									>
										<Typography sx={{ color: "var(--Theme-accentColor)" }}>{translate("Dots")}</Typography>
									<div style={{ display: "flex", gap: "4px" }}>
										<div style={{ width: "8px", height: "8px", backgroundColor: "var(--Theme-accentColor)", borderRadius: "50%" }}></div>
										<div style={{ width: "8px", height: "8px", backgroundColor: "var(--Theme-accentColor)", borderRadius: "50%" }}></div>
										<div style={{ width: "8px", height: "8px", backgroundColor: "var(--Theme-accentColor)", borderRadius: "50%" }}></div>
									</div>
								</Box>
							</Grid>

							<Grid
								item
									xs={12}
									sx={{paddingTop:"6px !important"}}
							>
								<Box
									onClick={() => handleOptionSelect(2)}
									sx={{
										display: "flex",
										justifyContent: "space-between",
										alignItems: "center",
										padding: "5px 10px",
										backgroundColor: Number(elementSettings.progressSelectedOption) === 2 ? "#5f9ea01a" : "#e5dada",
										borderRadius: "6px",
										cursor: "pointer",
										border:Number(elementSettings.progressSelectedOption) === 2 ? "1px solid var(--Theme-accentColor)" :"transparent"
									}}
									>
										<Typography sx={{ color: "var(--Theme-accentColor)" }}>{translate("Linear Progress")}</Typography>
									<div style={{ width: "40px", height: "4px", backgroundColor: "#5f96a0" }}></div>
								</Box>
							</Grid>
							<Grid
								item
									xs={12}
									sx={{paddingTop:"6px !important"}}
							>
								<Box
									onClick={() => handleOptionSelect(3)}
									sx={{
										display: "flex",
										justifyContent: "space-between",
										alignItems: "center",
										padding: "5px 10px",
										backgroundColor: Number(elementSettings.progressSelectedOption) === 3 ? "#5f9ea01a" : "#e5dada",
										borderRadius: "6px",
										cursor: "pointer",
										border:Number(elementSettings.progressSelectedOption) === 3 ? "1px solid var(--Theme-accentColor)" :"transparent"
									}}
									>
										<Typography sx={{ color: "var(--Theme-accentColor)" }}>{translate("Bread Crumbs")}</Typography>
									<div style={{ display: "flex", gap: "4px" }}>
									<div style={{width: "14px", height: "4px", backgroundColor: "var(--Theme-accentColor)", borderRadius: "100px" }}></div>
									<div style={{width: "14px", height: "4px", backgroundColor: "var(--Theme-accentColor)", borderRadius: "100px" }}></div>
									<div style={{width: "14px", height: "4px", backgroundColor: "var(--Theme-accentColor)", borderRadius: "100px" }}></div>

									</div>
								</Box>
							</Grid>

							<Grid
								item
									xs={12}
									sx={{paddingTop:"6px !important"}}

							>
								<Box
									onClick={() => handleOptionSelect(4)}
									sx={{
										display: "flex",
										justifyContent: "space-between",
										alignItems: "center",
										padding: "5px 10px",
										backgroundColor: Number(elementSettings.progressSelectedOption) === 4 ? "#5f9ea01a" : "#e5dada",
										borderRadius: "6px",
										cursor: "pointer",
										border:Number(elementSettings.progressSelectedOption) === 4 ? "1px solid var(--Theme-accentColor)" :"transparent"
									}}
									>
										<Typography sx={{ color: "var(--Theme-accentColor)" }}>{translate("Numbers")}</Typography>
										<Typography sx={{ color: "var(--Theme-accentColor)" }}>{translate("1 of 4")}</Typography>
								</Box>
							</Grid>
							<Grid
								item
									xs={12}
									sx={{paddingTop:"6px !important"}}

							>
								<Box
									//onClick={() => handleOptionSelect(4)}
									sx={{
										display: "flex",
										justifyContent: "space-between",
										alignItems: "center",
										padding: "5px 10px",
										backgroundColor: Number(elementSettings.progressSelectedOption) === 5 ? "#5f9ea01a" : "#e5dada",
										borderRadius: "6px",
										cursor: "pointer",
										border:Number(elementSettings.progressSelectedOption) === 5 ? "1px solid var(--Theme-accentColor)" :"transparent"
									}}
									>

										<Typography sx={{ color: "var(--Theme-accentColor)" }}>{translate("CheckList")}</Typography>
								</Box>
							</Grid>
						</Grid>
					</Box>
					)}



					<Box className="qadpt-control-box">
						<div
							className="qadpt-control-label"

						>
							{translate("Dismiss")}
						</div>
						<div>
						<label className="toggle-switch">
    <input
        type="checkbox"
        checked={tempDismiss}
        onChange={(e) => setTempDismiss(e.target.checked)}
        name="tempDismiss"
    />
    <span className="slider"></span>
							</label>
							</div>

					</Box>


				{dismiss && (
					<div

				>

						{/* <Typography sx={{ marginBottom: "8px" }}>Actions</Typography> */}
						{/* <Box sx={{ display: "flex", justifyContent: "space-between", marginBottom: "16px" }}>
							<Button
								variant="contained"
								sx={{
									backgroundColor: selectedOption === "dismiss" ? "rgba(211, 217, 218, 0.5)" : "rgba(250, 246, 246, 1)",
									color: "#000",
									width: "48%",
								}}
								onClick={() => handleOptionSelect("dismiss")}
							>
								Dismiss
							</Button>
							<Button
								variant="outlined"
								sx={{
									backgroundColor: selectedOption === "snooze" ? "rgba(211, 217, 218, 0.5)" : "rgba(250, 246, 246, 1)",
									color: "#000",
									width: "48%",
								}}
								onClick={() => handleOptionSelect("snooze")}
							>
								Snooze
							</Button>
						</Box> */}

						{/* <Typography sx={{ marginBottom: "8px", color: "rgba(0, 0, 0, 0.38)" }}>Display Type</Typography> */}
						{/* <Tooltip
							title="Coming Soon"
							PopperProps={{ sx: { zIndex: 9999 } }}
						>
							<span>
								{" "}

								<FormControl
									fullWidth
									sx={{ marginBottom: "16px" }}
									disabled
								>
									<Select
										value={displayType}
										onChange={(e) => handleDisplayTypeChange(e.target.value as string)}
										MenuProps={{
											anchorOrigin: { vertical: "bottom", horizontal: "left" },
											transformOrigin: { vertical: "top", horizontal: "left" },
											disablePortal: true,
										}}
										sx={{
											backgroundColor: "#F6EEEE",
											"& .MuiSelect-select": { padding: "8px" },
											opacity: "0.5",
										}}
									>
										<MenuItem
											value="Cross Icon"
											disabled
										>
											Cross Icon
										</MenuItem>
										<MenuItem
											value="Close"
											disabled
										>
											Close
										</MenuItem>
									</Select>
								</FormControl>
							</span>
						</Tooltip> */}

						{/* <Typography sx={{ marginBottom: "8px" }}>Color</Typography>
						<Box
							sx={{
								display: "flex",
								alignItems: "center",
								justifyContent: "space-between",
								backgroundColor: "#F6EEEE",
								padding: "8px",
								borderRadius: "8px",
								marginBottom: "16px",
							}}
						>
							<Typography>Fill</Typography>
							<input
								type="color"
								value={colors}
								onChange={handleBorderColorChange}
								style={{
									width: "30px",
									height: "30px",
									border: "none",
									borderRadius: "50%",
									backgroundColor: "transparent",
									cursor: "pointer",
								}}
							/>
						</Box> */}

						{/* <Box sx={{ marginTop: "16px" }}>
							<Typography sx={{ marginBottom: "8px", color: "rgba(0, 0, 0, 0.38)" }}>Don't show again</Typography>
							<Tooltip
								title="Coming Soon"
								PopperProps={{ sx: { zIndex: 9999 } }}
							>
								<span>
									<Box sx={{ display: "flex", justifyContent: "space-between" }}>
										<Button
											variant={dontShowAgain ? "contained" : "outlined"}
											sx={{
												backgroundColor: dontShowAgain ? "rgba(211, 217, 218, 0.5)" : "rgba(250, 246, 246, 1)",
												color: "#000",
												width: "48%",
												opacity: "0.5",
											}}
											onClick={() => handleDontShowAgainChange(true)}
											disabled
										>
											Yes
										</Button>
										<Button
											variant={!dontShowAgain ? "contained" : "outlined"}
											sx={{
												backgroundColor: !dontShowAgain ? "rgba(211, 217, 218, 0.5)" : "rgba(250, 246, 246, 1)",
												color: "#000",
												width: "48%",
												opacity: "0.5",
											}}
											onClick={() => handleDontShowAgainChange(false)}
											disabled
										>
											No
										</Button>
									</Box>
								</span>
							</Tooltip>
						</Box> */}

						</div>
					)}

									{/* <Box className="qadpt-control-box">
						<div
							className="qadpt-control-label"

						>
							Don't show again
							</div>
							<div>
									<label className="toggle-switch">
									<input
										type="checkbox"
										checked={dontShowAgain}
											onChange={() => { setDontShowAgain(!dontShowAgain) }}
										disabled={translaterue}//For temporary we are disableing for turn off
										/>
										<span className="slider"></span>
								</label>
								</div> */}
						{/* <Switch
      checked={dontShowAgain}
      onChange={handleDontShowAgainChange}
      value={dontShowAgain}
      sx={{
        color: dontShowAgain ? "rgba(211, 217, 218, 0.5)" : "rgba(250, 246, 246, 1)",
      }}
    /> */}
					{/* </Box> */}

					</div>
				<div className="qadpt-drawerFooter">
					<Button
						variant="contained"
						onClick={handleApplyChanges}
						className={`qadpt-btn`}
					>
						{translate("Apply")}
					</Button>

			</div>
			</div>
			</div>
	);
};

export default Elementssettings;
