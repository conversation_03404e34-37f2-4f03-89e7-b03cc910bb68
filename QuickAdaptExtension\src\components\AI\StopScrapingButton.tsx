import React, { useState } from 'react';
import './EnableAIButton.css';
import { useTranslation } from 'react-i18next';
import useDrawerStore, { DrawerState } from '../../store/drawerStore';
import AgentAdditionalContextPopup from './AgentAdditionalContextPopup';
import { cancelTraining } from '../../services/ScrapingService';

interface StopScrapingButtonProps {
  onClick: () => void;
}

const StopScrapingButton: React.FC<StopScrapingButtonProps> = ({ onClick }) => {
  const {
    setIsAgentTraining,
    isAgentTraining
  } = useDrawerStore((state: DrawerState) => state);
  const { t: translate } = useTranslation();
  const [showAdditionalContext, setShowAdditionalContext] = useState(false);

  const handleClick = () => {
    if(isAgentTraining){
    setShowAdditionalContext(true);
    }
    else
    {
      onClick();
    }
  };

  const handleCancel = async () => {
    try {
      // Cancel the training process (stops scraping and clears data)
      await cancelTraining();

      // Update the training state
      setIsAgentTraining(false);

      // Close the popup
      setShowAdditionalContext(false);
    } catch (error) {
      console.error('Error canceling training:', error);
      // Still close the popup even if there's an error
      setShowAdditionalContext(false);
    }
  };

  return (
    <div className='stop-scraping-button-container' id='stop-scraping-button'>
      <button className="enable-ai-button stop-scraping-button" onClick={handleClick}>
        <span className="enable-ai-text">{translate("Stop Training")}</span>
      </button>
      {showAdditionalContext &&  isAgentTraining &&  (
        <AgentAdditionalContextPopup
          open={showAdditionalContext}
          onClose={() => setShowAdditionalContext(false)}
          onSaved={() => {
            setShowAdditionalContext(false);
            onClick();
          }}
          onCancel={handleCancel}
        />
      )}
    </div>
  );
};

export default StopScrapingButton;