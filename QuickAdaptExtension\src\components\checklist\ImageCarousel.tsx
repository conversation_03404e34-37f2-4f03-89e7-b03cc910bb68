import React, { useEffect, useState } from "react";
import { useTranslation } from 'react-i18next';
import useDrawerStore from "../../store/drawerStore";

interface ImageCarouselProps {
  images: string[];
  selectedItem: any;
  activeItem: any;
  isMaximized: any;
}

const ImageCarousel: React.FC<ImageCarouselProps> = ({ images, selectedItem,activeItem,isMaximized }) => {
  const { t: translate } = useTranslation();
  const [currentIndex, setCurrentIndex] = useState(0);
  const {
    checklistGuideMetaData,

  } = useDrawerStore((state: any) => state);
  useEffect(() =>
  {
    setCurrentIndex(0);
},[activeItem])
  // Function to change the image when clicking a progress dot
  const goToImage = (index: number) => {
    setCurrentIndex(index);
  };

  return (
    <div style={{
      width: "-webkit-fill-available",
      height: "244px" ,
    }} className="qadpt-imgsec">
      {/* 🖼️ Display Current Image */}
      <div style={{
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  height: '100%',
  width: '100%'
}}
>
        {selectedItem.supportingMedia.length > 0 && (
          <img
            src={images[currentIndex]} // Show selected image
            alt={translate('Image {{index}}', { index: currentIndex, defaultValue: `Image ${currentIndex}` })}
            style={{
              width: "100%",
              height: "100%",
              borderRadius: "10px",
              transition: "opacity 0.5s ease-in-out", // Smooth transition effect
              objectFit:isMaximized ? "contain" :"initial",
            }}
          />
        )}
       
      </div>

      {/* 🔵 Progress Dots */}
      <div style={{ marginTop: "5px" , position: "relative"}}>
        {images.map((_, index) => (
          <span
            key={index}
            onClick={() => goToImage(index)} // Set index correctly
            style={{
              height: "6px",
              width: "6px",
              margin: "3px",
              display: "inline-block",
              backgroundColor: currentIndex === index ? checklistGuideMetaData[0]?.canvas?.primaryColor : "gray",
              borderRadius: "50%",
              cursor: "pointer",
              transition: "background-color 0.3s",
            }}
          />
        ))}
      </div>
    </div>
  );
};

export default ImageCarousel;
