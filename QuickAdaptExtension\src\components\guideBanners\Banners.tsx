import React, { useState, useRef, useEffect } from "react";
import { IconButton, Box, Typography, MobileStepper, Button, LinearProgress } from "@mui/material";
import AddIcon from "@mui/icons-material/Add";
import DeleteOutlineOutlinedIcon from "@mui/icons-material/DeleteOutlineOutlined";
import { TextFormat, Link } from "@mui/icons-material";
import RTEsection from "../guideSetting/PopupSections/RTEsection";
import ImageSectionField from "./selectedpopupfields/ImageSectionField";
import ButtonSection from "../guideSetting/PopupSections/Button";
import HtmlSection from "../guideSetting/PopupSections/HtmlSection";
import EmojiPicker from "emoji-picker-react";
import "./guideBanner.css";
import CloseIcon from "@mui/icons-material/Close";
import useDrawerStore, { BUTTON_CONT_DEF_VALUE } from "../../store/drawerStore";
import ButtonSettings from "./selectedpopupfields/ButtonSettings";
import { saveGuide } from "../../services/GuideListServices";
import Tooltip from "@mui/material/Tooltip";
import AlertPopup from "../drawer/AlertPopup";
import { GuideData } from "../drawer/Drawer";
import { useTranslation } from "react-i18next";
const Banner = ({
	setImageSrc,
	imageSrc,
	textBoxRef,
	setHtmlContent,
	htmlContent,
	buttonColor,
	setButtonColor,
	setImageName,
	imageName,
	alignment,
	setAlignment,
	textvalue,
	setTextvalue,
	isBanner,
	overlays,
	setOverLays,
	Bposition,
	setBposition,
	bpadding,
	setbPadding,
	isUnSavedChanges,
	openWarning,
	setopenWarning,
	handleLeave,
	savedGuideData,
	Progress,
}: {
	setImageSrc: any;
	imageSrc: any;
	textBoxRef: any;
	setHtmlContent: any;
	htmlContent: any;
	buttonColor: any;
	setButtonColor: any;
	setImageName: any;
	imageName: any;
	alignment: any;
	setAlignment: any;
	textvalue: any;
	setTextvalue: any;
	isBanner: boolean;
	overlays: boolean;
	setOverLays: any;
	Bposition: any;
	setBposition: any;
	bpadding: any;
		setbPadding: any;
		isUnSavedChanges: boolean;
		openWarning: boolean;
		setopenWarning: (params: boolean) => void;
		handleLeave: () => void;
		savedGuideData: GuideData | null;
		Progress: any;
}) => {
	const guidePopUpRef = useRef<HTMLDivElement | null>(null);
	const {
		dismissData,
		sectionColor,
		setSectionColor,
		buttonProperty,
		setButtonProperty,
		BborderSize,
		Bbordercolor,
		backgroundC,
		textArray,
		setBannerButtonSelected,
		setTextArray,
		preview,
		setPreview,
		bannerButtonSelected,
		clearGuideDetails,
		btnBgColor,
		btnTextColor,
		btnBorderColor,
		buttonsContainer,
		clearBannerButtonDetials,
		setRTEAnchorEl,
		deleteRTEContainer,
		rteAnchorEl,
		bannerJson,
		currentStep,
		selectedOption,
		ProgressColor,
		setProgressColor,
		steps,
		progress,
		createWithAI,
		selectedTemplate,
		syncAIAnnouncementDataForPreview,
		rtesContainer
	} = useDrawerStore((state) => state);
	const { t: translate } = useTranslation()
	const [textAreas, setTextAreas] = useState<{ name: string; value: string | JSX.Element }[][]>([
		[
		  {
			name: "Rich Text",
			value: (
			  <RTEsection
				key={0}
				textBoxRef={textBoxRef}
				isBanner={true}
				index={0}
				handleDeleteRTESection={() => {}}
				// @ts-ignore
				ref={textBoxRef}
					guidePopUpRef={guidePopUpRef}
			  />
			),
		  },
		],
	  ]);

	  // Effect to restore textAreas state from persisted store data on component mount
	  useEffect(() => {
		// Only run this restoration logic once on component mount
		const restoreTextAreasFromStore = () => {
			const rowIndex = 0;
			let restoredTextAreas = [...textAreas];
			let hasChanges = false;

			// Check if we need to restore button state from the store
			if (bannerButtonSelected && !textAreas[rowIndex].some((item) => item.name === "Button")) {
				const newButton = {
					name: "Button",
					value: (
						<ButtonSection
							key={0}
							buttonColor={buttonColor}
							setButtonColor={setButtonColor}
							isBanner={true}
						/>
					),
				};
				restoredTextAreas[rowIndex] = [...restoredTextAreas[rowIndex], newButton];
				hasChanges = true;
			}

			// Check if RTE content exists in the store and needs to be restored
			// For banners, RTE content is stored in rtesContainer
			const hasRTEContent = rtesContainer && rtesContainer.length > 0 &&
				rtesContainer[0].rtes && rtesContainer[0].rtes.length > 0 &&
				rtesContainer[0].rtes[0].text && rtesContainer[0].rtes[0].text.trim() !== "";

			// If there's RTE content in the store but the banner is not visible,
			// it means we need to restore the textAreas properly
			if (hasRTEContent) {
				// console.log("Banner: Restoring RTE content from store", {
				// 	rteContent: rtesContainer[0].rtes[0].text.substring(0, 50) + "...",
				// 	currentTextAreas: textAreas.length
				// });

				// Ensure the RTE section is properly initialized with the stored content
				// The RTEsection component will automatically pick up the content from rtesContainer
				// We just need to make sure the textAreas structure is correct
				const hasRTEInTextAreas = restoredTextAreas[rowIndex].some((item) => item.name === "Rich Text");

				if (!hasRTEInTextAreas) {
					// This shouldn't happen normally, but if it does, recreate the RTE section
					const rteSection = {
						name: "Rich Text",
						value: (
							<RTEsection
								key={0}
								textBoxRef={textBoxRef}
								isBanner={true}
								index={0}
								handleDeleteRTESection={() => {}}
								// @ts-ignore
								ref={textBoxRef}
								guidePopUpRef={guidePopUpRef}
							/>
						),
					};
					restoredTextAreas[rowIndex] = [rteSection, ...restoredTextAreas[rowIndex].filter(item => item.name !== "Rich Text")];
					hasChanges = true;
				}
			}

			// Update textAreas if changes were made
			if (hasChanges) {
				setTextAreas(restoredTextAreas);
			}
		};

		// Run restoration logic after a small delay to ensure store is fully loaded
		const timeoutId = setTimeout(restoreTextAreasFromStore, 100);

		return () => clearTimeout(timeoutId);
	  }, []); // Empty dependency array - only run once on mount

	  // UseEffect to update textAreas when setBannerButtonSelected changes
	useEffect(() => {
		const rowIndex = 0; // Assuming we want to update the first row
		const existingRow = textAreas[rowIndex];
		if (bannerButtonSelected) {
		  // Check if Button already exists in the first row


		  // If Button is not already in the row, add it
		  if (!existingRow.some((item) => item.name === "Button")) {
			const newButton = {
			  name: "Button",
			  value: (
				<ButtonSection
				  key={0}
				  buttonColor={buttonColor}
				  setButtonColor={setButtonColor}
				  isBanner={true}
				/>
			  ),
			};

			const updatedTextAreas = [...textAreas];
			updatedTextAreas[rowIndex] = [...existingRow, newButton];
			setTextAreas(updatedTextAreas);
		  }
		} else {
			// Find the button index and remove it
			const buttonIndex = existingRow.findIndex((item) => item.name === "Button");
			if (buttonIndex !== -1) {
				removeTextArea(rowIndex, buttonIndex);
			}
		}
	  }, [bannerButtonSelected]); // Trigger when setBannerButtonSelected changes

	  // Update textArray whenever textAreas changes
	  useEffect(() => {
		setTextArray(textAreas);
	  }, [textAreas]);

	  // Sync AI announcement data on component mount to ensure progress bar is visible
	  useEffect(() => {
		// Only run this for AI-created announcements
		if (!createWithAI || selectedTemplate !== "Announcement") return;

		console.log("Banner component mounted for AI announcement - syncing data");

		// Synchronize AI announcement data to ensure progress bar and other settings are properly initialized
		syncAIAnnouncementDataForPreview(true); // Preserve global state during banner initialization
	  }, [createWithAI, selectedTemplate, syncAIAnnouncementDataForPreview]);

	  // Sync textAreas with AI-created guide data on mount and when relevant data changes
	  useEffect(() => {
		// Only run this for AI-created guides
		if (!createWithAI) return;

		const rowIndex = 0;
		const existingRow = textAreas[rowIndex];

		// Check if we need to add a button based on the AI guide data
		const hasButtonInData = bannerButtonSelected;
		const hasButtonInTextAreas = existingRow.some((item) => item.name === "Button");

		if (hasButtonInData && !hasButtonInTextAreas) {
			// Add button to textAreas if it exists in AI data but not in local state
			const newButton = {
			  name: "Button",
			  value: (
				<ButtonSection
				  key={0}
				  buttonColor={buttonColor}
				  setButtonColor={setButtonColor}
				  isBanner={true}
				/>
			  ),
			};

			const updatedTextAreas = [...textAreas];
			updatedTextAreas[rowIndex] = [...existingRow, newButton];
			setTextAreas(updatedTextAreas);
		}
	  }, [createWithAI, bannerButtonSelected, buttonColor, setButtonColor]); // Dependencies for AI guide sync

	  // Additional effect to ensure button state is preserved for AI guides
	  useEffect(() => {
		if (!createWithAI) return;

		// Check if we have button data in the store but not in textAreas
		const rowIndex = 0;
		const existingRow = textAreas[rowIndex];
		const hasButtonInTextAreas = existingRow.some((item) => item.name === "Button");

		// If buttonsContainer has buttons but textAreas doesn't, restore the button
		if (buttonsContainer.length > 0 && !hasButtonInTextAreas && !bannerButtonSelected) {
			setBannerButtonSelected(true);
		}
	  }, [createWithAI, buttonsContainer, textAreas, bannerButtonSelected, setBannerButtonSelected]); // Monitor button container changes

	// Handle overflow behavior based on banner position in creation mode
	useEffect(() => {
		// Use a small delay to ensure this runs after resetHeightofBanner
		const timeoutId = setTimeout(() => {
			// Only apply overflow hidden in creation/edit mode when position is "Cover Top"
			// This component is only rendered in creation mode (when !showBannerenduser)
			// Preview mode is handled by separate preview components
			if (Bposition === "Cover Top") {
				document.body.style.setProperty("overflow", "hidden", "important");
			} else {
				// Restore normal scrolling for other positions
				document.body.style.removeProperty("overflow");
			}
		}, 100); // Small delay to ensure it runs after resetHeightofBanner

		// Cleanup function to restore overflow when component unmounts
		return () => {
			clearTimeout(timeoutId);
			document.body.style.removeProperty("overflow");
		};
	}, [Bposition]); // Re-run when position changes

	const overlayEnabled = useDrawerStore((state) => state.overlayEnabled);
	const [showOptions, setShowOptions] = useState(false);
	const [focusedRowIndex, setFocusedRowIndex] = useState<number | null>(null);
	const [showEmojiPicker, setShowEmojiPicker] = useState(false);

	const handleDeleteRTESection = (index: number) => {
		const newTextAreas = [...textAreas];
		newTextAreas.splice(index, 1);
		setTextAreas(newTextAreas);
		setTextArray(newTextAreas);

	};

	const addTextAreaInSameRow = (rowIndex: number, option: string) => {
		if (!textAreas[rowIndex]) {
			textAreas[rowIndex] = [];
		}

		const existingRow = textAreas[rowIndex];
		// Check if a Rich Text or Button already exists in the row
		if (
			(option === "richText" && existingRow.some((item) => item.name === "Rich Text")) ||
			(option === "button" && existingRow.some((item) => item.name === "Button"))
		) {
			alert(`Only one ${option === "richText" ? "Rich Text" : "Button"} is allowed per row.`);
			return;
		}

		let newValue: JSX.Element | string;
		let newName: string = "";

		switch (option) {
			case "image":
				newValue = (
					<ImageSectionField
						key={rowIndex}
						setImageSrc={setImageSrc}
						imageSrc={imageSrc}
						setImageName={setImageName}
					/>
				);
				newName = "Image";
				break;
			case "richText":
				// setBannerButtonSelected(false);

				newValue = (
					<RTEsection
						key={rowIndex}
						textBoxRef={textBoxRef}
						isBanner={true}
						index={rowIndex}
						handleDeleteRTESection={handleDeleteRTESection}
						// @ts-ignore
						ref={textBoxRef}
						guidePopUpRef={guidePopUpRef}
					/>
				);
				newName = "Rich Text";
				break;
			case "button":
				setBannerButtonSelected(true);

				newValue = (
					<ButtonSection
						key={rowIndex}
						buttonColor={buttonColor}
						setButtonColor={setButtonColor}
						isBanner={true}
					/>
				);
				newName = "Button";

				break;
			case "html":
				newValue = (
					<div className="htmlbanner">
						<HtmlSection
							key={rowIndex}
							htmlContent={htmlContent}
							setHtmlContent={setHtmlContent}
							isBanner={true}
						/>
					</div>
				);
				newName = "HTML";
				break;
			default:
				newValue = "";
				newName = "Text";
		}


		const newTextAreas = [...textAreas];

		newTextAreas[rowIndex] = [...existingRow, { name: newName, value: newValue }];
		setTextAreas(newTextAreas);
		setTextArray(newTextAreas);
	};

	const removeTextArea = (rowIndex: number, textAreaIndex: number) => {
		const updatedTextAreas = textAreas.map((row, index) => {
			if (index === rowIndex) {
			  const filteredRow = row.filter((_, idx) => idx !== textAreaIndex);

			  // Check if there are any buttons remaining in the row after removal
			  const hasButtonInRow = filteredRow.some((t) => t.name === "Button");
			  setBannerButtonSelected(hasButtonInRow);

			  return filteredRow;
			}
			return row;
		});
		if (textAreaIndex === 1)
		{
			clearBannerButtonDetials();
			// Close the button settings popup when button is deleted
			setButtonProperty(false);
		}
		else if (textAreaIndex === 0)
		{
			setRTEAnchorEl({
				rteId: "",
				containerId: "",
				// @ts-ignore
				value: null,
			});

			deleteRTEContainer(rteAnchorEl.containerId);
		}
		setTextAreas(updatedTextAreas);
		setTextArray(updatedTextAreas);

	};
	const enableProgress = savedGuideData?.GuideStep?.[0]?.Tooltip?.EnableProgress || false;
	function getProgressTemplate(selectedOption: any) {
		if (selectedOption === 1) {
			return "dots";
		} else if (selectedOption === 2) {
			return "linear";
		} else if (selectedOption === 3) {
			return "BreadCrumbs";
		}
        else if (selectedOption === 4) {
			return "breadcrumbs";
		}

		return savedGuideData?.GuideStep?.[0]?.Tooltip?.ProgressTemplate || "dots";
	}
	const progressTemplate = getProgressTemplate(selectedOption);
	const renderProgress = () => {
		// Check both the global progress state and the saved guide data for progress settings
		const enableProgressFromData = savedGuideData?.GuideStep?.[0]?.Tooltip?.EnableProgress;
		const shouldShowProgress = progress || enableProgressFromData;

		if(!shouldShowProgress) return null;

		if (progressTemplate === "dots") {
			return (
				<MobileStepper
					variant="dots"
					steps={steps.length}
					position="static"
					activeStep={currentStep - 1}
					sx={{ backgroundColor: "transparent", padding:"8px 0 0 0 !important",  "& .MuiMobileStepper-dotActive": {
                        backgroundColor: ProgressColor, // Active dot
                      }, }}
					backButton={<Button style={{ visibility: "hidden" }} />}
					nextButton={<Button style={{ visibility: "hidden" }} />}
				/>
			);
		}
        if (progressTemplate === "BreadCrumbs") {
			return (
                <Box sx={{padding:"8px 0 0 0 !important",display: "flex",
					alignItems: "center",
					placeContent: "center",
					gap: "5px"}}>
                  {/* Custom Step Indicators */}

                    {Array.from({ length: steps.length }).map((_, index) => (
                      <div
                        key={index}
                        style={{
                          width: '14px',
                          height: '4px',
                          backgroundColor: index === currentStep - 1 ? ProgressColor : '#e0e0e0', // Active color and inactive color
                          borderRadius: '100px',
                        }}
                      />
                    ))}

                </Box>
              );
		}
		if (progressTemplate === "breadcrumbs") {
			return (
				<Box sx={{padding:"8px 0 0 0 !important",display: "flex",
				alignItems: "center",
				placeContent: "flex-start",
				}}>
					<Typography sx={{fontSize:"12px", color: ProgressColor}} >
						 Step {currentStep} of {steps.length}
					</Typography>
				</Box>
			);
		}

		if (progressTemplate === "linear") {
			return (
				<Box sx={{    width: "calc(50% - 410px)",
					padding: "8px 0 0 0"}}>
					<Typography variant="body2">
						<LinearProgress
							variant="determinate"
							value={Progress}
                            sx={{'& .MuiLinearProgress-bar': {
                                backgroundColor: ProgressColor, // progress bar color
                              },}}
						/>
					</Typography>
				</Box>
			);
		}

		return null;
	};
	const style = bannerJson.GuideStep.find((step:any) => step.stepName === currentStep)?.Canvas as
		| Record<string, unknown>
		| undefined;
// Apply overflow hidden to body when canvas position is "Cover Top" in creation mode
useEffect(() => {
	if (style?.Position === "Cover Top") {
		document.body.style.overflow = "hidden";
	} else {
		document.body.style.overflow = "";
	}

	// Cleanup function to restore overflow when component unmounts
	return () => {
		document.body.style.overflow = "";
	};
}, [style?.Position]); // Re-run when canvas position changes

	return (
		<>
			{/* <Box
				sx={{
					position: "fixed",
					top: 0,
					left: 0,
					width: "100vw",
					height: "17vh",
					borderColor: (style?.BorderColor as string) || "defaultColor",
    zIndex: overlays ? 1300 : "",
    backgroundColor: style?.BackgroundColor as string | undefined,
				}}
			/> */}

			<div className="qadpt-container creation">
				<Box
					className="qadpt-box"
					sx={{
						padding: style?.Padding !== undefined && style?.Padding !== null ? `${style.Padding}px` : "10px",
						boxShadow: (style && (style?.Position === "Push Down")) ?  "none" : "0px 1px 15px rgba(0, 0, 0, 0.7)",
						borderTop:
							 `${style?.BorderSize || 2}px solid ${style?.BorderColor || "#f0f0f0"} !important`
								,
							borderRight:
							 `${style?.BorderSize || 2}px solid ${style?.BorderColor || "#f0f0f0"} !important`
								,
							borderLeft:
						`${style?.BorderSize || 2}px solid ${style?.BorderColor || "#f0f0f0"} !important` ,

					borderBottom: `${style?.BorderSize || 2}px solid ${style?.BorderColor || "#f0f0f0"} !important`
							,
					backgroundColor: `${style?.BackgroundColor} !important ` || "#f0f0f0",
					position: style?.Position || "Cover Top",
					zIndex: style?.Zindex || 9999,
				}}
				id="guide-popup"
				ref={guidePopUpRef}
			>


{(textArray ).map((row: any, rowIndex: number) => (
  <Box key={rowIndex} className="qadpt-row">
    {row.map((textArea: any, textAreaIndex: number) => (
      <Box key={textAreaIndex} className="qadpt-text-area-wrapper">
        <div
          className="qadpt-text-area"
          style={{
            backgroundColor:
				  (textArea.name === "RTE" || textArea.name === "Rich Text") && sectionColor ? sectionColor : "",
          }}
        >
				{textArea.value}
        </div>
          {textArea.name === "Button" && (
            <IconButton
              className="qadpt-add-btn"
              size="large"
              onClick={() => removeTextArea(rowIndex, textAreaIndex)}
            >
              <DeleteOutlineOutlinedIcon />
            </IconButton>
          )}
      </Box>
    ))}


      <Box display="flex" alignItems="center">
	  {row.some((item: any) => item.name === "Button") ? (
				<Tooltip title={translate("Only one button is allowed")} placement="bottom">
          <span>
            <IconButton
              onClick={() => {
                setShowOptions(true);
                setFocusedRowIndex(rowIndex);
              }}
              className="qadpt-add-btn"
              size="small"
			  disabled={true}
					>
              <AddIcon />
            </IconButton>
          </span>
				</Tooltip>
			) : (
				<IconButton
				onClick={() => {
				  setShowOptions(true);
				  setFocusedRowIndex(rowIndex);
				}}
				className="qadpt-add-btn"
				size="small"
				disabled={row.some((item: any) => item.name === "Button")}
			  >
				<AddIcon />
			  </IconButton>
			)}
        {/* Always render the dismiss icon, but conditionally show/hide it based on dismissData.dismisssel */}
        <IconButton
          sx={{
						// position: "fixed",
						padding:"3px",
            boxShadow: "rgba(0, 0, 0, 0.15) 0px 4px 8px",
            marginLeft: "2px",
            background: "#fff !important",
            border: "1px solid #ccc",
            zIndex:"999999",
            display: dismissData?.dismisssel ? "flex" : "none", // Show/hide based on dismisssel
          }}
        >
					<CloseIcon  sx={{zoom:"1",color:"#000" }}   />
        </IconButton>
      </Box>
	  {isUnSavedChanges && openWarning &&(
			<AlertPopup
					openWarning={openWarning}
					setopenWarning={setopenWarning}
					handleLeave={handleLeave}
				/>
			)}
    {showOptions && focusedRowIndex === rowIndex && (
      <Box
        onMouseEnter={() => setShowOptions(true)}
        onMouseLeave={() => setShowOptions(false)}
        className="qadpt-options-menu"
      >
        <Box className="qadpt-options-content">
          <Box
            display="flex"
            flexDirection="row"
            alignItems="center"
            sx={{ cursor: "pointer",gap : "10px",placeContent:"center",width:"100%" }}

            onClick={() => addTextAreaInSameRow(rowIndex, "button")}
					>
				<Link />
						<Typography variant="caption">{translate("Button")}</Typography>
          </Box>
        </Box>
      </Box>
    )}
  </Box>
))}
{(savedGuideData?.GuideType === "Tour" || savedGuideData?.GuideType === "Announcement") &&

<Box
sx={{
  ...(progressTemplate === "linear" && {
	display: "flex",
	placeContent: "center",
	alignItems:"center"
  }),
}}
>
		{steps.length >= 1 ? (
                    <>
                        {renderProgress()}
                    </>
                ) : (
                    null
                )}
				</Box>

}
					{showEmojiPicker && <EmojiPicker onEmojiClick={() => {}} />}
				</Box>
			</div>
			{buttonProperty  && <ButtonSettings />}

			{/* {dismissData?.dismisssel && (
				<IconButton
					sx={{
						position: "absolute",
						top: "8%",
						right: 0,
						color: "red",
						borderRadius: "2px",
						//padding: "5px",
						borderColor: "var(--border-color)",
						zIndex: "999",
					}}
					aria-label="close"
				>
					<CloseIcon />
				</IconButton>
			)} */}
		</>
	);
};

export default Banner;
