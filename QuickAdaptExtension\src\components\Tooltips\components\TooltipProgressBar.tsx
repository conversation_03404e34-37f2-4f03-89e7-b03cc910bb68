import React, { useEffect, useState } from "react";
import {
	Button,
	Tooltip,
	Box,
	LinearProgress,
	Typography,
	tooltipClasses,
	TooltipProps,
	MobileStepper,
	Breadcrumbs,
} from "@mui/material";
import { CustomIconButton } from "../../Bannerspreview/Button";
import CloseIcon from "@mui/icons-material/Close";
import { styled } from "@mui/material/styles";
interface ButtonAction {
	Action: string;
	ActionValue: string;
	TargetUrl: string;
}

interface ButtonProperties {
	Padding: number;
	Width: number;
	Font: number;
	FontSize: number;
	ButtonTextColor: string;
	ButtonBackgroundColor: string;
}

interface ButtonData {
	ButtonStyle: string;
	ButtonName: string;
	Alignment: string;
	BackgroundColor: string;
	ButtonAction: ButtonAction;
	Padding: {
		Top: number;
		Right: number;
		Bottom: number;
		Left: number;
	};
	ButtonProperties: ButtonProperties;
}
interface Step {
	xpath: string;
	content: string | JSX.Element;
	targetUrl: string;
	imageUrl: string;
	buttonData: ButtonData[];
}

interface TooltipGuideProps {
	steps: Step[];
	currentUrl: string;
	onClose: () => void;
	tooltipConfig: any;
}

const CustomWidthTooltip = styled(({ className, ...props }: TooltipProps) => (
	<Tooltip
		{...props}
		classes={{ popper: className }}
		id="Tooltip-unique"
	/>
))({
	[`& .${tooltipClasses.tooltip}`]: {
		//maxWidth: TOOLTIP_MX_WIDTH,
		backgroundColor: "#d58fcf",
		color: "black",
		fontSize: "14px",
		padding: "12px 16px",
		borderRadius: "8px",
		boxShadow: "0px 4px 12px rgba(0, 0, 0, 0.2)",
	},
	[`& .${tooltipClasses.arrow}`]: {
		color: "#d58fcf",
	},
});

const TooltipGuide: React.FC<TooltipGuideProps> = ({ steps, currentUrl, onClose, tooltipConfig }) => {
	const [currentStepIndex, setCurrentStepIndex] = useState(0);
	const [targetElement, setTargetElement] = useState<HTMLElement | null>(null);

	const currentStep = steps[currentStepIndex];

	const getElementByXPath = (xpath: string): HTMLElement | null => {
		const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
		return result.singleNodeValue as HTMLElement | null;
	};

	const progress = ((currentStepIndex + 1) / steps.length) * 100;

	useEffect(() => {
		if (currentUrl === currentStep?.targetUrl) {
			const element = getElementByXPath(currentStep.xpath);
			setTargetElement(element);
		} else {
			setTargetElement(null);
		}
	}, [currentStep, currentUrl]);
	const enableProgress = tooltipConfig.EnableProgress || false;
	const progressTemplate = tooltipConfig.ProgressTemplate || "dots";
	const handleNext = () => setCurrentStepIndex((prev) => Math.min(prev + 1, steps.length - 1));
	const handlePrevious = () => setCurrentStepIndex((prev) => Math.max(prev - 1, 0));

	const renderContent = () => {
		const hasImage = currentStep?.imageUrl.startsWith("data:image/") || currentStep?.imageUrl.startsWith("http");
		const hasText = typeof currentStep?.content === "string" || React.isValidElement(currentStep?.content);

		return (
			<Box>
				{hasImage && (
					<Box
						component="img"
						src={currentStep.imageUrl}
						alt="Step Image"
						width="100%"
					/>
				)}
				{hasText && (
					<Box
						dangerouslySetInnerHTML={{
							__html: typeof currentStep.content === "string" ? currentStep.content : "",
						}}
					/>
				)}
			</Box>
		);
	};
	const renderProgress = () => {
		if (!enableProgress) return null;

		if (progressTemplate === "dots") {
			return (
				<MobileStepper
					variant="dots"
					steps={steps.length}
					position="static"
					activeStep={currentStepIndex}
					sx={{ backgroundColor: "transparent", marginTop: "10px" }}
					backButton={<Button style={{ visibility: "hidden" }} />}
					nextButton={<Button style={{ visibility: "hidden" }} />}
				/>
			);
		}

		// if (progressTemplate === "numbers") {
		// 	return (
		// 		<Breadcrumbs
		// 			aria-label="breadcrumb"
		// 			sx={{ marginTop: "10px" }}
		// 		>
		// 			{steps.map((_, index) => (
		// 				<Typography
		// 					key={index}
		// 					color={index === currentStepIndex ? "primary" : "text.secondary"}
		// 				>
		// 					Step {index + 1} of {steps.length}
		// 				</Typography>
		// 			))}
		// 		</Breadcrumbs>
		// 	);
		// }

		if (progressTemplate === "breadcrumbs") {
			return (
				<Box mb={2}>
					<Typography variant="body2">
						{currentStepIndex + 1} / {steps.length}
					</Typography>
				</Box>
			);
		}

		return null;
	};

	const renderButtons = () => {
		return currentStep?.buttonData?.length > 0
			? currentStep.buttonData.map((button, index) => {
					const buttonStyle = {
						backgroundColor: button.ButtonProperties.ButtonBackgroundColor,
						color: button.ButtonProperties.ButtonTextColor,
						padding: `${button.Padding.Top}px ${button.Padding.Right}px ${button.Padding.Bottom}px ${button.Padding.Left}px`,
						width: button.ButtonProperties.Width ? `${button.ButtonProperties.Width}px` : "auto",
						fontSize: button.ButtonProperties.FontSize ? `${button.ButtonProperties.FontSize}px` : "14px",
						fontFamily: button.ButtonProperties.Font ? `"Font-${button.ButtonProperties.Font}` : "Arial",
						borderRadius: "15px",
						textTransform: "none",
						minWidth: "fit-content",
						boxShadow: "none !important", // Remove box shadow in normal state
						"&:hover": {
							boxShadow: "none !important", // Remove box shadow in hover state
							backgroundColor: button.ButtonProperties.ButtonBackgroundColor, // Keep the same background color on hover
							opacity: 0.9, // Slightly reduce opacity on hover for visual feedback
						},
					};
					const handleClick = () => {
						if (button.ButtonAction.Action === "Open Url" && button.ButtonAction.TargetUrl) {
							window.location.href = button.ButtonAction.TargetUrl;
						} else if (button.ButtonAction.Action === "Next") {
							handleNext();
						} else if (button.ButtonAction.Action === "Previous" || button.ButtonAction.Action === "previous") {
							handlePrevious();
						} else if (button.ButtonAction.Action === "Restart") {
							// Reset to the first step
							setCurrentStepIndex(0);
							// Check if we need to navigate to a different page (multi-page) or stay on current page (single-page)
							if (steps[0]?.targetUrl && steps[0].targetUrl !== window.location.href) {
								// Multi-page: Navigate to the first step's URL
								window.location.href = steps[0].targetUrl;
							} else {
								// Single-page: Just scroll to the first step's element
								if (steps[0]?.xpath) {
									const firstStepElement = getElementByXPath(steps[0].xpath);
									if (firstStepElement) {
										firstStepElement.scrollIntoView({ behavior: 'smooth' });
									}
								}
							}
						}
					};
					return (
						<Button
							key={index}
							variant="contained"
							sx={buttonStyle}
							onClick={handleClick}
						>
							{button.ButtonName}
						</Button>
					);
			  })
			: null;
	};

	const TooltipContent = (
		<Box>
			<Box
				position="absolute"
				top={8}
				right={8}
				display="flex"
				justifyContent="center"
				alignItems="center"
			>
				<CustomIconButton
					sx={{ color: "white" }}
					onClick={onClose}
				>
					<CloseIcon sx={{ fontSize: "medium" }} />
				</CustomIconButton>
			</Box>
			<Box mb={2}>{renderContent()}</Box>

			<Box
				mb={2}
				display="flex"
				justifyContent="space-between"
			>
				{renderButtons()}
			</Box>
			{enableProgress && <Box mb={2}>{renderProgress()}</Box>}
		</Box>
	);

	return (
		<>
			{targetElement && (
				<CustomWidthTooltip
					open
					title={TooltipContent}
					placement="top"
					arrow
					PopperProps={{
						anchorEl: targetElement,
					}}
				>
					<Box
						sx={{
							position: "absolute",
							top: targetElement.offsetTop,
							left: targetElement.offsetLeft,
							width: targetElement.offsetWidth,
							height: targetElement.offsetHeight,
						}}
					/>
				</CustomWidthTooltip>
			)}
		</>
	);
};

export default TooltipGuide;
