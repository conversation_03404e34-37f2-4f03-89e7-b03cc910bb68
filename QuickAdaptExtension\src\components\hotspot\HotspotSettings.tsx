import React, { useReducer, useState,useEffect } from "react";
import { Box, Typography, TextField, Grid, IconButton, Button, InputAdornment, FormControl, InputLabel, Select, MenuItem, SelectChangeEvent, FormControlLabel, Switch } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import useDrawerStore, { BUTTON_CONT_DEF_VALUE_1, CANVAS_DEFAULT_VALUE, IMG_CONT_DEF_VALUE } from "../../store/drawerStore";
import { HOTSPOT_DEFAULT_VALUE } from "../../store/drawerStore";
import {
  InfoFilled,
  QuestionFill,
  Reselect,
  Solid,
} from "../../assets/icons/icons";
import ArrowBackIosNewOutlinedIcon from "@mui/icons-material/ArrowBackIosNewOutlined";
import { useTranslation } from 'react-i18next';

const HotspotSettings = ({ currentGuide }: any) => {
	const { t: translate } = useTranslation();
    const {
			setHotspotPopup,
			setElementSelected,
			updatehotspots,
			setDesignPopup,
			toolTipGuideMetaData,
			setOpenTooltip,
			openTooltip,
			currentStep,
			setTooltipPositionByXpath,
			updateCanvasInTooltip,
			updateTooltipBtnContainer,
			updateTooltipImageContainer,
			pulseAnimationsH,
		setPulseAnimationsH,
			setIsUnSavedChanges
		} = useDrawerStore((state: any) => state);

		const [hotSpotProperties, setHotSpotProperties] = useState<any>(() => {
			// Get the current step's hotspot properties
			const currentStepIndex = currentStep - 1;
			const currentStepHotspots = toolTipGuideMetaData[currentStepIndex]?.hotspots;

			// Use the current step's hotspot properties if available, otherwise use default values
			const initialHotspotProperties = currentStepHotspots || {
				XPosition: "4",
				YPosition: "4",
				Type: "Question",
				Color: "yellow",
				Size: 16,
				PulseAnimation: pulseAnimationsH,
				stopAnimationUponInteraction: true,
				ShowUpon: "Hovering Hotspot",
				ShowByDefault: false,
			};
			return initialHotspotProperties;
		});

		const handleClose = () => {
			setHotspotPopup(false);
		};
		const handledesignclose = () => {
			setDesignPopup(false);
		};
		const handleSizeChange = (value: number) => {
			const sizeInPx = 16 + (value - 1) * 4;
			onPropertyChange("Size", sizeInPx);
		};

		const onReselectElement = () => {
			// setHotSpotProperties({
			// 	XPosition: "4",
			// 	YPosition: "4",
			// 	Type: "Question",
			// 	Color: "y4ellow",
			// 	Size: 1,
			// 	PulseAnimation: pulseAnimationsH,
			// 	stopAnimationUponInteraction: true,
			// 	ShowUpon: "Hovering Hotspot",
			// 	ShowByDefault: false,
			// });
			setElementSelected(false);
			handledesignclose();
			//updatehotspots(HOTSPOT_DEFAULT_VALUE);
			// updateCanvasInTooltip(CANVAS_DEFAULT_VALUE);
			//  updateTooltipBtnContainer(BUTTON_CONT_DEF_VALUE_1);
			//  updateTooltipImageContainer(IMG_CONT_DEF_VALUE);
			const existingHotspot = document.getElementById("hotspotBlinkCreation");
			const existingTooltip = document.getElementById("Tooltip-unique");

			const allElementsWithOutline = document.querySelectorAll<HTMLElement>("[style*='outline']");
			allElementsWithOutline.forEach((element) => {
				element.style.outline = ""; // Reset the outline (border) color
			});

			if (existingHotspot) {
				existingHotspot.remove();
			}
			// if (existingTooltip)
			// {
			//   existingTooltip.remove();
			// }
			setHotspotPopup(false);
			setIsUnSavedChanges(true);
		};

		const onPropertyChange = (key: any, value: any) => {
			setHotSpotProperties((prevState: any) => ({
				...prevState,
				[key]: value,
			}));
		};

		const handleApplyChanges = () => {
			updatehotspots(hotSpotProperties);
			handleClose();
			setHotspotPopup(false);
			setIsUnSavedChanges(true);
		};

		return (
			<div
				id="qadpt-designpopup"
				className="qadpt-designpopup"
			>
				<div className="qadpt-content">
					<div className="qadpt-design-header">
						<IconButton
							aria-label={translate("Go Back")}
							onClick={handleClose}
						>
							<ArrowBackIosNewOutlinedIcon />
						</IconButton>
						<div className="qadpt-title">{translate("Hotspot")}</div>
						<IconButton
							size="small"
							aria-label={translate("Close")}
							onClick={handleClose}
						>
							<CloseIcon />
						</IconButton>
					</div>
					<div className="qadpt-canblock">
						<div className="qadpt-controls">
							<Box className="qadpt-control-box" sx={{ cursor: "pointer" }}>
								<Typography className="qadpt-control-label">{translate("Reselect Element")}</Typography>
								<span
									onClick={onReselectElement}
									dangerouslySetInnerHTML={{ __html: Reselect }}
									style={{  padding: "5px", marginRight: "10px" }}
								/>{" "}
							</Box>
							<Box className="qadpt-position-grid">
								<Typography className="qadpt-ctrl-title">{translate("Position within Element")}</Typography>

								<div
									className="qadpt-controls"
									style={{ padding: "0px" }}
								>
									<Box
										className="qadpt-control-box"
										sx={{ padding: "0 !important",marginBottom:"0 !important" }}
									>
										<Typography
											className="qadpt-control-label"
											sx={{ fontSize: "12px !important", paddingLeft: "0 !important", margin: "3px" }}
										>
											X {translate("Axis Offset")}
										</Typography>
										<TextField
											variant="outlined"
											value={hotSpotProperties.XPosition}
											
											size="small"
											className="qadpt-control-input"
											onChange={(e) => onPropertyChange("XPosition", e.target.value)}
											InputProps={{
												endAdornment: "%",
												sx: {
								 
													"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" }, 
													"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
													"& fieldset":{border:"none"},

												},
											}}
										/>
									</Box>
									<Box
										className="qadpt-control-box"
										sx={{ padding: "0 !important",marginBottom:"0 !important" }}
									>
										<Typography
											className="qadpt-control-label"
											sx={{ fontSize: "12px !important", paddingLeft: "0 !important", margin: "3px" }}
										>
											Y {translate("Axis Offset")}
										</Typography>
										<TextField
											variant="outlined"
											value={hotSpotProperties.YPosition}
											
											size="small"
											className="qadpt-control-input"
											onChange={(e) => onPropertyChange("YPosition", e.target.value)}
											InputProps={{
												endAdornment: "%",
												sx: {
								 
													"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" }, 
													"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
													"& fieldset":{border:"none"},

												},
											}}
										/>
									</Box>
								</div>
							</Box>

							<Box
								id="qadpt-designpopup"
								className="qadpt-control-box"
								sx={{ flexDirection: "column", height: "auto !important",padding:"8px !important" }}
							>
								<Typography className="qadpt-control-label" sx={{ padding: "0 0 5px 0 !important" }}>{translate("Type")}</Typography>
								<Select
									value={hotSpotProperties.Type}
									onChange={(e) => onPropertyChange("Type", e.target.value)}
									displayEmpty
									className="qadpt-control-input"
									
									sx={{
										width :"100% !important",
										borderRadius: "12px",
										"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" },
										"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
										"& fieldset": { border: "none" },
										
									  }}
									size="small"
									renderValue={(selected) => (
										<Box sx={{ display: "flex", justifyContent: "space-between", width: "100%" }}>
											<span>{translate(selected)}</span>
											<span>
												{selected === "Info" && <span dangerouslySetInnerHTML={{ __html: InfoFilled }} />}
												{selected === "Question" && <span dangerouslySetInnerHTML={{ __html: QuestionFill }} />}
												{selected === "Solid" && <span dangerouslySetInnerHTML={{ __html: Solid }} />}
											</span>
										</Box>
									)}
								>
									<MenuItem
										id="qadpt-designpopup"
										value="Info"
									>
										<span>{translate("Info")}</span>
										<span style={{ marginLeft: "auto", display: "flex", alignItems: "center" }}>
											<span dangerouslySetInnerHTML={{ __html: InfoFilled }} />
										</span>
									</MenuItem>
									<MenuItem
										id="qadpt-designpopup"
										value="Question"
									>
										<span>{translate("Question")}</span>
										<span style={{ marginLeft: "auto", display: "flex", alignItems: "center" }}>
											<span dangerouslySetInnerHTML={{ __html: QuestionFill }} />
										</span>
									</MenuItem>
									<MenuItem
										id="qadpt-designpopup"
										value="Solid"
									>
										<span>{translate("Solid")}</span>
										<span style={{ marginLeft: "auto", display: "flex", alignItems: "center" }}>
											<span dangerouslySetInnerHTML={{ __html: Solid }} />
										</span>
									</MenuItem>
								</Select>
							</Box>

							<Box
								id="qadpt-designpopup"
								className="qadpt-control-box"
							>
								<Typography
									id="qadpt-designpopup"
									className="qadpt-control-label"
								>
									{translate("Color")}
								</Typography>
								<input
									type="color"
									value={hotSpotProperties.Color}
									onChange={(e) => onPropertyChange("Color", e.target.value)}
									className="qadpt-color-input"
								/>
							</Box>

							<Box
								id="qadpt-designpopup"
								className="qadpt-control-box"
							>
								<Typography
									id="qadpt-designpopup"
									className="qadpt-control-label"
								>
									{translate("Size")}
								</Typography>

								<FormControl
									id="qadpt-designpopup"
									variant="outlined"
									fullWidth
									className="qadpt-control-input"
								>
									<Select
										defaultValue={1}
										id="qadpt-designpopup"
										value={(hotSpotProperties.Size - 16) / 4 + 1}
										onChange={(e) => handleSizeChange(Number(e.target.value))}
										sx={{
											borderRadius: "12px",
											"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" },
											"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
											"& fieldset": { border: "none" },
										  }}
									>
										{[1, 2, 3, 4, 5, 6, 7, 8, 9].map((size) => (
											<MenuItem
												id="qadpt-designpopup"
												key={size}
												value={size}
											>
												{size}
											</MenuItem>
										))}
									</Select>
								</FormControl>
							</Box>
							<Box
								id="qadpt-designpopup"
								className="qadpt-control-box"
								sx={{ flexDirection: "column", height: "auto !important", gap: "8px",padding:"8px !important" }}
							>
								{/* Pulse Animation Toggle */}
								<Box
									id="qadpt-designpopup"
									className="qadpt-control-item"
									sx={{
										display: "flex",
										alignItems: "center",
										justifyContent: "space-between",
										width: "100%",
									}}
								>
									<Typography
										className="qadpt-control-label"
										sx={{ textAlign: "left", minWidth: "130px" ,padding:"0 !important"}}
									>
										{translate("Pulse Animation")}
									</Typography>
									<label className="toggle-switch">
    <input
        type="checkbox"
        checked={pulseAnimationsH}
        onChange={(e) => {
            onPropertyChange("PulseAnimation", e.target.checked);
            setPulseAnimationsH(e.target.checked);
        }}
        name="pulseAnimation"
    />
    <span className="slider"></span>
</label>
								</Box>
								{pulseAnimationsH && (
									<Box
										id="qadpt-designpopup"
										className="qadpt-control-item"
										sx={{
											display: "flex",
											alignItems: "center",
											justifyContent: "space-between",
											width:"100%"
										}}
									>
										<Typography
											id="qadpt-designpopup"
											className="qadpt-control-label"
											sx={{ textAlign: "left", minWidth: "130px",width:"20px",padding:"0 !important" }}
										>
											{translate("Stop Animation Upon Interaction")}
										</Typography>
										<label className="toggle-switch">
    <input
        type="checkbox"
        checked={hotSpotProperties.stopAnimationUponInteraction}
        onChange={(e) => onPropertyChange("stopAnimationUponInteraction", e.target.checked)}
        name="stopAnimationUponInteraction"
    />
    <span className="slider"></span>
</label>

									</Box>
								)}
							</Box>
							{hotSpotProperties.ShowByDefault === false && (
								<Box
									id="qadpt-designpopup"
									className="qadpt-control-box"
									sx={{ flexDirection: "column", height: "auto !important" ,padding:"8px !important"}}
								>
									<Typography className="qadpt-control-label" sx={{ padding: "0 0 5px 0 !important" }}>{translate("Show Upon")}</Typography>

									{/* Show Upon Dropdown */}
									<FormControl
										variant="outlined"
										fullWidth
										className="qadpt-control-input"
										sx={{
											borderRadius: "12px",
											width: "100% !important",
											"&:hover .MuiOutlinedInput-notchedOutline": { border: "none" },
											"&.Mui-focused .MuiOutlinedInput-notchedOutline": { border: "none" },
											"& fieldset": { border: "none" },
											"&.MuiInputBase-root":{border : "1px solid #a8a8a8 !important"}
										  }}
									>
										<Select
											value={hotSpotProperties.ShowUpon} // Ensure this value is correctly tied to the state
											onChange={(e: any) => onPropertyChange("ShowUpon", e.target.value)}
											name="ShowUpon"
											sx={{ width: "100% !important", borderRadius: "12px" }}
										>
											<MenuItem value="Clicking Hotspot">{translate("Clicking Hotspot")}</MenuItem>
											<MenuItem value="Hovering Hotspot">{translate("Hovering Hotspot")}</MenuItem>
										</Select>
									</FormControl>
								</Box>
							)}
							<Box className="qadpt-control-box">
								<Typography className="qadpt-control-label">{translate("Show by Default")}</Typography>

								{/* Show by Default Toggle */}

								<label className="toggle-switch">
    <input
        type="checkbox"
        checked={hotSpotProperties.ShowByDefault}
        onChange={(e) => onPropertyChange("ShowByDefault", e.target.checked)}
        name="showByDefault"
    />
    <span className="slider"></span>
</label>

							</Box>
						</div>
					</div>
					<div className="qadpt-drawerFooter">
						<Button
							variant="contained"
							onClick={handleApplyChanges}
							className="qadpt-btn"
						>
							{translate("Apply")}
						</Button>
					</div>
				</div>
			</div>
		);
};

export default HotspotSettings;
