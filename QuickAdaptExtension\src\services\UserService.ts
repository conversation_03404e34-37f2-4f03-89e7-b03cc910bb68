import userManager from '../components/auth/UseAuth';
import { User } from "../models/User";
import {adminApiService, idsApiService } from "./APIService";


export const UserLogin = async (
	emailId: string,
	password: string,
	organizationId: string,
	rememberLogin: boolean,
	returnUrl: string = ""
): Promise<any> => {
	try {
		const requestUrl = `/ExtensionLogin`;
		const requestBody = {
			Email: emailId,
			Password: password,
			OrganizationId: organizationId,
			RememberLogin: rememberLogin,
			ReturnUrl: returnUrl,
		};  
		const response = await idsApiService.post(requestUrl, requestBody);  
		if (response.status === 200) {
			return response.data;
		} else {
			return response.data.ErrorMessage;
		}
	} catch (error) {
		return "Sorry , Unable to provide login for you, Please try again later"
	}
}

export const getAllUsers = async (): Promise<User[] | null> => {
	try {
		const response = await adminApiService.get<User[]>("/User/GetAllUsersForAllOrganizations");
		return response.data;
	} catch (error) {
		throw error;
	}
};
export const GetUserDetails = async (): Promise<User | null> => {
	try {
		const response = await adminApiService.get<User>("/User/GetUserDetails");
		return response.data;
	} catch (error) {
		return null;
	}
};
export const GetUserDetailsById = async (id: any) => {
	try {
		const response = await adminApiService.get<User>(`/User/GetUserByUserId?userId=${id}`);
		if (!response) {
			throw new Error("Network response was not ok");
		}
		return response
	}
	catch (error) {
		return null;
		
	}
}
export const GetUserByEmail = async (EmailId: string): Promise<User | null> => {
	try {
		const response = await adminApiService.get<User>(`/User/GetUserByEmail?emailId=${EmailId}`);
		return response.data;
	} catch (error) {
		userManager.signoutRedirect();
		return null;
	}
};
