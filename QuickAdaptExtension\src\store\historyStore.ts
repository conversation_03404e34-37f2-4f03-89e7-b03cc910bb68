import { create } from "zustand";
import { devtools } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";

// Define the types for our history entries
export interface HistoryEntry {
  id: string;
  timestamp: number;
  actionType: string;
  description: string;
  stateBefore: any;
  stateAfter: any;
  targetId?: string;
  metadata?: Record<string, any>;
}

// Define the history store state
export interface HistoryState {
  past: HistoryEntry[];
  future: HistoryEntry[];
  isRecording: boolean;
  maxHistorySize: number;
  
  // Actions
  recordChange: (entry: Omit<HistoryEntry, "id" | "timestamp">) => void;
  undo: () => HistoryEntry | null;
  redo: () => HistoryEntry | null;
  clearHistory: () => void;
  startRecording: () => void;
  stopRecording: () => void;
  setMaxHistorySize: (size: number) => void;
  canUndo: () => boolean;
  canRedo: () => boolean;
}

// Create the history store
const useHistoryStore = create<HistoryState>()(
  devtools(
    immer((set, get) => ({
      past: [],
      future: [],
      isRecording: true,
      maxHistorySize: 50, // Default max history size
      
      // Record a new change
      recordChange: (entry) => {
        if (!get().isRecording) return;
        
        set((state) => {
          // Create a new history entry with ID and timestamp
          const newEntry: HistoryEntry = {
            ...entry,
            id: crypto.randomUUID(),
            timestamp: Date.now(),
          };
          
          // Add to past and clear future (since we're creating a new branch)
          state.past.push(newEntry);
          state.future = [];
          
          // Limit history size
          if (state.past.length > state.maxHistorySize) {
            state.past.shift(); // Remove oldest entry
          }
        });
      },
      
      // Undo the last change
      undo: () => {
        const { past, future } = get();
        
        if (past.length === 0) return null;
        
        const lastChange = past[past.length - 1];
        
        set((state) => {
          // Move the last action from past to future
          state.past.pop();
          state.future.unshift(lastChange);
        });
        
        return lastChange;
      },
      
      // Redo the last undone change
      redo: () => {
        const { future } = get();
        
        if (future.length === 0) return null;
        
        const nextChange = future[0];
        
        set((state) => {
          // Move the first action from future to past
          state.future.shift();
          state.past.push(nextChange);
        });
        
        return nextChange;
      },
      
      // Clear all history
      clearHistory: () => {
        set((state) => {
          state.past = [];
          state.future = [];
        });
      },
      
      // Start recording changes
      startRecording: () => {
        set((state) => {
          state.isRecording = true;
        });
      },
      
      // Stop recording changes
      stopRecording: () => {
        set((state) => {
          state.isRecording = false;
        });
      },
      
      // Set the maximum history size
      setMaxHistorySize: (size) => {
        set((state) => {
          state.maxHistorySize = size;
          
          // Trim history if needed
          if (state.past.length > size) {
            state.past = state.past.slice(state.past.length - size);
          }
        });
      },
      
      // Check if undo is available
      canUndo: () => {
        return get().past.length > 0;
      },
      
      // Check if redo is available
      canRedo: () => {
        return get().future.length > 0;
      },
    })),
    { name: "HistoryStore" }
  )
);

export default useHistoryStore;
