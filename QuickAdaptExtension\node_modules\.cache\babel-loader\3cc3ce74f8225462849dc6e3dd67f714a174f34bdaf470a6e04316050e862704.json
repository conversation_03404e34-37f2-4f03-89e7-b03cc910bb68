{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideSetting\\\\ElementRules.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { Box, Button, IconButton } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\nimport { useTranslation } from 'react-i18next';\n\n// import Draggable from \"react-draggable\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ElementRules = () => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const [isOpen, setIsOpen] = useState(true);\n  const handleClose = () => {\n    setIsOpen(false); // Close the popup when close button is clicked\n  };\n  if (!isOpen) return null;\n  return (\n    /*#__PURE__*/\n    // <Draggable>\n    _jsxDEV(\"div\", {\n      id: \"qadpt-designpopup\",\n      className: \"qadpt-designpopup\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-design-header\",\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            \"aria-label\": translate(\"close\"),\n            onClick: handleClose,\n            children: /*#__PURE__*/_jsxDEV(ArrowBackIosNewOutlinedIcon, {\n              className: \"qadpt-design-back\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 7\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 6\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-title\",\n            children: translate(\"Element Rules\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 6\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            \"aria-label\": translate(\"close\"),\n            onClick: handleClose,\n            children: /*#__PURE__*/_jsxDEV(CloseIcon, {\n              className: \"qadpt-design-close\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 7\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 6\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 5\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: \"flex\",\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            backgroundColor: \"#f9f7f7\",\n            borderRadius: \"10px\",\n            padding: \"12px\",\n            marginBottom: \"12px\"\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            sx: {\n              textTransform: \"none\",\n              borderRadius: \"8px\",\n              borderColor: \"#d1bcbc\",\n              color: \"#4c9999\",\n              width: \"100%\"\n            },\n            children: translate(\"Choose Element\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 6\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 5\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: \"flex\",\n            justifyContent: \"space-between\",\n            gap: \"12px\",\n            marginBottom: \"12px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            sx: {\n              textTransform: \"none\",\n              borderRadius: \"8px\",\n              borderColor: \"#d1bcbc\",\n              color: \"#4c9999\",\n              flex: 1\n            },\n            children: translate(\"Present\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 6\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            sx: {\n              textTransform: \"none\",\n              borderRadius: \"8px\",\n              borderColor: \"#d1bcbc\",\n              color: \"#4c9999\",\n              flex: 1\n            },\n            children: translate(\"Absent\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 6\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 5\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            sx: {\n              textTransform: \"none\",\n              borderRadius: \"8px\",\n              borderColor: \"#d1bcbc\",\n              color: \"#4c9999\",\n              width: \"100%\",\n              padding: \"12px\"\n            },\n            children: translate(\"Add Element\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 6\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 5\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 4\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 3\n    }, this)\n    //</Draggable>\n  );\n};\n_s(ElementRules, \"Dmz/GRX9Z70h1t2Vdo6pPKbIAoI=\", false, function () {\n  return [useTranslation];\n});\n_c = ElementRules;\nexport default ElementRules;\nvar _c;\n$RefreshReg$(_c, \"ElementRules\");", "map": {"version": 3, "names": ["React", "useState", "Box", "<PERSON><PERSON>", "IconButton", "CloseIcon", "ArrowBackIosNewOutlinedIcon", "useTranslation", "jsxDEV", "_jsxDEV", "ElementRules", "_s", "t", "translate", "isOpen", "setIsOpen", "handleClose", "id", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "sx", "display", "justifyContent", "alignItems", "backgroundColor", "borderRadius", "padding", "marginBottom", "variant", "textTransform", "borderColor", "color", "width", "gap", "flex", "_c", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/guideSetting/ElementRules.tsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { Box, Button, Typography, IconButton } from \"@mui/material\";\r\nimport ArrowBackIosIcon from \"@mui/icons-material/ArrowBackIos\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\n// import Draggable from \"react-draggable\";\r\n\r\nconst ElementRules = () => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst [isOpen, setIsOpen] = useState(true);\r\n\tconst handleClose = () => {\r\n\t\tsetIsOpen(false); // Close the popup when close button is clicked\r\n\t};\r\n\r\n\tif (!isOpen) return null;\r\n\treturn (\r\n\t\t// <Draggable>\r\n\t\t<div\r\n\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\tclassName=\"qadpt-designpopup\"\r\n\t\t>\r\n\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t{/* Header */}\r\n\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\taria-label={translate(\"close\")}\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<ArrowBackIosNewOutlinedIcon  className=\"qadpt-design-back\"/>\r\n\t\t\t\t\t\t{/* Header */}\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t<div className=\"qadpt-title\">{translate(\"Element Rules\")}</div>\r\n\t\t\t\t\t{/* Close Button */}\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\taria-label={translate(\"close\")}\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<CloseIcon className=\"qadpt-design-close\"/>\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t{/* Choose Element Button */}\r\n\t\t\t\t<Box\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\tbackgroundColor: \"#f9f7f7\",\r\n\t\t\t\t\t\tborderRadius: \"10px\",\r\n\t\t\t\t\t\tpadding: \"12px\",\r\n\t\t\t\t\t\tmarginBottom: \"12px\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\tborderColor: \"#d1bcbc\",\r\n\t\t\t\t\t\t\tcolor: \"#4c9999\",\r\n\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{translate(\"Choose Element\")}\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t</Box>\r\n\r\n\t\t\t\t{/* Present & Absent Buttons */}\r\n\t\t\t\t<Box\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\tjustifyContent: \"space-between\",\r\n\t\t\t\t\t\tgap: \"12px\",\r\n\t\t\t\t\t\tmarginBottom: \"12px\",\r\n\t\t\t\t\t}}\r\n\t\t\t\t>\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\tborderColor: \"#d1bcbc\",\r\n\t\t\t\t\t\t\tcolor: \"#4c9999\",\r\n\t\t\t\t\t\t\tflex: 1,\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{translate(\"Present\")}\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\tborderColor: \"#d1bcbc\",\r\n\t\t\t\t\t\t\tcolor: \"#4c9999\",\r\n\t\t\t\t\t\t\tflex: 1,\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{translate(\"Absent\")}\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t</Box>\r\n\r\n\t\t\t\t{/* Add Element Button */}\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\tborderRadius: \"8px\",\r\n\t\t\t\t\t\t\tborderColor: \"#d1bcbc\",\r\n\t\t\t\t\t\t\tcolor: \"#4c9999\",\r\n\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\tpadding: \"12px\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{translate(\"Add Element\")}\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t</Box>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t\t//</Draggable>\r\n\t);\r\n};\r\n\r\nexport default ElementRules;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,GAAG,EAAEC,MAAM,EAAcC,UAAU,QAAQ,eAAe;AAEnE,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,2BAA2B,MAAM,6CAA6C;AACrF,SAASC,cAAc,QAAQ,eAAe;;AAE9C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGN,cAAc,CAAC,CAAC;EACzC,MAAM,CAACO,MAAM,EAAEC,SAAS,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAMe,WAAW,GAAGA,CAAA,KAAM;IACzBD,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;EACnB,CAAC;EAED,IAAI,CAACD,MAAM,EAAE,OAAO,IAAI;EACxB;IAAA;IACC;IACAL,OAAA;MACCQ,EAAE,EAAC,mBAAmB;MACtBC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAE7BV,OAAA;QAAKS,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAE7BV,OAAA;UAAKS,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBACnCV,OAAA,CAACL,UAAU;YACV,cAAYS,SAAS,CAAC,OAAO,CAAE;YAC/BO,OAAO,EAAEJ,WAAY;YAAAG,QAAA,eAErBV,OAAA,CAACH,2BAA2B;cAAEY,SAAS,EAAC;YAAmB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAElD,CAAC,eACbf,OAAA;YAAKS,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAEN,SAAS,CAAC,eAAe;UAAC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAE/Df,OAAA,CAACL,UAAU;YACVqB,IAAI,EAAC,OAAO;YACZ,cAAYZ,SAAS,CAAC,OAAO,CAAE;YAC/BO,OAAO,EAAEJ,WAAY;YAAAG,QAAA,eAErBV,OAAA,CAACJ,SAAS;cAACa,SAAS,EAAC;YAAoB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAGNf,OAAA,CAACP,GAAG;UACHwB,EAAE,EAAE;YACHC,OAAO,EAAE,MAAM;YACfC,cAAc,EAAE,QAAQ;YACxBC,UAAU,EAAE,QAAQ;YACpBC,eAAe,EAAE,SAAS;YAC1BC,YAAY,EAAE,MAAM;YACpBC,OAAO,EAAE,MAAM;YACfC,YAAY,EAAE;UACf,CAAE;UAAAd,QAAA,eAEFV,OAAA,CAACN,MAAM;YACN+B,OAAO,EAAC,UAAU;YAClBR,EAAE,EAAE;cACHS,aAAa,EAAE,MAAM;cACrBJ,YAAY,EAAE,KAAK;cACnBK,WAAW,EAAE,SAAS;cACtBC,KAAK,EAAE,SAAS;cAChBC,KAAK,EAAE;YACR,CAAE;YAAAnB,QAAA,EAEDN,SAAS,CAAC,gBAAgB;UAAC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGNf,OAAA,CAACP,GAAG;UACHwB,EAAE,EAAE;YACHC,OAAO,EAAE,MAAM;YACfC,cAAc,EAAE,eAAe;YAC/BW,GAAG,EAAE,MAAM;YACXN,YAAY,EAAE;UACf,CAAE;UAAAd,QAAA,gBAEFV,OAAA,CAACN,MAAM;YACN+B,OAAO,EAAC,UAAU;YAClBR,EAAE,EAAE;cACHS,aAAa,EAAE,MAAM;cACrBJ,YAAY,EAAE,KAAK;cACnBK,WAAW,EAAE,SAAS;cACtBC,KAAK,EAAE,SAAS;cAChBG,IAAI,EAAE;YACP,CAAE;YAAArB,QAAA,EAEDN,SAAS,CAAC,SAAS;UAAC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC,eACTf,OAAA,CAACN,MAAM;YACN+B,OAAO,EAAC,UAAU;YAClBR,EAAE,EAAE;cACHS,aAAa,EAAE,MAAM;cACrBJ,YAAY,EAAE,KAAK;cACnBK,WAAW,EAAE,SAAS;cACtBC,KAAK,EAAE,SAAS;cAChBG,IAAI,EAAE;YACP,CAAE;YAAArB,QAAA,EAEDN,SAAS,CAAC,QAAQ;UAAC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAGNf,OAAA,CAACP,GAAG;UAAAiB,QAAA,eACHV,OAAA,CAACN,MAAM;YACN+B,OAAO,EAAC,UAAU;YAClBR,EAAE,EAAE;cACHS,aAAa,EAAE,MAAM;cACrBJ,YAAY,EAAE,KAAK;cACnBK,WAAW,EAAE,SAAS;cACtBC,KAAK,EAAE,SAAS;cAChBC,KAAK,EAAE,MAAM;cACbN,OAAO,EAAE;YACV,CAAE;YAAAb,QAAA,EAEDN,SAAS,CAAC,aAAa;UAAC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;IACL;EAAA;AAEF,CAAC;AAACb,EAAA,CApHID,YAAY;EAAA,QACQH,cAAc;AAAA;AAAAkC,EAAA,GADlC/B,YAAY;AAsHlB,eAAeA,YAAY;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}