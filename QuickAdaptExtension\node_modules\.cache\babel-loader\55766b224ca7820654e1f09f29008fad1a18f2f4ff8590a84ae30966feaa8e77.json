{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\hotspot\\\\HotspotSettings.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { Box, Typography, TextField, IconButton, Button, FormControl, Select, MenuItem } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport useDrawerStore from \"../../store/drawerStore\";\nimport { InfoFilled, QuestionFill, Reselect, Solid } from \"../../assets/icons/icons\";\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\nimport { useTranslation } from 'react-i18next';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HotspotSettings = ({\n  currentGuide\n}) => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    setHotspotPopup,\n    setElementSelected,\n    updatehotspots,\n    setDesignPopup,\n    toolTipGuideMetaData,\n    setOpenTooltip,\n    openTooltip,\n    currentStep,\n    setTooltipPositionByXpath,\n    updateCanvasInTooltip,\n    updateTooltipBtnContainer,\n    updateTooltipImageContainer,\n    pulseAnimationsH,\n    setPulseAnimationsH,\n    setIsUnSavedChanges\n  } = useDrawerStore(state => state);\n  const [hotSpotProperties, setHotSpotProperties] = useState(() => {\n    var _toolTipGuideMetaData;\n    // Get the current step's hotspot properties\n    const currentStepIndex = currentStep - 1;\n    const currentStepHotspots = (_toolTipGuideMetaData = toolTipGuideMetaData[currentStepIndex]) === null || _toolTipGuideMetaData === void 0 ? void 0 : _toolTipGuideMetaData.hotspots;\n\n    // Use the current step's hotspot properties if available, otherwise use default values\n    const initialHotspotProperties = currentStepHotspots || {\n      XPosition: \"4\",\n      YPosition: \"4\",\n      Type: \"Question\",\n      Color: \"yellow\",\n      Size: 16,\n      PulseAnimation: pulseAnimationsH,\n      stopAnimationUponInteraction: true,\n      ShowUpon: \"Hovering Hotspot\",\n      ShowByDefault: false\n    };\n    return initialHotspotProperties;\n  });\n  const handleClose = () => {\n    setHotspotPopup(false);\n  };\n  const handledesignclose = () => {\n    setDesignPopup(false);\n  };\n  const handleSizeChange = value => {\n    const sizeInPx = 16 + (value - 1) * 4;\n    onPropertyChange(\"Size\", sizeInPx);\n  };\n  const onReselectElement = () => {\n    // setHotSpotProperties({\n    // \tXPosition: \"4\",\n    // \tYPosition: \"4\",\n    // \tType: \"Question\",\n    // \tColor: \"y4ellow\",\n    // \tSize: 1,\n    // \tPulseAnimation: pulseAnimationsH,\n    // \tstopAnimationUponInteraction: true,\n    // \tShowUpon: \"Hovering Hotspot\",\n    // \tShowByDefault: false,\n    // });\n    setElementSelected(false);\n    handledesignclose();\n    //updatehotspots(HOTSPOT_DEFAULT_VALUE);\n    // updateCanvasInTooltip(CANVAS_DEFAULT_VALUE);\n    //  updateTooltipBtnContainer(BUTTON_CONT_DEF_VALUE_1);\n    //  updateTooltipImageContainer(IMG_CONT_DEF_VALUE);\n    const existingHotspot = document.getElementById(\"hotspotBlinkCreation\");\n    const existingTooltip = document.getElementById(\"Tooltip-unique\");\n    const allElementsWithOutline = document.querySelectorAll(\"[style*='outline']\");\n    allElementsWithOutline.forEach(element => {\n      element.style.outline = \"\"; // Reset the outline (border) color\n    });\n    if (existingHotspot) {\n      existingHotspot.remove();\n    }\n    // if (existingTooltip)\n    // {\n    //   existingTooltip.remove();\n    // }\n    setHotspotPopup(false);\n    setIsUnSavedChanges(true);\n  };\n  const onPropertyChange = (key, value) => {\n    setHotSpotProperties(prevState => ({\n      ...prevState,\n      [key]: value\n    }));\n  };\n  const handleApplyChanges = () => {\n    updatehotspots(hotSpotProperties);\n    handleClose();\n    setHotspotPopup(false);\n    setIsUnSavedChanges(true);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    id: \"qadpt-designpopup\",\n    className: \"qadpt-designpopup\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-design-header\",\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          \"aria-label\": translate(\"Go Back\"),\n          onClick: handleClose,\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIosNewOutlinedIcon, {\n            className: \"qadpt-design-back\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-title\",\n          children: translate(\"Hotspot\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          \"aria-label\": translate(\"Close\"),\n          onClick: handleClose,\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {\n            className: \"qadpt-design-close\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 7\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 6\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-canblock\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-controls\",\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            sx: {\n              cursor: \"pointer\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              className: \"qadpt-control-label\",\n              children: translate(\"Reselect Element\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              onClick: onReselectElement,\n              dangerouslySetInnerHTML: {\n                __html: Reselect\n              },\n              style: {\n                padding: \"5px\",\n                marginRight: \"10px\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 9\n            }, this), \" \"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-position-grid\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              className: \"qadpt-ctrl-title\",\n              children: translate(\"Position within Element\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-controls\",\n              style: {\n                padding: \"0px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                className: \"qadpt-control-box\",\n                sx: {\n                  padding: \"0 !important\",\n                  marginBottom: \"0 !important\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  className: \"qadpt-control-label\",\n                  sx: {\n                    fontSize: \"12px !important\",\n                    paddingLeft: \"0 !important\",\n                    margin: \"3px\"\n                  },\n                  children: [\"X \", translate(\"Axis Offset\")]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 11\n                }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                  variant: \"outlined\",\n                  value: hotSpotProperties.XPosition,\n                  size: \"small\",\n                  className: \"qadpt-control-input\",\n                  onChange: e => onPropertyChange(\"XPosition\", e.target.value),\n                  InputProps: {\n                    endAdornment: \"%\",\n                    sx: {\n                      \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"& fieldset\": {\n                        border: \"none\"\n                      }\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 11\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                className: \"qadpt-control-box\",\n                sx: {\n                  padding: \"0 !important\",\n                  marginBottom: \"0 !important\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  className: \"qadpt-control-label\",\n                  sx: {\n                    fontSize: \"12px !important\",\n                    paddingLeft: \"0 !important\",\n                    margin: \"3px\"\n                  },\n                  children: [\"Y \", translate(\"Axis Offset\")]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 11\n                }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                  variant: \"outlined\",\n                  value: hotSpotProperties.YPosition,\n                  size: \"small\",\n                  className: \"qadpt-control-input\",\n                  onChange: e => onPropertyChange(\"YPosition\", e.target.value),\n                  InputProps: {\n                    endAdornment: \"%\",\n                    sx: {\n                      \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"& fieldset\": {\n                        border: \"none\"\n                      }\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 11\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            id: \"qadpt-designpopup\",\n            className: \"qadpt-control-box\",\n            sx: {\n              flexDirection: \"column\",\n              height: \"auto !important\",\n              padding: \"8px !important\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              className: \"qadpt-control-label\",\n              sx: {\n                padding: \"0 0 5px 0 !important\"\n              },\n              children: translate(\"Type\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: hotSpotProperties.Type,\n              onChange: e => onPropertyChange(\"Type\", e.target.value),\n              displayEmpty: true,\n              className: \"qadpt-control-input\",\n              sx: {\n                width: \"100% !important\",\n                borderRadius: \"12px\",\n                \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                  border: \"none\"\n                },\n                \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                  border: \"none\"\n                },\n                \"& fieldset\": {\n                  border: \"none\"\n                }\n              },\n              size: \"small\",\n              renderValue: selected => /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: \"flex\",\n                  justifyContent: \"space-between\",\n                  width: \"100%\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: translate(selected)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 12\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [selected === \"Info\" && /*#__PURE__*/_jsxDEV(\"span\", {\n                    dangerouslySetInnerHTML: {\n                      __html: InfoFilled\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 37\n                  }, this), selected === \"Question\" && /*#__PURE__*/_jsxDEV(\"span\", {\n                    dangerouslySetInnerHTML: {\n                      __html: QuestionFill\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 41\n                  }, this), selected === \"Solid\" && /*#__PURE__*/_jsxDEV(\"span\", {\n                    dangerouslySetInnerHTML: {\n                      __html: Solid\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 38\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 12\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 11\n              }, this),\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                id: \"qadpt-designpopup\",\n                value: \"Info\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: translate(\"Info\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 253,\n                  columnNumber: 11\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    marginLeft: \"auto\",\n                    display: \"flex\",\n                    alignItems: \"center\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    dangerouslySetInnerHTML: {\n                      __html: InfoFilled\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 255,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 11\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                id: \"qadpt-designpopup\",\n                value: \"Question\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: translate(\"Question\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 11\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    marginLeft: \"auto\",\n                    display: \"flex\",\n                    alignItems: \"center\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    dangerouslySetInnerHTML: {\n                      __html: QuestionFill\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 264,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 11\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                id: \"qadpt-designpopup\",\n                value: \"Solid\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: translate(\"Solid\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 11\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    marginLeft: \"auto\",\n                    display: \"flex\",\n                    alignItems: \"center\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    dangerouslySetInnerHTML: {\n                      __html: Solid\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 273,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 11\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            id: \"qadpt-designpopup\",\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              id: \"qadpt-designpopup\",\n              className: \"qadpt-control-label\",\n              children: translate(\"Color\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"color\",\n              value: hotSpotProperties.Color,\n              onChange: e => onPropertyChange(\"Color\", e.target.value),\n              className: \"qadpt-color-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            id: \"qadpt-designpopup\",\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              id: \"qadpt-designpopup\",\n              className: \"qadpt-control-label\",\n              children: translate(\"Size\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n              id: \"qadpt-designpopup\",\n              variant: \"outlined\",\n              fullWidth: true,\n              className: \"qadpt-control-input\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                defaultValue: 1,\n                id: \"qadpt-designpopup\",\n                value: (hotSpotProperties.Size - 16) / 4 + 1,\n                onChange: e => handleSizeChange(Number(e.target.value)),\n                sx: {\n                  borderRadius: \"12px\",\n                  \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                    border: \"none\"\n                  },\n                  \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                    border: \"none\"\n                  },\n                  \"& fieldset\": {\n                    border: \"none\"\n                  }\n                },\n                children: [1, 2, 3, 4, 5, 6, 7, 8, 9].map(size => /*#__PURE__*/_jsxDEV(MenuItem, {\n                  id: \"qadpt-designpopup\",\n                  value: size,\n                  children: size\n                }, size, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 12\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 10\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            id: \"qadpt-designpopup\",\n            className: \"qadpt-control-box\",\n            sx: {\n              flexDirection: \"column\",\n              height: \"auto !important\",\n              gap: \"8px\",\n              padding: \"8px !important\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              id: \"qadpt-designpopup\",\n              className: \"qadpt-control-item\",\n              sx: {\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n                width: \"100%\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                className: \"qadpt-control-label\",\n                sx: {\n                  textAlign: \"left\",\n                  minWidth: \"130px\",\n                  padding: \"0 !important\"\n                },\n                children: translate(\"Pulse Animation\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 354,\n                columnNumber: 10\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"toggle-switch\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: pulseAnimationsH,\n                  onChange: e => {\n                    onPropertyChange(\"PulseAnimation\", e.target.checked);\n                    setPulseAnimationsH(e.target.checked);\n                  },\n                  name: \"pulseAnimation\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 5\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"slider\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 370,\n                  columnNumber: 5\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 10\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 9\n            }, this), pulseAnimationsH && /*#__PURE__*/_jsxDEV(Box, {\n              id: \"qadpt-designpopup\",\n              className: \"qadpt-control-item\",\n              sx: {\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"space-between\",\n                width: \"100%\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                id: \"qadpt-designpopup\",\n                className: \"qadpt-control-label\",\n                sx: {\n                  textAlign: \"left\",\n                  minWidth: \"130px\",\n                  width: \"20px\",\n                  padding: \"0 !important\"\n                },\n                children: translate(\"Stop Animation Upon Interaction\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 11\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"toggle-switch\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"checkbox\",\n                  checked: hotSpotProperties.stopAnimationUponInteraction,\n                  onChange: e => onPropertyChange(\"stopAnimationUponInteraction\", e.target.checked),\n                  name: \"stopAnimationUponInteraction\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 392,\n                  columnNumber: 5\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"slider\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 5\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 11\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 10\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 8\n          }, this), hotSpotProperties.ShowByDefault === false && /*#__PURE__*/_jsxDEV(Box, {\n            id: \"qadpt-designpopup\",\n            className: \"qadpt-control-box\",\n            sx: {\n              flexDirection: \"column\",\n              height: \"auto !important\",\n              padding: \"8px !important\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              className: \"qadpt-control-label\",\n              sx: {\n                padding: \"0 0 5px 0 !important\"\n              },\n              children: translate(\"Show Upon\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 10\n            }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n              variant: \"outlined\",\n              fullWidth: true,\n              className: \"qadpt-control-input\",\n              sx: {\n                borderRadius: \"12px\",\n                width: \"100% !important\",\n                \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                  border: \"none\"\n                },\n                \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                  border: \"none\"\n                },\n                \"& fieldset\": {\n                  border: \"none\"\n                },\n                \"&.MuiInputBase-root\": {\n                  border: \"1px solid #a8a8a8 !important\"\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                value: hotSpotProperties.ShowUpon // Ensure this value is correctly tied to the state\n                ,\n                onChange: e => onPropertyChange(\"ShowUpon\", e.target.value),\n                name: \"ShowUpon\",\n                sx: {\n                  width: \"100% !important\",\n                  borderRadius: \"12px\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"Clicking Hotspot\",\n                  children: translate(\"Clicking Hotspot\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 432,\n                  columnNumber: 12\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"Hovering Hotspot\",\n                  children: translate(\"Hovering Hotspot\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 12\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 426,\n                columnNumber: 11\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 10\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 9\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: \"qadpt-control-box\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              className: \"qadpt-control-label\",\n              children: translate(\"Show by Default\")\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"toggle-switch\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                checked: hotSpotProperties.ShowByDefault,\n                onChange: e => onPropertyChange(\"ShowByDefault\", e.target.checked),\n                name: \"showByDefault\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 5\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"slider\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 450,\n                columnNumber: 5\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 9\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 8\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 7\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 6\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-drawerFooter\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: handleApplyChanges,\n          className: \"qadpt-btn\",\n          children: translate(\"Apply\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 7\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 456,\n        columnNumber: 6\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 5\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 118,\n    columnNumber: 4\n  }, this);\n};\n_s(HotspotSettings, \"bh9a9eZ34mT21n6SU+cBoyHkIFA=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c = HotspotSettings;\nexport default HotspotSettings;\nvar _c;\n$RefreshReg$(_c, \"HotspotSettings\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "TextField", "IconButton", "<PERSON><PERSON>", "FormControl", "Select", "MenuItem", "CloseIcon", "useDrawerStore", "InfoFilled", "QuestionFill", "Reselect", "Solid", "ArrowBackIosNewOutlinedIcon", "useTranslation", "jsxDEV", "_jsxDEV", "HotspotSettings", "currentGuide", "_s", "t", "translate", "setHotspotPopup", "setElementSelected", "updatehotspots", "setDesignPopup", "toolTipGuideMetaData", "setOpenTooltip", "openTooltip", "currentStep", "setTooltipPositionByXpath", "updateCanvasInTooltip", "updateTooltipBtnContainer", "updateTooltipImageContainer", "pulseAnimationsH", "setPulseAnimationsH", "setIsUnSavedChanges", "state", "hotSpotProperties", "setHotSpotProperties", "_toolTipGuideMetaData", "currentStepIndex", "currentStepHotspots", "hotspots", "initialHotspotProperties", "XPosition", "YPosition", "Type", "Color", "Size", "PulseAnimation", "stopAnimationUponInteraction", "ShowUpon", "ShowByDefault", "handleClose", "handledesignclose", "handleSizeChange", "value", "sizeInPx", "onPropertyChange", "onReselectElement", "existingHotspot", "document", "getElementById", "existingTooltip", "allElementsWithOutline", "querySelectorAll", "for<PERSON>ach", "element", "style", "outline", "remove", "key", "prevState", "handleApplyChanges", "id", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "sx", "cursor", "dangerouslySetInnerHTML", "__html", "padding", "marginRight", "marginBottom", "fontSize", "paddingLeft", "margin", "variant", "onChange", "e", "target", "InputProps", "endAdornment", "border", "flexDirection", "height", "displayEmpty", "width", "borderRadius", "renderValue", "selected", "display", "justifyContent", "marginLeft", "alignItems", "type", "fullWidth", "defaultValue", "Number", "map", "gap", "textAlign", "min<PERSON><PERSON><PERSON>", "checked", "name", "_c", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/hotspot/HotspotSettings.tsx"], "sourcesContent": ["import React, { useReducer, useState,useEffect } from \"react\";\r\nimport { Box, Typography, TextField, Grid, IconButton, Button, InputAdornment, FormControl, InputLabel, Select, MenuItem, SelectChangeEvent, FormControlLabel, Switch } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport useDrawerStore, { BUTTON_CONT_DEF_VALUE_1, CANVAS_DEFAULT_VALUE, IMG_CONT_DEF_VALUE } from \"../../store/drawerStore\";\r\nimport { HOTSPOT_DEFAULT_VALUE } from \"../../store/drawerStore\";\r\nimport {\r\n  InfoFilled,\r\n  QuestionFill,\r\n  Reselect,\r\n  Solid,\r\n} from \"../../assets/icons/icons\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nconst HotspotSettings = ({ currentGuide }: any) => {\r\n\tconst { t: translate } = useTranslation();\r\n    const {\r\n\t\t\tsetHotspotPopup,\r\n\t\t\tsetElementSelected,\r\n\t\t\tupdatehotspots,\r\n\t\t\tsetDesignPopup,\r\n\t\t\ttoolTipGuideMetaData,\r\n\t\t\tsetOpenTooltip,\r\n\t\t\topenTooltip,\r\n\t\t\tcurrentStep,\r\n\t\t\tsetTooltipPositionByXpath,\r\n\t\t\tupdateCanvasInTooltip,\r\n\t\t\tupdateTooltipBtnContainer,\r\n\t\t\tupdateTooltipImageContainer,\r\n\t\t\tpulseAnimationsH,\r\n\t\tsetPulseAnimationsH,\r\n\t\t\tsetIsUnSavedChanges\r\n\t\t} = useDrawerStore((state: any) => state);\r\n\r\n\t\tconst [hotSpotProperties, setHotSpotProperties] = useState<any>(() => {\r\n\t\t\t// Get the current step's hotspot properties\r\n\t\t\tconst currentStepIndex = currentStep - 1;\r\n\t\t\tconst currentStepHotspots = toolTipGuideMetaData[currentStepIndex]?.hotspots;\r\n\r\n\t\t\t// Use the current step's hotspot properties if available, otherwise use default values\r\n\t\t\tconst initialHotspotProperties = currentStepHotspots || {\r\n\t\t\t\tXPosition: \"4\",\r\n\t\t\t\tYPosition: \"4\",\r\n\t\t\t\tType: \"Question\",\r\n\t\t\t\tColor: \"yellow\",\r\n\t\t\t\tSize: 16,\r\n\t\t\t\tPulseAnimation: pulseAnimationsH,\r\n\t\t\t\tstopAnimationUponInteraction: true,\r\n\t\t\t\tShowUpon: \"Hovering Hotspot\",\r\n\t\t\t\tShowByDefault: false,\r\n\t\t\t};\r\n\t\t\treturn initialHotspotProperties;\r\n\t\t});\r\n\r\n\t\tconst handleClose = () => {\r\n\t\t\tsetHotspotPopup(false);\r\n\t\t};\r\n\t\tconst handledesignclose = () => {\r\n\t\t\tsetDesignPopup(false);\r\n\t\t};\r\n\t\tconst handleSizeChange = (value: number) => {\r\n\t\t\tconst sizeInPx = 16 + (value - 1) * 4;\r\n\t\t\tonPropertyChange(\"Size\", sizeInPx);\r\n\t\t};\r\n\r\n\t\tconst onReselectElement = () => {\r\n\t\t\t// setHotSpotProperties({\r\n\t\t\t// \tXPosition: \"4\",\r\n\t\t\t// \tYPosition: \"4\",\r\n\t\t\t// \tType: \"Question\",\r\n\t\t\t// \tColor: \"y4ellow\",\r\n\t\t\t// \tSize: 1,\r\n\t\t\t// \tPulseAnimation: pulseAnimationsH,\r\n\t\t\t// \tstopAnimationUponInteraction: true,\r\n\t\t\t// \tShowUpon: \"Hovering Hotspot\",\r\n\t\t\t// \tShowByDefault: false,\r\n\t\t\t// });\r\n\t\t\tsetElementSelected(false);\r\n\t\t\thandledesignclose();\r\n\t\t\t//updatehotspots(HOTSPOT_DEFAULT_VALUE);\r\n\t\t\t// updateCanvasInTooltip(CANVAS_DEFAULT_VALUE);\r\n\t\t\t//  updateTooltipBtnContainer(BUTTON_CONT_DEF_VALUE_1);\r\n\t\t\t//  updateTooltipImageContainer(IMG_CONT_DEF_VALUE);\r\n\t\t\tconst existingHotspot = document.getElementById(\"hotspotBlinkCreation\");\r\n\t\t\tconst existingTooltip = document.getElementById(\"Tooltip-unique\");\r\n\r\n\t\t\tconst allElementsWithOutline = document.querySelectorAll<HTMLElement>(\"[style*='outline']\");\r\n\t\t\tallElementsWithOutline.forEach((element) => {\r\n\t\t\t\telement.style.outline = \"\"; // Reset the outline (border) color\r\n\t\t\t});\r\n\r\n\t\t\tif (existingHotspot) {\r\n\t\t\t\texistingHotspot.remove();\r\n\t\t\t}\r\n\t\t\t// if (existingTooltip)\r\n\t\t\t// {\r\n\t\t\t//   existingTooltip.remove();\r\n\t\t\t// }\r\n\t\t\tsetHotspotPopup(false);\r\n\t\t\tsetIsUnSavedChanges(true);\r\n\t\t};\r\n\r\n\t\tconst onPropertyChange = (key: any, value: any) => {\r\n\t\t\tsetHotSpotProperties((prevState: any) => ({\r\n\t\t\t\t...prevState,\r\n\t\t\t\t[key]: value,\r\n\t\t\t}));\r\n\t\t};\r\n\r\n\t\tconst handleApplyChanges = () => {\r\n\t\t\tupdatehotspots(hotSpotProperties);\r\n\t\t\thandleClose();\r\n\t\t\tsetHotspotPopup(false);\r\n\t\t\tsetIsUnSavedChanges(true);\r\n\t\t};\r\n\r\n\t\treturn (\r\n\t\t\t<div\r\n\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\tclassName=\"qadpt-designpopup\"\r\n\t\t\t>\r\n\t\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\taria-label={translate(\"Go Back\")}\r\n\t\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t<ArrowBackIosNewOutlinedIcon  className=\"qadpt-design-back\"/>\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t<div className=\"qadpt-title\">{translate(\"Hotspot\")}</div>\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\taria-label={translate(\"Close\")}\r\n\t\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t<CloseIcon className=\"qadpt-design-close\"/>\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div className=\"qadpt-canblock\">\r\n\t\t\t\t\t\t<div className=\"qadpt-controls\">\r\n\t\t\t\t\t\t\t<Box className=\"qadpt-control-box\" sx={{ cursor: \"pointer\" }}>\r\n\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Reselect Element\")}</Typography>\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\tonClick={onReselectElement}\r\n\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: Reselect }}\r\n\t\t\t\t\t\t\t\t\tstyle={{  padding: \"5px\", marginRight: \"10px\" }}\r\n\t\t\t\t\t\t\t\t/>{\" \"}\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t<Box className=\"qadpt-position-grid\">\r\n\t\t\t\t\t\t\t\t<Typography className=\"qadpt-ctrl-title\">{translate(\"Position within Element\")}</Typography>\r\n\r\n\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-controls\"\r\n\t\t\t\t\t\t\t\t\tstyle={{ padding: \"0px\" }}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{ padding: \"0 !important\",marginBottom:\"0 !important\" }}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ fontSize: \"12px !important\", paddingLeft: \"0 !important\", margin: \"3px\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\tX {translate(\"Axis Offset\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\t\tvalue={hotSpotProperties.XPosition}\r\n\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"XPosition\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tendAdornment: \"%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" }, \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{ padding: \"0 !important\",marginBottom:\"0 !important\" }}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ fontSize: \"12px !important\", paddingLeft: \"0 !important\", margin: \"3px\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\tY {translate(\"Axis Offset\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\t\tvalue={hotSpotProperties.YPosition}\r\n\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"YPosition\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tendAdornment: \"%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" }, \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\tsx={{ flexDirection: \"column\", height: \"auto !important\",padding:\"8px !important\" }}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\" sx={{ padding: \"0 0 5px 0 !important\" }}>{translate(\"Type\")}</Typography>\r\n\t\t\t\t\t\t\t\t<Select\r\n\t\t\t\t\t\t\t\t\tvalue={hotSpotProperties.Type}\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"Type\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\tdisplayEmpty\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\twidth :\"100% !important\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\"& fieldset\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t  }}\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\trenderValue={(selected) => (\r\n\t\t\t\t\t\t\t\t\t\t<Box sx={{ display: \"flex\", justifyContent: \"space-between\", width: \"100%\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t<span>{translate(selected)}</span>\r\n\t\t\t\t\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{selected === \"Info\" && <span dangerouslySetInnerHTML={{ __html: InfoFilled }} />}\r\n\t\t\t\t\t\t\t\t\t\t\t\t{selected === \"Question\" && <span dangerouslySetInnerHTML={{ __html: QuestionFill }} />}\r\n\t\t\t\t\t\t\t\t\t\t\t\t{selected === \"Solid\" && <span dangerouslySetInnerHTML={{ __html: Solid }} />}\r\n\t\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<MenuItem\r\n\t\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\t\tvalue=\"Info\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<span>{translate(\"Info\")}</span>\r\n\t\t\t\t\t\t\t\t\t\t<span style={{ marginLeft: \"auto\", display: \"flex\", alignItems: \"center\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: InfoFilled }} />\r\n\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t</MenuItem>\r\n\t\t\t\t\t\t\t\t\t<MenuItem\r\n\t\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\t\tvalue=\"Question\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<span>{translate(\"Question\")}</span>\r\n\t\t\t\t\t\t\t\t\t\t<span style={{ marginLeft: \"auto\", display: \"flex\", alignItems: \"center\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: QuestionFill }} />\r\n\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t</MenuItem>\r\n\t\t\t\t\t\t\t\t\t<MenuItem\r\n\t\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\t\tvalue=\"Solid\"\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<span>{translate(\"Solid\")}</span>\r\n\t\t\t\t\t\t\t\t\t\t<span style={{ marginLeft: \"auto\", display: \"flex\", alignItems: \"center\" }}>\r\n\t\t\t\t\t\t\t\t\t\t\t<span dangerouslySetInnerHTML={{ __html: Solid }} />\r\n\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t</MenuItem>\r\n\t\t\t\t\t\t\t\t</Select>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{translate(\"Color\")}\r\n\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\t\t\tvalue={hotSpotProperties.Color}\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"Color\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{translate(\"Size\")}\r\n\t\t\t\t\t\t\t\t</Typography>\r\n\r\n\t\t\t\t\t\t\t\t<FormControl\r\n\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Select\r\n\t\t\t\t\t\t\t\t\t\tdefaultValue={1}\r\n\t\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\t\tvalue={(hotSpotProperties.Size - 16) / 4 + 1}\r\n\t\t\t\t\t\t\t\t\t\tonChange={(e) => handleSizeChange(Number(e.target.value))}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t  }}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{[1, 2, 3, 4, 5, 6, 7, 8, 9].map((size) => (\r\n\t\t\t\t\t\t\t\t\t\t\t<MenuItem\r\n\t\t\t\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tkey={size}\r\n\t\t\t\t\t\t\t\t\t\t\t\tvalue={size}\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t{size}\r\n\t\t\t\t\t\t\t\t\t\t\t</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t\t</Select>\r\n\t\t\t\t\t\t\t\t</FormControl>\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\tsx={{ flexDirection: \"column\", height: \"auto !important\", gap: \"8px\",padding:\"8px !important\" }}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{/* Pulse Animation Toggle */}\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-item\"\r\n\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent: \"space-between\",\r\n\t\t\t\t\t\t\t\t\t\twidth: \"100%\",\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{ textAlign: \"left\", minWidth: \"130px\" ,padding:\"0 !important\"}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{translate(\"Pulse Animation\")}\r\n\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t<label className=\"toggle-switch\">\r\n    <input\r\n        type=\"checkbox\"\r\n        checked={pulseAnimationsH}\r\n        onChange={(e) => {\r\n            onPropertyChange(\"PulseAnimation\", e.target.checked);\r\n            setPulseAnimationsH(e.target.checked);\r\n        }}\r\n        name=\"pulseAnimation\"\r\n    />\r\n    <span className=\"slider\"></span>\r\n</label>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t{pulseAnimationsH && (\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-item\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\tjustifyContent: \"space-between\",\r\n\t\t\t\t\t\t\t\t\t\t\twidth:\"100%\"\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-label\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ textAlign: \"left\", minWidth: \"130px\",width:\"20px\",padding:\"0 !important\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{translate(\"Stop Animation Upon Interaction\")}\r\n\t\t\t\t\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t\t\t\t\t\t<label className=\"toggle-switch\">\r\n    <input\r\n        type=\"checkbox\"\r\n        checked={hotSpotProperties.stopAnimationUponInteraction}\r\n        onChange={(e) => onPropertyChange(\"stopAnimationUponInteraction\", e.target.checked)}\r\n        name=\"stopAnimationUponInteraction\"\r\n    />\r\n    <span className=\"slider\"></span>\r\n</label>\r\n\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t{hotSpotProperties.ShowByDefault === false && (\r\n\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-box\"\r\n\t\t\t\t\t\t\t\t\tsx={{ flexDirection: \"column\", height: \"auto !important\" ,padding:\"8px !important\"}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\" sx={{ padding: \"0 0 5px 0 !important\" }}>{translate(\"Show Upon\")}</Typography>\r\n\r\n\t\t\t\t\t\t\t\t\t{/* Show Upon Dropdown */}\r\n\t\t\t\t\t\t\t\t\t<FormControl\r\n\t\t\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"12px\",\r\n\t\t\t\t\t\t\t\t\t\t\twidth: \"100% !important\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"& fieldset\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\t\t\"&.MuiInputBase-root\":{border : \"1px solid #a8a8a8 !important\"}\r\n\t\t\t\t\t\t\t\t\t\t  }}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<Select\r\n\t\t\t\t\t\t\t\t\t\t\tvalue={hotSpotProperties.ShowUpon} // Ensure this value is correctly tied to the state\r\n\t\t\t\t\t\t\t\t\t\t\tonChange={(e: any) => onPropertyChange(\"ShowUpon\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\t\t\tname=\"ShowUpon\"\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{ width: \"100% !important\", borderRadius: \"12px\" }}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"Clicking Hotspot\">{translate(\"Clicking Hotspot\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t\t<MenuItem value=\"Hovering Hotspot\">{translate(\"Hovering Hotspot\")}</MenuItem>\r\n\t\t\t\t\t\t\t\t\t\t</Select>\r\n\t\t\t\t\t\t\t\t\t</FormControl>\r\n\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Show by Default\")}</Typography>\r\n\r\n\t\t\t\t\t\t\t\t{/* Show by Default Toggle */}\r\n\r\n\t\t\t\t\t\t\t\t<label className=\"toggle-switch\">\r\n    <input\r\n        type=\"checkbox\"\r\n        checked={hotSpotProperties.ShowByDefault}\r\n        onChange={(e) => onPropertyChange(\"ShowByDefault\", e.target.checked)}\r\n        name=\"showByDefault\"\r\n    />\r\n    <span className=\"slider\"></span>\r\n</label>\r\n\r\n\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\tonClick={handleApplyChanges}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-btn\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{translate(\"Apply\")}\r\n\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t);\r\n};\r\n\r\nexport default HotspotSettings;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAgBC,QAAQ,QAAkB,OAAO;AAC7D,SAASC,GAAG,EAAEC,UAAU,EAAEC,SAAS,EAAQC,UAAU,EAAEC,MAAM,EAAkBC,WAAW,EAAcC,MAAM,EAAEC,QAAQ,QAAqD,eAAe;AAC5L,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,cAAc,MAA6E,yBAAyB;AAE3H,SACEC,UAAU,EACVC,YAAY,EACZC,QAAQ,EACRC,KAAK,QACA,0BAA0B;AACjC,OAAOC,2BAA2B,MAAM,6CAA6C;AACrF,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,eAAe,GAAGA,CAAC;EAAEC;AAAkB,CAAC,KAAK;EAAAC,EAAA;EAClD,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGP,cAAc,CAAC,CAAC;EACtC,MAAM;IACPQ,eAAe;IACfC,kBAAkB;IAClBC,cAAc;IACdC,cAAc;IACdC,oBAAoB;IACpBC,cAAc;IACdC,WAAW;IACXC,WAAW;IACXC,yBAAyB;IACzBC,qBAAqB;IACrBC,yBAAyB;IACzBC,2BAA2B;IAC3BC,gBAAgB;IACjBC,mBAAmB;IAClBC;EACD,CAAC,GAAG5B,cAAc,CAAE6B,KAAU,IAAKA,KAAK,CAAC;EAEzC,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzC,QAAQ,CAAM,MAAM;IAAA,IAAA0C,qBAAA;IACrE;IACA,MAAMC,gBAAgB,GAAGZ,WAAW,GAAG,CAAC;IACxC,MAAMa,mBAAmB,IAAAF,qBAAA,GAAGd,oBAAoB,CAACe,gBAAgB,CAAC,cAAAD,qBAAA,uBAAtCA,qBAAA,CAAwCG,QAAQ;;IAE5E;IACA,MAAMC,wBAAwB,GAAGF,mBAAmB,IAAI;MACvDG,SAAS,EAAE,GAAG;MACdC,SAAS,EAAE,GAAG;MACdC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,QAAQ;MACfC,IAAI,EAAE,EAAE;MACRC,cAAc,EAAEhB,gBAAgB;MAChCiB,4BAA4B,EAAE,IAAI;MAClCC,QAAQ,EAAE,kBAAkB;MAC5BC,aAAa,EAAE;IAChB,CAAC;IACD,OAAOT,wBAAwB;EAChC,CAAC,CAAC;EAEF,MAAMU,WAAW,GAAGA,CAAA,KAAM;IACzBhC,eAAe,CAAC,KAAK,CAAC;EACvB,CAAC;EACD,MAAMiC,iBAAiB,GAAGA,CAAA,KAAM;IAC/B9B,cAAc,CAAC,KAAK,CAAC;EACtB,CAAC;EACD,MAAM+B,gBAAgB,GAAIC,KAAa,IAAK;IAC3C,MAAMC,QAAQ,GAAG,EAAE,GAAG,CAACD,KAAK,GAAG,CAAC,IAAI,CAAC;IACrCE,gBAAgB,CAAC,MAAM,EAAED,QAAQ,CAAC;EACnC,CAAC;EAED,MAAME,iBAAiB,GAAGA,CAAA,KAAM;IAC/B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACArC,kBAAkB,CAAC,KAAK,CAAC;IACzBgC,iBAAiB,CAAC,CAAC;IACnB;IACA;IACA;IACA;IACA,MAAMM,eAAe,GAAGC,QAAQ,CAACC,cAAc,CAAC,sBAAsB,CAAC;IACvE,MAAMC,eAAe,GAAGF,QAAQ,CAACC,cAAc,CAAC,gBAAgB,CAAC;IAEjE,MAAME,sBAAsB,GAAGH,QAAQ,CAACI,gBAAgB,CAAc,oBAAoB,CAAC;IAC3FD,sBAAsB,CAACE,OAAO,CAAEC,OAAO,IAAK;MAC3CA,OAAO,CAACC,KAAK,CAACC,OAAO,GAAG,EAAE,CAAC,CAAC;IAC7B,CAAC,CAAC;IAEF,IAAIT,eAAe,EAAE;MACpBA,eAAe,CAACU,MAAM,CAAC,CAAC;IACzB;IACA;IACA;IACA;IACA;IACAjD,eAAe,CAAC,KAAK,CAAC;IACtBc,mBAAmB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMuB,gBAAgB,GAAGA,CAACa,GAAQ,EAAEf,KAAU,KAAK;IAClDlB,oBAAoB,CAAEkC,SAAc,KAAM;MACzC,GAAGA,SAAS;MACZ,CAACD,GAAG,GAAGf;IACR,CAAC,CAAC,CAAC;EACJ,CAAC;EAED,MAAMiB,kBAAkB,GAAGA,CAAA,KAAM;IAChClD,cAAc,CAACc,iBAAiB,CAAC;IACjCgB,WAAW,CAAC,CAAC;IACbhC,eAAe,CAAC,KAAK,CAAC;IACtBc,mBAAmB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,oBACCpB,OAAA;IACC2D,EAAE,EAAC,mBAAmB;IACtBC,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eAE7B7D,OAAA;MAAK4D,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC7B7D,OAAA;QAAK4D,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBACnC7D,OAAA,CAACd,UAAU;UACV,cAAYmB,SAAS,CAAC,SAAS,CAAE;UACjCyD,OAAO,EAAExB,WAAY;UAAAuB,QAAA,eAEtB7D,OAAA,CAACH,2BAA2B;YAAE+D,SAAS,EAAC;UAAmB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACblE,OAAA;UAAK4D,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAExD,SAAS,CAAC,SAAS;QAAC;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzDlE,OAAA,CAACd,UAAU;UACViF,IAAI,EAAC,OAAO;UACZ,cAAY9D,SAAS,CAAC,OAAO,CAAE;UAC/ByD,OAAO,EAAExB,WAAY;UAAAuB,QAAA,eAEtB7D,OAAA,CAACT,SAAS;YAACqE,SAAS,EAAC;UAAoB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eACNlE,OAAA;QAAK4D,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC9B7D,OAAA;UAAK4D,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC9B7D,OAAA,CAACjB,GAAG;YAAC6E,SAAS,EAAC,mBAAmB;YAACQ,EAAE,EAAE;cAAEC,MAAM,EAAE;YAAU,CAAE;YAAAR,QAAA,gBAC5D7D,OAAA,CAAChB,UAAU;cAAC4E,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAExD,SAAS,CAAC,kBAAkB;YAAC;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACxFlE,OAAA;cACC8D,OAAO,EAAElB,iBAAkB;cAC3B0B,uBAAuB,EAAE;gBAAEC,MAAM,EAAE5E;cAAS,CAAE;cAC9C0D,KAAK,EAAE;gBAAGmB,OAAO,EAAE,KAAK;gBAAEC,WAAW,EAAE;cAAO;YAAE;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,EAAC,GAAG;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACNlE,OAAA,CAACjB,GAAG;YAAC6E,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBACnC7D,OAAA,CAAChB,UAAU;cAAC4E,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAExD,SAAS,CAAC,yBAAyB;YAAC;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAE5FlE,OAAA;cACC4D,SAAS,EAAC,gBAAgB;cAC1BP,KAAK,EAAE;gBAAEmB,OAAO,EAAE;cAAM,CAAE;cAAAX,QAAA,gBAE1B7D,OAAA,CAACjB,GAAG;gBACH6E,SAAS,EAAC,mBAAmB;gBAC7BQ,EAAE,EAAE;kBAAEI,OAAO,EAAE,cAAc;kBAACE,YAAY,EAAC;gBAAe,CAAE;gBAAAb,QAAA,gBAE5D7D,OAAA,CAAChB,UAAU;kBACV4E,SAAS,EAAC,qBAAqB;kBAC/BQ,EAAE,EAAE;oBAAEO,QAAQ,EAAE,iBAAiB;oBAAEC,WAAW,EAAE,cAAc;oBAAEC,MAAM,EAAE;kBAAM,CAAE;kBAAAhB,QAAA,GAChF,IACE,EAACxD,SAAS,CAAC,aAAa,CAAC;gBAAA;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACblE,OAAA,CAACf,SAAS;kBACT6F,OAAO,EAAC,UAAU;kBAClBrC,KAAK,EAAEnB,iBAAiB,CAACO,SAAU;kBAEnCsC,IAAI,EAAC,OAAO;kBACZP,SAAS,EAAC,qBAAqB;kBAC/BmB,QAAQ,EAAGC,CAAC,IAAKrC,gBAAgB,CAAC,WAAW,EAAEqC,CAAC,CAACC,MAAM,CAACxC,KAAK,CAAE;kBAC/DyC,UAAU,EAAE;oBACXC,YAAY,EAAE,GAAG;oBACjBf,EAAE,EAAE;sBAEH,0CAA0C,EAAE;wBAAEgB,MAAM,EAAE;sBAAO,CAAC;sBAC9D,gDAAgD,EAAE;wBAAEA,MAAM,EAAE;sBAAO,CAAC;sBACpE,YAAY,EAAC;wBAACA,MAAM,EAAC;sBAAM;oBAE5B;kBACD;gBAAE;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNlE,OAAA,CAACjB,GAAG;gBACH6E,SAAS,EAAC,mBAAmB;gBAC7BQ,EAAE,EAAE;kBAAEI,OAAO,EAAE,cAAc;kBAACE,YAAY,EAAC;gBAAe,CAAE;gBAAAb,QAAA,gBAE5D7D,OAAA,CAAChB,UAAU;kBACV4E,SAAS,EAAC,qBAAqB;kBAC/BQ,EAAE,EAAE;oBAAEO,QAAQ,EAAE,iBAAiB;oBAAEC,WAAW,EAAE,cAAc;oBAAEC,MAAM,EAAE;kBAAM,CAAE;kBAAAhB,QAAA,GAChF,IACE,EAACxD,SAAS,CAAC,aAAa,CAAC;gBAAA;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACblE,OAAA,CAACf,SAAS;kBACT6F,OAAO,EAAC,UAAU;kBAClBrC,KAAK,EAAEnB,iBAAiB,CAACQ,SAAU;kBAEnCqC,IAAI,EAAC,OAAO;kBACZP,SAAS,EAAC,qBAAqB;kBAC/BmB,QAAQ,EAAGC,CAAC,IAAKrC,gBAAgB,CAAC,WAAW,EAAEqC,CAAC,CAACC,MAAM,CAACxC,KAAK,CAAE;kBAC/DyC,UAAU,EAAE;oBACXC,YAAY,EAAE,GAAG;oBACjBf,EAAE,EAAE;sBAEH,0CAA0C,EAAE;wBAAEgB,MAAM,EAAE;sBAAO,CAAC;sBAC9D,gDAAgD,EAAE;wBAAEA,MAAM,EAAE;sBAAO,CAAC;sBACpE,YAAY,EAAC;wBAACA,MAAM,EAAC;sBAAM;oBAE5B;kBACD;gBAAE;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAENlE,OAAA,CAACjB,GAAG;YACH4E,EAAE,EAAC,mBAAmB;YACtBC,SAAS,EAAC,mBAAmB;YAC7BQ,EAAE,EAAE;cAAEiB,aAAa,EAAE,QAAQ;cAAEC,MAAM,EAAE,iBAAiB;cAACd,OAAO,EAAC;YAAiB,CAAE;YAAAX,QAAA,gBAEpF7D,OAAA,CAAChB,UAAU;cAAC4E,SAAS,EAAC,qBAAqB;cAACQ,EAAE,EAAE;gBAAEI,OAAO,EAAE;cAAuB,CAAE;cAAAX,QAAA,EAAExD,SAAS,CAAC,MAAM;YAAC;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACrHlE,OAAA,CAACX,MAAM;cACNoD,KAAK,EAAEnB,iBAAiB,CAACS,IAAK;cAC9BgD,QAAQ,EAAGC,CAAC,IAAKrC,gBAAgB,CAAC,MAAM,EAAEqC,CAAC,CAACC,MAAM,CAACxC,KAAK,CAAE;cAC1D8C,YAAY;cACZ3B,SAAS,EAAC,qBAAqB;cAE/BQ,EAAE,EAAE;gBACHoB,KAAK,EAAE,iBAAiB;gBACxBC,YAAY,EAAE,MAAM;gBACpB,0CAA0C,EAAE;kBAAEL,MAAM,EAAE;gBAAO,CAAC;gBAC9D,gDAAgD,EAAE;kBAAEA,MAAM,EAAE;gBAAO,CAAC;gBACpE,YAAY,EAAE;kBAAEA,MAAM,EAAE;gBAAO;cAE9B,CAAE;cACJjB,IAAI,EAAC,OAAO;cACZuB,WAAW,EAAGC,QAAQ,iBACrB3F,OAAA,CAACjB,GAAG;gBAACqF,EAAE,EAAE;kBAAEwB,OAAO,EAAE,MAAM;kBAAEC,cAAc,EAAE,eAAe;kBAAEL,KAAK,EAAE;gBAAO,CAAE;gBAAA3B,QAAA,gBAC5E7D,OAAA;kBAAA6D,QAAA,EAAOxD,SAAS,CAACsF,QAAQ;gBAAC;kBAAA5B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAClClE,OAAA;kBAAA6D,QAAA,GACE8B,QAAQ,KAAK,MAAM,iBAAI3F,OAAA;oBAAMsE,uBAAuB,EAAE;sBAAEC,MAAM,EAAE9E;oBAAW;kBAAE;oBAAAsE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EAChFyB,QAAQ,KAAK,UAAU,iBAAI3F,OAAA;oBAAMsE,uBAAuB,EAAE;sBAAEC,MAAM,EAAE7E;oBAAa;kBAAE;oBAAAqE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACtFyB,QAAQ,KAAK,OAAO,iBAAI3F,OAAA;oBAAMsE,uBAAuB,EAAE;sBAAEC,MAAM,EAAE3E;oBAAM;kBAAE;oBAAAmE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACJ;cAAAL,QAAA,gBAEF7D,OAAA,CAACV,QAAQ;gBACRqE,EAAE,EAAC,mBAAmB;gBACtBlB,KAAK,EAAC,MAAM;gBAAAoB,QAAA,gBAEZ7D,OAAA;kBAAA6D,QAAA,EAAOxD,SAAS,CAAC,MAAM;gBAAC;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAChClE,OAAA;kBAAMqD,KAAK,EAAE;oBAAEyC,UAAU,EAAE,MAAM;oBAAEF,OAAO,EAAE,MAAM;oBAAEG,UAAU,EAAE;kBAAS,CAAE;kBAAAlC,QAAA,eAC1E7D,OAAA;oBAAMsE,uBAAuB,EAAE;sBAAEC,MAAM,EAAE9E;oBAAW;kBAAE;oBAAAsE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACXlE,OAAA,CAACV,QAAQ;gBACRqE,EAAE,EAAC,mBAAmB;gBACtBlB,KAAK,EAAC,UAAU;gBAAAoB,QAAA,gBAEhB7D,OAAA;kBAAA6D,QAAA,EAAOxD,SAAS,CAAC,UAAU;gBAAC;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACpClE,OAAA;kBAAMqD,KAAK,EAAE;oBAAEyC,UAAU,EAAE,MAAM;oBAAEF,OAAO,EAAE,MAAM;oBAAEG,UAAU,EAAE;kBAAS,CAAE;kBAAAlC,QAAA,eAC1E7D,OAAA;oBAAMsE,uBAAuB,EAAE;sBAAEC,MAAM,EAAE7E;oBAAa;kBAAE;oBAAAqE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACXlE,OAAA,CAACV,QAAQ;gBACRqE,EAAE,EAAC,mBAAmB;gBACtBlB,KAAK,EAAC,OAAO;gBAAAoB,QAAA,gBAEb7D,OAAA;kBAAA6D,QAAA,EAAOxD,SAAS,CAAC,OAAO;gBAAC;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjClE,OAAA;kBAAMqD,KAAK,EAAE;oBAAEyC,UAAU,EAAE,MAAM;oBAAEF,OAAO,EAAE,MAAM;oBAAEG,UAAU,EAAE;kBAAS,CAAE;kBAAAlC,QAAA,eAC1E7D,OAAA;oBAAMsE,uBAAuB,EAAE;sBAAEC,MAAM,EAAE3E;oBAAM;kBAAE;oBAAAmE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENlE,OAAA,CAACjB,GAAG;YACH4E,EAAE,EAAC,mBAAmB;YACtBC,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAE7B7D,OAAA,CAAChB,UAAU;cACV2E,EAAE,EAAC,mBAAmB;cACtBC,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAE9BxD,SAAS,CAAC,OAAO;YAAC;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eACblE,OAAA;cACCgG,IAAI,EAAC,OAAO;cACZvD,KAAK,EAAEnB,iBAAiB,CAACU,KAAM;cAC/B+C,QAAQ,EAAGC,CAAC,IAAKrC,gBAAgB,CAAC,OAAO,EAAEqC,CAAC,CAACC,MAAM,CAACxC,KAAK,CAAE;cAC3DmB,SAAS,EAAC;YAAmB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENlE,OAAA,CAACjB,GAAG;YACH4E,EAAE,EAAC,mBAAmB;YACtBC,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAE7B7D,OAAA,CAAChB,UAAU;cACV2E,EAAE,EAAC,mBAAmB;cACtBC,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAE9BxD,SAAS,CAAC,MAAM;YAAC;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eAEblE,OAAA,CAACZ,WAAW;cACXuE,EAAE,EAAC,mBAAmB;cACtBmB,OAAO,EAAC,UAAU;cAClBmB,SAAS;cACTrC,SAAS,EAAC,qBAAqB;cAAAC,QAAA,eAE/B7D,OAAA,CAACX,MAAM;gBACN6G,YAAY,EAAE,CAAE;gBAChBvC,EAAE,EAAC,mBAAmB;gBACtBlB,KAAK,EAAE,CAACnB,iBAAiB,CAACW,IAAI,GAAG,EAAE,IAAI,CAAC,GAAG,CAAE;gBAC7C8C,QAAQ,EAAGC,CAAC,IAAKxC,gBAAgB,CAAC2D,MAAM,CAACnB,CAAC,CAACC,MAAM,CAACxC,KAAK,CAAC,CAAE;gBAC1D2B,EAAE,EAAE;kBACHqB,YAAY,EAAE,MAAM;kBACpB,0CAA0C,EAAE;oBAAEL,MAAM,EAAE;kBAAO,CAAC;kBAC9D,gDAAgD,EAAE;oBAAEA,MAAM,EAAE;kBAAO,CAAC;kBACpE,YAAY,EAAE;oBAAEA,MAAM,EAAE;kBAAO;gBAC9B,CAAE;gBAAAvB,QAAA,EAEH,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACuC,GAAG,CAAEjC,IAAI,iBACrCnE,OAAA,CAACV,QAAQ;kBACRqE,EAAE,EAAC,mBAAmB;kBAEtBlB,KAAK,EAAE0B,IAAK;kBAAAN,QAAA,EAEXM;gBAAI,GAHAA,IAAI;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAIA,CACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNlE,OAAA,CAACjB,GAAG;YACH4E,EAAE,EAAC,mBAAmB;YACtBC,SAAS,EAAC,mBAAmB;YAC7BQ,EAAE,EAAE;cAAEiB,aAAa,EAAE,QAAQ;cAAEC,MAAM,EAAE,iBAAiB;cAAEe,GAAG,EAAE,KAAK;cAAC7B,OAAO,EAAC;YAAiB,CAAE;YAAAX,QAAA,gBAGhG7D,OAAA,CAACjB,GAAG;cACH4E,EAAE,EAAC,mBAAmB;cACtBC,SAAS,EAAC,oBAAoB;cAC9BQ,EAAE,EAAE;gBACHwB,OAAO,EAAE,MAAM;gBACfG,UAAU,EAAE,QAAQ;gBACpBF,cAAc,EAAE,eAAe;gBAC/BL,KAAK,EAAE;cACR,CAAE;cAAA3B,QAAA,gBAEF7D,OAAA,CAAChB,UAAU;gBACV4E,SAAS,EAAC,qBAAqB;gBAC/BQ,EAAE,EAAE;kBAAEkC,SAAS,EAAE,MAAM;kBAAEC,QAAQ,EAAE,OAAO;kBAAE/B,OAAO,EAAC;gBAAc,CAAE;gBAAAX,QAAA,EAEnExD,SAAS,CAAC,iBAAiB;cAAC;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eACblE,OAAA;gBAAO4D,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBACrC7D,OAAA;kBACIgG,IAAI,EAAC,UAAU;kBACfQ,OAAO,EAAEtF,gBAAiB;kBAC1B6D,QAAQ,EAAGC,CAAC,IAAK;oBACbrC,gBAAgB,CAAC,gBAAgB,EAAEqC,CAAC,CAACC,MAAM,CAACuB,OAAO,CAAC;oBACpDrF,mBAAmB,CAAC6D,CAAC,CAACC,MAAM,CAACuB,OAAO,CAAC;kBACzC,CAAE;kBACFC,IAAI,EAAC;gBAAgB;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACFlE,OAAA;kBAAM4D,SAAS,EAAC;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,EACLhD,gBAAgB,iBAChBlB,OAAA,CAACjB,GAAG;cACH4E,EAAE,EAAC,mBAAmB;cACtBC,SAAS,EAAC,oBAAoB;cAC9BQ,EAAE,EAAE;gBACHwB,OAAO,EAAE,MAAM;gBACfG,UAAU,EAAE,QAAQ;gBACpBF,cAAc,EAAE,eAAe;gBAC/BL,KAAK,EAAC;cACP,CAAE;cAAA3B,QAAA,gBAEF7D,OAAA,CAAChB,UAAU;gBACV2E,EAAE,EAAC,mBAAmB;gBACtBC,SAAS,EAAC,qBAAqB;gBAC/BQ,EAAE,EAAE;kBAAEkC,SAAS,EAAE,MAAM;kBAAEC,QAAQ,EAAE,OAAO;kBAACf,KAAK,EAAC,MAAM;kBAAChB,OAAO,EAAC;gBAAe,CAAE;gBAAAX,QAAA,EAEhFxD,SAAS,CAAC,iCAAiC;cAAC;gBAAA0D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,eACblE,OAAA;gBAAO4D,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBACtC7D,OAAA;kBACIgG,IAAI,EAAC,UAAU;kBACfQ,OAAO,EAAElF,iBAAiB,CAACa,4BAA6B;kBACxD4C,QAAQ,EAAGC,CAAC,IAAKrC,gBAAgB,CAAC,8BAA8B,EAAEqC,CAAC,CAACC,MAAM,CAACuB,OAAO,CAAE;kBACpFC,IAAI,EAAC;gBAA8B;kBAAA1C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACFlE,OAAA;kBAAM4D,SAAS,EAAC;gBAAQ;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEM,CACL;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,EACL5C,iBAAiB,CAACe,aAAa,KAAK,KAAK,iBACzCrC,OAAA,CAACjB,GAAG;YACH4E,EAAE,EAAC,mBAAmB;YACtBC,SAAS,EAAC,mBAAmB;YAC7BQ,EAAE,EAAE;cAAEiB,aAAa,EAAE,QAAQ;cAAEC,MAAM,EAAE,iBAAiB;cAAEd,OAAO,EAAC;YAAgB,CAAE;YAAAX,QAAA,gBAEpF7D,OAAA,CAAChB,UAAU;cAAC4E,SAAS,EAAC,qBAAqB;cAACQ,EAAE,EAAE;gBAAEI,OAAO,EAAE;cAAuB,CAAE;cAAAX,QAAA,EAAExD,SAAS,CAAC,WAAW;YAAC;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAG1HlE,OAAA,CAACZ,WAAW;cACX0F,OAAO,EAAC,UAAU;cAClBmB,SAAS;cACTrC,SAAS,EAAC,qBAAqB;cAC/BQ,EAAE,EAAE;gBACHqB,YAAY,EAAE,MAAM;gBACpBD,KAAK,EAAE,iBAAiB;gBACxB,0CAA0C,EAAE;kBAAEJ,MAAM,EAAE;gBAAO,CAAC;gBAC9D,gDAAgD,EAAE;kBAAEA,MAAM,EAAE;gBAAO,CAAC;gBACpE,YAAY,EAAE;kBAAEA,MAAM,EAAE;gBAAO,CAAC;gBAChC,qBAAqB,EAAC;kBAACA,MAAM,EAAG;gBAA8B;cAC7D,CAAE;cAAAvB,QAAA,eAEJ7D,OAAA,CAACX,MAAM;gBACNoD,KAAK,EAAEnB,iBAAiB,CAACc,QAAS,CAAC;gBAAA;gBACnC2C,QAAQ,EAAGC,CAAM,IAAKrC,gBAAgB,CAAC,UAAU,EAAEqC,CAAC,CAACC,MAAM,CAACxC,KAAK,CAAE;gBACnEgE,IAAI,EAAC,UAAU;gBACfrC,EAAE,EAAE;kBAAEoB,KAAK,EAAE,iBAAiB;kBAAEC,YAAY,EAAE;gBAAO,CAAE;gBAAA5B,QAAA,gBAEvD7D,OAAA,CAACV,QAAQ;kBAACmD,KAAK,EAAC,kBAAkB;kBAAAoB,QAAA,EAAExD,SAAS,CAAC,kBAAkB;gBAAC;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC7ElE,OAAA,CAACV,QAAQ;kBAACmD,KAAK,EAAC,kBAAkB;kBAAAoB,QAAA,EAAExD,SAAS,CAAC,kBAAkB;gBAAC;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CACL,eACDlE,OAAA,CAACjB,GAAG;YAAC6E,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBACjC7D,OAAA,CAAChB,UAAU;cAAC4E,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAExD,SAAS,CAAC,iBAAiB;YAAC;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAIvFlE,OAAA;cAAO4D,SAAS,EAAC,eAAe;cAAAC,QAAA,gBACpC7D,OAAA;gBACIgG,IAAI,EAAC,UAAU;gBACfQ,OAAO,EAAElF,iBAAiB,CAACe,aAAc;gBACzC0C,QAAQ,EAAGC,CAAC,IAAKrC,gBAAgB,CAAC,eAAe,EAAEqC,CAAC,CAACC,MAAM,CAACuB,OAAO,CAAE;gBACrEC,IAAI,EAAC;cAAe;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eACFlE,OAAA;gBAAM4D,SAAS,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACNlE,OAAA;QAAK4D,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eAClC7D,OAAA,CAACb,MAAM;UACN2F,OAAO,EAAC,WAAW;UACnBhB,OAAO,EAAEJ,kBAAmB;UAC5BE,SAAS,EAAC,WAAW;UAAAC,QAAA,EAEpBxD,SAAS,CAAC,OAAO;QAAC;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAET,CAAC;AAAC/D,EAAA,CArcIF,eAAe;EAAA,QACKH,cAAc,EAiBlCN,cAAc;AAAA;AAAAkH,EAAA,GAlBdzG,eAAe;AAucrB,eAAeA,eAAe;AAAC,IAAAyG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}