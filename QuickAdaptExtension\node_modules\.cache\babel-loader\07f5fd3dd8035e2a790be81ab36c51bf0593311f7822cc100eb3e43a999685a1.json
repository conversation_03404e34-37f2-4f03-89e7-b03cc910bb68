{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\auth\\\\AuthProvider.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useEffect, useState } from 'react';\nimport userManager from './UseAuth';\nimport jwt_decode from \"jwt-decode\";\nimport { GetUserDetailsById } from '../../services/UserService';\nimport { getRolesByUser } from '../../services/UserRoleService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nlet userLocalData = {};\nconst AuthContext = /*#__PURE__*/createContext(undefined);\nlet initialsData;\nexport const AuthProvider = ({\n  children\n}) => {\n  _s();\n  const [user, setUser] = useState(null);\n  const [userDetails, setUserDetails] = useState(null);\n  const [loggedOut, setLoggedOut] = useState(false);\n  const [userRoles, setUserRoles] = useState({});\n  useEffect(() => {\n    const initializeUser = async () => {\n      if (loggedOut) return; // Skip reinitialization if user has logged out\n\n      try {\n        const user = await userManager.getUser();\n        if (!user) {\n          // Try silent login if not authenticated\n          try {\n            await userManager.signinSilent();\n          } catch (e) {\n            // Silent login failed, will require interactive login\n          }\n        }\n        if (user) {\n          setUser(user);\n          if (user.access_token) {\n            var _userResponse$data, _userResponse$data$Us, _userResponse$data2;\n            const decodedToken = jwt_decode(user.access_token);\n            const userResponse = await GetUserDetailsById(decodedToken.UserId);\n            setUserDetails((_userResponse$data = userResponse === null || userResponse === void 0 ? void 0 : userResponse.data) !== null && _userResponse$data !== void 0 ? _userResponse$data : null);\n            GetUserRoles();\n            const firstNameInitials = userResponse !== null && userResponse !== void 0 && userResponse.data && userResponse !== null && userResponse !== void 0 && userResponse.data.FirstName ? userResponse === null || userResponse === void 0 ? void 0 : userResponse.data.FirstName.substring(0, 1).toUpperCase() : '';\n            const lastNameinitials = userResponse !== null && userResponse !== void 0 && userResponse.data && userResponse !== null && userResponse !== void 0 && userResponse.data.LastName ? userResponse === null || userResponse === void 0 ? void 0 : userResponse.data.LastName.substring(0, 1).toUpperCase() : '';\n            const finalData = firstNameInitials + lastNameinitials;\n            initialsData = finalData;\n            localStorage.setItem(\"userType\", (_userResponse$data$Us = userResponse === null || userResponse === void 0 ? void 0 : (_userResponse$data2 = userResponse.data) === null || _userResponse$data2 === void 0 ? void 0 : _userResponse$data2.UserType) !== null && _userResponse$data$Us !== void 0 ? _userResponse$data$Us : \"\");\n            userLocalData[\"user\"] = JSON.stringify(userResponse === null || userResponse === void 0 ? void 0 : userResponse.data);\n            localStorage.setItem(\"userInfo\", JSON.stringify(userLocalData));\n          }\n        }\n      } catch (error) {\n        console.error('Failed to fetch user details:', error);\n        userManager.signoutRedirect();\n      }\n    };\n    initializeUser();\n    userManager.events.addUserLoaded(async loadedUser => {\n      if (loggedOut) return;\n      setUser(loadedUser);\n      if (loadedUser.access_token) {\n        var _userResponse$data3;\n        const decodedToken = jwt_decode(loadedUser.access_token);\n        const userResponse = await GetUserDetailsById(decodedToken.UserId);\n        setUserDetails((_userResponse$data3 = userResponse === null || userResponse === void 0 ? void 0 : userResponse.data) !== null && _userResponse$data3 !== void 0 ? _userResponse$data3 : null);\n        GetUserRoles();\n        const firstNameInitials = userResponse !== null && userResponse !== void 0 && userResponse.data && userResponse !== null && userResponse !== void 0 && userResponse.data.FirstName ? userResponse === null || userResponse === void 0 ? void 0 : userResponse.data.FirstName.substring(0, 1).toUpperCase() : '';\n        const lastNameinitials = userResponse !== null && userResponse !== void 0 && userResponse.data && userResponse !== null && userResponse !== void 0 && userResponse.data.LastName ? userResponse === null || userResponse === void 0 ? void 0 : userResponse.data.LastName.substring(0, 1).toUpperCase() : '';\n        const finalData = firstNameInitials + lastNameinitials;\n        initialsData = finalData;\n        userLocalData[\"oidc-info\"] = JSON.stringify(loadedUser);\n        userLocalData[\"user\"] = JSON.stringify(userResponse === null || userResponse === void 0 ? void 0 : userResponse.data);\n        localStorage.setItem(\"userInfo\", JSON.stringify(userLocalData));\n      }\n    });\n    userManager.events.addUserUnloaded(() => {\n      setUser(null);\n      setUserDetails(null);\n      localStorage.clear();\n      // Remove theme-custom class on user unload\n      document.body.classList.remove(\"theme-custom\");\n      document.cookie.split(\";\").forEach(cookie => {\n        const [name] = cookie.split(\"=\");\n        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;\n      });\n      setLoggedOut(true); // Indicate the user has logged out\n      const redirectPath = process.env.REACT_APP_IDS_API;\n      // if (redirectPath) {\n      //   window.location.href = redirectPath;\n      // }\n    });\n  }, []);\n  const GetUserRoles = async () => {\n    try {\n      const rolesData = await getRolesByUser();\n      // console.log(rolesData);\n      const dist = rolesData.reduce((acc, curr) => {\n        if (!acc[curr.AccountId]) {\n          acc[curr.AccountId] = [];\n        }\n        if (!acc[curr.AccountId].includes(curr.RoleName)) {\n          acc[curr.AccountId].push(curr.RoleName);\n        }\n        return acc;\n      }, {});\n      setUserRoles(dist);\n    } catch (e) {}\n  };\n  const signOut = () => {\n    const logeduserType = localStorage.getItem('userType');\n    if ((logeduserType === null || logeduserType === void 0 ? void 0 : logeduserType.toLowerCase()) !== \"superadmin\") {\n      setUser(null);\n      setUserDetails(null);\n      localStorage.clear();\n      // Remove theme-custom class on sign out\n      document.body.classList.remove(\"theme-custom\");\n      document.cookie.split(\";\").forEach(cookie => {\n        const [name] = cookie.split(\"=\");\n        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;\n      });\n      setLoggedOut(true);\n      localStorage.setItem('logout-event', Date.now().toString());\n      sessionStorage.clear();\n      userManager.signoutRedirect();\n    } else {\n      sessionStorage.clear();\n    }\n  };\n  const signIn = () => {\n    setLoggedOut(false);\n    userManager.signinRedirect();\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: {\n      user,\n      userDetails,\n      signOut,\n      loggedOut,\n      userRoles,\n      setUserRoles\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 158,\n    columnNumber: 5\n  }, this);\n};\n_s(AuthProvider, \"EwyVk7iqB9p9N+gzhDsatTMww04=\");\n_c = AuthProvider;\nexport const useAuth = () => {\n  _s2();\n  var _context;\n  let context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  if ((_context = context) !== null && _context !== void 0 && _context.user) {\n    const userInfo = JSON.parse(localStorage.getItem(\"userInfo\") || '{}');\n    userLocalData[\"oidc-info\"] = JSON.stringify(context.user);\n    if (userInfo['user']) {\n      userLocalData[\"user\"] = JSON.stringify(userInfo['user']);\n    }\n  } else {\n    const userInfo = JSON.parse(localStorage.getItem(\"userInfo\") || '{}');\n    if (userInfo['oidc-info'] && userInfo['user']) {\n      context = {\n        ...context,\n        user: JSON.parse(userInfo['oidc-info'])\n      };\n      context.userDetails = JSON.parse(userInfo['user']);\n      context.loggedOut = false;\n    }\n  }\n  return context;\n};\n_s2(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport { initialsData };\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useEffect", "useState", "userManager", "jwt_decode", "GetUserDetailsById", "getRolesByUser", "jsxDEV", "_jsxDEV", "userLocalData", "AuthContext", "undefined", "initialsData", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "user", "setUser", "userDetails", "setUserDetails", "loggedOut", "setLoggedOut", "userRoles", "setUserRoles", "initializeUser", "getUser", "signinSilent", "e", "access_token", "_userResponse$data", "_userResponse$data$Us", "_userResponse$data2", "decodedToken", "userResponse", "UserId", "data", "GetUserRoles", "firstNameInitials", "FirstName", "substring", "toUpperCase", "lastNameinitials", "LastName", "finalData", "localStorage", "setItem", "UserType", "JSON", "stringify", "error", "console", "signoutRedirect", "events", "addUserLoaded", "loadedUser", "_userResponse$data3", "addUserUnloaded", "clear", "document", "body", "classList", "remove", "cookie", "split", "for<PERSON>ach", "name", "redirectPath", "process", "env", "REACT_APP_IDS_API", "rolesData", "dist", "reduce", "acc", "curr", "AccountId", "includes", "RoleName", "push", "signOut", "logeduserType", "getItem", "toLowerCase", "Date", "now", "toString", "sessionStorage", "signIn", "signinRedirect", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "_context", "context", "Error", "userInfo", "parse", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/auth/AuthProvider.tsx"], "sourcesContent": ["import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';\r\nimport { User, UserManager } from 'oidc-client-ts';\r\nimport userManager from './UseAuth';\r\nimport { LoginUserInfo } from '../../models/LoginUserInfo';\r\nimport jwt_decode from \"jwt-decode\";\r\nimport { User as LoginUser } from '../../models/User';\r\nimport { useNavigate, useLocation } from 'react-router-dom';\r\nimport { GetUserDetailsById } from '../../services/UserService';\r\nimport { getRolesByUser } from '../../services/UserRoleService';\r\n\r\ninterface AuthContextType {\r\n  user: User | null;\r\n  userDetails: LoginUser | null;\r\n  signOut: () => void;\r\n  loggedOut: boolean;\r\n  userRoles: any;\r\n  setUserRoles: any;\r\n}\r\nlet userLocalData: { [key: string]: any } = {}\r\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\r\n\r\ninterface AuthProviderProps {\r\n  children: ReactNode;\r\n}\r\nlet initialsData: string;\r\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\r\n  const [user, setUser] = useState<User | null>(null);\r\n  const [userDetails, setUserDetails] = useState<LoginUser | null>(null);\r\n  const [loggedOut, setLoggedOut] = useState<boolean>(false);\r\n  const [userRoles, setUserRoles] = useState({});\r\n\r\n  useEffect(() => {\r\n    const initializeUser = async () => {\r\n      if (loggedOut) return;  // Skip reinitialization if user has logged out\r\n\r\n      try {\r\n        const user = await userManager.getUser();\r\n        if (!user) {\r\n          // Try silent login if not authenticated\r\n          try {\r\n            await userManager.signinSilent();\r\n          } catch (e) {\r\n            // Silent login failed, will require interactive login\r\n          }\r\n        }\r\n        if (user) {\r\n          setUser(user);\r\n  \r\n          if (user.access_token) {     \r\n            const decodedToken = jwt_decode<LoginUserInfo>(user.access_token);\r\n            const userResponse = await GetUserDetailsById(decodedToken.UserId);\r\n            setUserDetails(userResponse?.data ?? null);\r\n            GetUserRoles();\r\n            const firstNameInitials =  userResponse?.data && userResponse?.data.FirstName ? userResponse?.data.FirstName.substring(0, 1).toUpperCase() : '';\r\n            const lastNameinitials = userResponse?.data && userResponse?.data.LastName ? userResponse?.data.LastName.substring(0, 1).toUpperCase() : '';\r\n            const finalData = firstNameInitials + lastNameinitials;\r\n            initialsData = finalData;\r\n            localStorage.setItem(\"userType\", userResponse?.data?.UserType ?? \"\");\r\n            userLocalData[\"user\"] = JSON.stringify(userResponse?.data)\r\n            localStorage.setItem(\"userInfo\", JSON.stringify(userLocalData));\r\n\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Failed to fetch user details:', error);\r\n        userManager.signoutRedirect();\r\n      }\r\n    };  \r\n    initializeUser();  \r\n    userManager.events.addUserLoaded(async (loadedUser) => {\r\n      if (loggedOut) return; \r\n      setUser(loadedUser);\r\n      if (loadedUser.access_token) {\r\n        const decodedToken = jwt_decode<LoginUserInfo>(loadedUser.access_token);\r\n        const userResponse = await GetUserDetailsById(decodedToken.UserId);\r\n        setUserDetails(userResponse?.data ?? null);\r\n        GetUserRoles();\r\n        const firstNameInitials =   userResponse?.data && userResponse?.data.FirstName ? userResponse?.data.FirstName.substring(0, 1).toUpperCase() : '';\r\n          const lastNameinitials =  userResponse?.data && userResponse?.data.LastName ? userResponse?.data.LastName.substring(0, 1).toUpperCase() : '';\r\n          const finalData = firstNameInitials + lastNameinitials;\r\n          initialsData = finalData;\r\n        userLocalData[\"oidc-info\"] = JSON.stringify(loadedUser);    \r\n        userLocalData[\"user\"]= JSON.stringify(userResponse?.data )       \r\n        localStorage.setItem(\"userInfo\", JSON.stringify(userLocalData));\r\n\r\n      }\r\n    });\r\n  \r\n    userManager.events.addUserUnloaded(() => {\r\n      setUser(null);\r\n      setUserDetails(null);\r\n      localStorage.clear();\r\n      // Remove theme-custom class on user unload\r\n      document.body.classList.remove(\"theme-custom\");\r\n      document.cookie.split(\";\").forEach(cookie => {\r\n        const [name] = cookie.split(\"=\");\r\n        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;\r\n      });\r\n      setLoggedOut(true);  // Indicate the user has logged out\r\n      const redirectPath = process.env.REACT_APP_IDS_API;\r\n      // if (redirectPath) {\r\n      //   window.location.href = redirectPath;\r\n      // }\r\n    });\r\n  }, []);\r\n\r\n\r\n  const GetUserRoles = async () => {\r\n    try {\r\n      const rolesData = await getRolesByUser();\r\n      // console.log(rolesData);\r\n      const dist = rolesData.reduce((acc: { [x: string]: any[]; }, curr: { AccountId: string | number; RoleName: any; }) => {\r\n        if (!acc[curr.AccountId]) {\r\n          acc[curr.AccountId] = [];\r\n        }\r\n        if (!acc[curr.AccountId].includes(curr.RoleName)) {\r\n          acc[curr.AccountId].push(curr.RoleName);\r\n        }\r\n        return acc;\r\n      }, {});\r\n\r\n      setUserRoles(dist);\r\n              \r\n      \r\n    } catch (e) {\r\n      \r\n    }\r\n  }\r\n\r\n  const signOut = () => {\r\n    const logeduserType = localStorage.getItem('userType');\r\n    if (logeduserType?.toLowerCase()!== \"superadmin\") {\r\n      setUser(null);\r\n      setUserDetails(null);\r\n      localStorage.clear();\r\n      // Remove theme-custom class on sign out\r\n      document.body.classList.remove(\"theme-custom\");\r\n      document.cookie.split(\";\").forEach(cookie => {\r\n        const [name] = cookie.split(\"=\");\r\n        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;\r\n      });\r\n      setLoggedOut(true);\r\n      localStorage.setItem('logout-event', Date.now().toString());\r\n      sessionStorage.clear()\r\n      userManager.signoutRedirect();\r\n    }\r\n    else {\r\n      sessionStorage.clear()\r\n    }\r\n  };\r\n  \r\n  const signIn = () => {\r\n    setLoggedOut(false);  \r\n    userManager.signinRedirect();\r\n  };\r\n\r\n  return (\r\n    <AuthContext.Provider value={{ user, userDetails, signOut,loggedOut,userRoles,setUserRoles }}>\r\n      {children}\r\n    </AuthContext.Provider>\r\n  );\r\n};\r\n\r\nexport const useAuth = (): AuthContextType => {\r\n  let context = useContext(AuthContext);\r\n\r\n  if (context === undefined) {\r\n    throw new Error('useAuth must be used within an AuthProvider');\r\n  }\r\n  if (context?.user) {\r\n    const userInfo = JSON.parse(localStorage.getItem(\"userInfo\") || '{}');\r\n    userLocalData[\"oidc-info\"] = JSON.stringify(context.user);    \r\n\r\n    if (userInfo['user']) {\r\n      userLocalData[\"user\"] =  JSON.stringify(userInfo['user'])\r\n    }   \r\n  } \r\n  else {\r\n    const userInfo = JSON.parse(localStorage.getItem(\"userInfo\") || '{}');\r\n    if (userInfo['oidc-info'] && userInfo['user']) {\r\n      context = { ...context, user: JSON.parse(userInfo['oidc-info']) };\r\n      context.userDetails =  JSON.parse(userInfo['user'])\r\n      context.loggedOut = false;\r\n    }\r\n  }\r\n\r\n  return context;\r\n};\r\n\r\nexport {initialsData}"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,SAAS,EAAEC,QAAQ,QAAmB,OAAO;AAExF,OAAOC,WAAW,MAAM,WAAW;AAEnC,OAAOC,UAAU,MAAM,YAAY;AAGnC,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,cAAc,QAAQ,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAUhE,IAAIC,aAAqC,GAAG,CAAC,CAAC;AAC9C,MAAMC,WAAW,gBAAGX,aAAa,CAA8BY,SAAS,CAAC;AAKzE,IAAIC,YAAoB;AACxB,OAAO,MAAMC,YAAyC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACzE,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGf,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAM,CAACgB,WAAW,EAAEC,cAAc,CAAC,GAAGjB,QAAQ,CAAmB,IAAI,CAAC;EACtE,MAAM,CAACkB,SAAS,EAAEC,YAAY,CAAC,GAAGnB,QAAQ,CAAU,KAAK,CAAC;EAC1D,MAAM,CAACoB,SAAS,EAAEC,YAAY,CAAC,GAAGrB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAE9CD,SAAS,CAAC,MAAM;IACd,MAAMuB,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAIJ,SAAS,EAAE,OAAO,CAAE;;MAExB,IAAI;QACF,MAAMJ,IAAI,GAAG,MAAMb,WAAW,CAACsB,OAAO,CAAC,CAAC;QACxC,IAAI,CAACT,IAAI,EAAE;UACT;UACA,IAAI;YACF,MAAMb,WAAW,CAACuB,YAAY,CAAC,CAAC;UAClC,CAAC,CAAC,OAAOC,CAAC,EAAE;YACV;UAAA;QAEJ;QACA,IAAIX,IAAI,EAAE;UACRC,OAAO,CAACD,IAAI,CAAC;UAEb,IAAIA,IAAI,CAACY,YAAY,EAAE;YAAA,IAAAC,kBAAA,EAAAC,qBAAA,EAAAC,mBAAA;YACrB,MAAMC,YAAY,GAAG5B,UAAU,CAAgBY,IAAI,CAACY,YAAY,CAAC;YACjE,MAAMK,YAAY,GAAG,MAAM5B,kBAAkB,CAAC2B,YAAY,CAACE,MAAM,CAAC;YAClEf,cAAc,EAAAU,kBAAA,GAACI,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEE,IAAI,cAAAN,kBAAA,cAAAA,kBAAA,GAAI,IAAI,CAAC;YAC1CO,YAAY,CAAC,CAAC;YACd,MAAMC,iBAAiB,GAAIJ,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEE,IAAI,IAAIF,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEE,IAAI,CAACG,SAAS,GAAGL,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEE,IAAI,CAACG,SAAS,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG,EAAE;YAC/I,MAAMC,gBAAgB,GAAGR,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEE,IAAI,IAAIF,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEE,IAAI,CAACO,QAAQ,GAAGT,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEE,IAAI,CAACO,QAAQ,CAACH,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG,EAAE;YAC3I,MAAMG,SAAS,GAAGN,iBAAiB,GAAGI,gBAAgB;YACtD7B,YAAY,GAAG+B,SAAS;YACxBC,YAAY,CAACC,OAAO,CAAC,UAAU,GAAAf,qBAAA,GAAEG,YAAY,aAAZA,YAAY,wBAAAF,mBAAA,GAAZE,YAAY,CAAEE,IAAI,cAAAJ,mBAAA,uBAAlBA,mBAAA,CAAoBe,QAAQ,cAAAhB,qBAAA,cAAAA,qBAAA,GAAI,EAAE,CAAC;YACpErB,aAAa,CAAC,MAAM,CAAC,GAAGsC,IAAI,CAACC,SAAS,CAACf,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEE,IAAI,CAAC;YAC1DS,YAAY,CAACC,OAAO,CAAC,UAAU,EAAEE,IAAI,CAACC,SAAS,CAACvC,aAAa,CAAC,CAAC;UAEjE;QACF;MACF,CAAC,CAAC,OAAOwC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD9C,WAAW,CAACgD,eAAe,CAAC,CAAC;MAC/B;IACF,CAAC;IACD3B,cAAc,CAAC,CAAC;IAChBrB,WAAW,CAACiD,MAAM,CAACC,aAAa,CAAC,MAAOC,UAAU,IAAK;MACrD,IAAIlC,SAAS,EAAE;MACfH,OAAO,CAACqC,UAAU,CAAC;MACnB,IAAIA,UAAU,CAAC1B,YAAY,EAAE;QAAA,IAAA2B,mBAAA;QAC3B,MAAMvB,YAAY,GAAG5B,UAAU,CAAgBkD,UAAU,CAAC1B,YAAY,CAAC;QACvE,MAAMK,YAAY,GAAG,MAAM5B,kBAAkB,CAAC2B,YAAY,CAACE,MAAM,CAAC;QAClEf,cAAc,EAAAoC,mBAAA,GAACtB,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEE,IAAI,cAAAoB,mBAAA,cAAAA,mBAAA,GAAI,IAAI,CAAC;QAC1CnB,YAAY,CAAC,CAAC;QACd,MAAMC,iBAAiB,GAAKJ,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEE,IAAI,IAAIF,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEE,IAAI,CAACG,SAAS,GAAGL,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEE,IAAI,CAACG,SAAS,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG,EAAE;QAC9I,MAAMC,gBAAgB,GAAIR,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEE,IAAI,IAAIF,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAEE,IAAI,CAACO,QAAQ,GAAGT,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEE,IAAI,CAACO,QAAQ,CAACH,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG,EAAE;QAC5I,MAAMG,SAAS,GAAGN,iBAAiB,GAAGI,gBAAgB;QACtD7B,YAAY,GAAG+B,SAAS;QAC1BlC,aAAa,CAAC,WAAW,CAAC,GAAGsC,IAAI,CAACC,SAAS,CAACM,UAAU,CAAC;QACvD7C,aAAa,CAAC,MAAM,CAAC,GAAEsC,IAAI,CAACC,SAAS,CAACf,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEE,IAAK,CAAC;QAC1DS,YAAY,CAACC,OAAO,CAAC,UAAU,EAAEE,IAAI,CAACC,SAAS,CAACvC,aAAa,CAAC,CAAC;MAEjE;IACF,CAAC,CAAC;IAEFN,WAAW,CAACiD,MAAM,CAACI,eAAe,CAAC,MAAM;MACvCvC,OAAO,CAAC,IAAI,CAAC;MACbE,cAAc,CAAC,IAAI,CAAC;MACpByB,YAAY,CAACa,KAAK,CAAC,CAAC;MACpB;MACAC,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,cAAc,CAAC;MAC9CH,QAAQ,CAACI,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAACF,MAAM,IAAI;QAC3C,MAAM,CAACG,IAAI,CAAC,GAAGH,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC;QAChCL,QAAQ,CAACI,MAAM,GAAG,GAAGG,IAAI,mDAAmD;MAC9E,CAAC,CAAC;MACF5C,YAAY,CAAC,IAAI,CAAC,CAAC,CAAE;MACrB,MAAM6C,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB;MAClD;MACA;MACA;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAGN,MAAMjC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMkC,SAAS,GAAG,MAAMhE,cAAc,CAAC,CAAC;MACxC;MACA,MAAMiE,IAAI,GAAGD,SAAS,CAACE,MAAM,CAAC,CAACC,GAA4B,EAAEC,IAAoD,KAAK;QACpH,IAAI,CAACD,GAAG,CAACC,IAAI,CAACC,SAAS,CAAC,EAAE;UACxBF,GAAG,CAACC,IAAI,CAACC,SAAS,CAAC,GAAG,EAAE;QAC1B;QACA,IAAI,CAACF,GAAG,CAACC,IAAI,CAACC,SAAS,CAAC,CAACC,QAAQ,CAACF,IAAI,CAACG,QAAQ,CAAC,EAAE;UAChDJ,GAAG,CAACC,IAAI,CAACC,SAAS,CAAC,CAACG,IAAI,CAACJ,IAAI,CAACG,QAAQ,CAAC;QACzC;QACA,OAAOJ,GAAG;MACZ,CAAC,EAAE,CAAC,CAAC,CAAC;MAENlD,YAAY,CAACgD,IAAI,CAAC;IAGpB,CAAC,CAAC,OAAO5C,CAAC,EAAE,CAEZ;EACF,CAAC;EAED,MAAMoD,OAAO,GAAGA,CAAA,KAAM;IACpB,MAAMC,aAAa,GAAGpC,YAAY,CAACqC,OAAO,CAAC,UAAU,CAAC;IACtD,IAAI,CAAAD,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEE,WAAW,CAAC,CAAC,MAAI,YAAY,EAAE;MAChDjE,OAAO,CAAC,IAAI,CAAC;MACbE,cAAc,CAAC,IAAI,CAAC;MACpByB,YAAY,CAACa,KAAK,CAAC,CAAC;MACpB;MACAC,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,cAAc,CAAC;MAC9CH,QAAQ,CAACI,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAACF,MAAM,IAAI;QAC3C,MAAM,CAACG,IAAI,CAAC,GAAGH,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC;QAChCL,QAAQ,CAACI,MAAM,GAAG,GAAGG,IAAI,mDAAmD;MAC9E,CAAC,CAAC;MACF5C,YAAY,CAAC,IAAI,CAAC;MAClBuB,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEsC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC;MAC3DC,cAAc,CAAC7B,KAAK,CAAC,CAAC;MACtBtD,WAAW,CAACgD,eAAe,CAAC,CAAC;IAC/B,CAAC,MACI;MACHmC,cAAc,CAAC7B,KAAK,CAAC,CAAC;IACxB;EACF,CAAC;EAED,MAAM8B,MAAM,GAAGA,CAAA,KAAM;IACnBlE,YAAY,CAAC,KAAK,CAAC;IACnBlB,WAAW,CAACqF,cAAc,CAAC,CAAC;EAC9B,CAAC;EAED,oBACEhF,OAAA,CAACE,WAAW,CAAC+E,QAAQ;IAACC,KAAK,EAAE;MAAE1E,IAAI;MAAEE,WAAW;MAAE6D,OAAO;MAAC3D,SAAS;MAACE,SAAS;MAACC;IAAa,CAAE;IAAAT,QAAA,EAC1FA;EAAQ;IAAA6E,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAC/E,EAAA,CAxIWF,YAAyC;AAAAkF,EAAA,GAAzClF,YAAyC;AA0ItD,OAAO,MAAMmF,OAAO,GAAGA,CAAA,KAAuB;EAAAC,GAAA;EAAA,IAAAC,QAAA;EAC5C,IAAIC,OAAO,GAAGnG,UAAU,CAACU,WAAW,CAAC;EAErC,IAAIyF,OAAO,KAAKxF,SAAS,EAAE;IACzB,MAAM,IAAIyF,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,KAAAF,QAAA,GAAIC,OAAO,cAAAD,QAAA,eAAPA,QAAA,CAASlF,IAAI,EAAE;IACjB,MAAMqF,QAAQ,GAAGtD,IAAI,CAACuD,KAAK,CAAC1D,YAAY,CAACqC,OAAO,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC;IACrExE,aAAa,CAAC,WAAW,CAAC,GAAGsC,IAAI,CAACC,SAAS,CAACmD,OAAO,CAACnF,IAAI,CAAC;IAEzD,IAAIqF,QAAQ,CAAC,MAAM,CAAC,EAAE;MACpB5F,aAAa,CAAC,MAAM,CAAC,GAAIsC,IAAI,CAACC,SAAS,CAACqD,QAAQ,CAAC,MAAM,CAAC,CAAC;IAC3D;EACF,CAAC,MACI;IACH,MAAMA,QAAQ,GAAGtD,IAAI,CAACuD,KAAK,CAAC1D,YAAY,CAACqC,OAAO,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC;IACrE,IAAIoB,QAAQ,CAAC,WAAW,CAAC,IAAIA,QAAQ,CAAC,MAAM,CAAC,EAAE;MAC7CF,OAAO,GAAG;QAAE,GAAGA,OAAO;QAAEnF,IAAI,EAAE+B,IAAI,CAACuD,KAAK,CAACD,QAAQ,CAAC,WAAW,CAAC;MAAE,CAAC;MACjEF,OAAO,CAACjF,WAAW,GAAI6B,IAAI,CAACuD,KAAK,CAACD,QAAQ,CAAC,MAAM,CAAC,CAAC;MACnDF,OAAO,CAAC/E,SAAS,GAAG,KAAK;IAC3B;EACF;EAEA,OAAO+E,OAAO;AAChB,CAAC;AAACF,GAAA,CAxBWD,OAAO;AA0BpB,SAAQpF,YAAY;AAAC,IAAAmF,EAAA;AAAAQ,YAAA,CAAAR,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}