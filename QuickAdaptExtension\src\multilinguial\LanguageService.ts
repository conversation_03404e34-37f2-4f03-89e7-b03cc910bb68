
import { adminApiService } from "../services/APIService";


export type LanguageType = {
  LanguageId: string;
  Language: string;
  LanguageCode: string;
  FlagIcon: string;
};


export const getLanguages = async (): Promise<LanguageType[]> => {
  try {
    const response = await adminApiService.get("/Translation/GetLanguages");
    return Array.isArray(response.data) ? response.data : [];
  } catch (error) {
    console.error("Error fetching languages:", error);
    return [];
  }
};


export const getLabels = async (language: string, isMobile: boolean = false) => {
  try {
    const response = await adminApiService.get("/Translation/GetAllLabels", {
      params: { language, isMobile },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching translation labels:", error);
    throw error;
  }
};

export const updateLanguage = async (language: string) => {
  try {
    const response = await adminApiService.get("/Translation/UpdateUserLanguage", {
      params: { language },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching translation labels:", error);
    throw error;
  }
};
