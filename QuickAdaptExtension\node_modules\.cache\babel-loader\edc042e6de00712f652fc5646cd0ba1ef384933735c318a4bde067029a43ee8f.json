{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideDesign\\\\Animation.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { IconButton, MenuItem, Select, FormControl } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\nimport \"./Canvas.module.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AnimationSettings = ({\n  selectedTemplate\n}) => {\n  _s();\n  const [entryAnimation, setEntryAnimation] = useState(\"None\");\n  const [delightAnimation, setDelightAnimation] = useState(\"None\");\n  const [isOpen, setIsOpen] = useState(true);\n  const handleEntryAnimationChange = event => {\n    setEntryAnimation(event.target.value);\n  };\n  const handleDelightAnimationChange = event => {\n    setDelightAnimation(event.target.value);\n  };\n  const handleClose = () => {\n    setIsOpen(false); // Close the popup when close button is clicked\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"qadpt-designpopup\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"qadpt-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-design-header\",\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          \"aria-label\": \"close\",\n          onClick: handleClose,\n          children: /*#__PURE__*/_jsxDEV(ArrowBackIosNewOutlinedIcon, {\n            className: \"qadpt-design-back\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-title\",\n          children: \"Animation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          \"aria-label\": \"close\",\n          onClick: handleClose,\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {\n            className: \"qadpt-design-close\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: \"flex\",\n          flexDirection: \"column\",\n          gap: \"15px\",\n          padding: \"10px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(FormControl, {\n          fullWidth: true,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: \"5px\",\n              fontSize: \"14px\"\n            },\n            children: \"Entry Animation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 9\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            labelId: \"entry-animation-label\",\n            value: entryAnimation,\n            onChange: handleEntryAnimationChange,\n            MenuProps: {\n              PopoverClasses: {\n                root: \"custom-popover-root\"\n              }\n            },\n            style: {\n              border: \"none\",\n              borderRadius: \"8px\",\n              fontSize: \"14px\",\n              boxShadow: \"none\",\n              backgroundColor: \"var(--back-light-color)\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"None\",\n              children: \"None\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 5\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"Top to Bottom\",\n              children: \"Top to Bottom\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 5\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"Fade\",\n              children: \"Fade\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 5\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 9\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 5\n        }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n          fullWidth: true,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: \"5px\",\n              fontSize: \"14px\"\n            },\n            children: \"Delight Animation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 9\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            labelId: \"delight-animation-label\",\n            value: delightAnimation,\n            onChange: handleDelightAnimationChange,\n            MenuProps: {\n              PopoverClasses: {\n                root: \"custom-popover-root\"\n              }\n            },\n            style: {\n              borderRadius: \"8px\",\n              fontSize: \"14px\",\n              boxShadow: \"none\",\n              backgroundColor: \"var(--back-light-color)\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"None\",\n              children: \"None\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 5\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"Confetti\",\n              children: \"Confetti\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 5\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 9\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 5\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 1\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 4\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 3\n  }, this);\n};\n_s(AnimationSettings, \"4VPU4kyVdyRPXIxb4q9nJfizS0c=\");\n_c = AnimationSettings;\nexport default AnimationSettings;\nvar _c;\n$RefreshReg$(_c, \"AnimationSettings\");", "map": {"version": 3, "names": ["React", "useState", "IconButton", "MenuItem", "Select", "FormControl", "CloseIcon", "ArrowBackIosNewOutlinedIcon", "jsxDEV", "_jsxDEV", "AnimationSettings", "selectedTemplate", "_s", "entryAnimation", "setEntryAnimation", "delightAnimation", "setDelightAnimation", "isOpen", "setIsOpen", "handleEntryAnimationChange", "event", "target", "value", "handleDelightAnimationChange", "handleClose", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "style", "display", "flexDirection", "gap", "padding", "fullWidth", "marginBottom", "fontSize", "labelId", "onChange", "MenuProps", "PopoverClasses", "root", "border", "borderRadius", "boxShadow", "backgroundColor", "_c", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/guideDesign/Animation.tsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport {\r\n\tBox,\r\n\tTypography,\r\n\tIconButton,\r\n\tMenuItem,\r\n\tSelect,\r\n\tFormControl,\r\n\tInputLabel,\r\n\tSelectChangeEvent,\r\n} from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport \"./Canvas.module.css\";\r\n\r\nconst AnimationSettings = ({ selectedTemplate }: { selectedTemplate: any }) => {\r\n\tconst [entryAnimation, setEntryAnimation] = useState(\"None\");\r\n\tconst [delightAnimation, setDelightAnimation] = useState(\"None\");\r\n\tconst [isOpen, setIsOpen] = useState(true);\r\n\r\n\tconst handleEntryAnimationChange = (event: SelectChangeEvent<string>) => {\r\n\t\tsetEntryAnimation(event.target.value as string);\r\n\t};\r\n\r\n\tconst handleDelightAnimationChange = (event: SelectChangeEvent<string>) => {\r\n\t\tsetDelightAnimation(event.target.value as string);\r\n\t};\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetIsOpen(false); // Close the popup when close button is clicked\r\n\t};\r\n\r\n\tif (!isOpen) return null;\r\n\r\n\treturn (\r\n\t\t<div className=\"qadpt-designpopup\">\r\n\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<ArrowBackIosNewOutlinedIcon  className=\"qadpt-design-back\"/>\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t<div className=\"qadpt-title\">Animation</div>\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<CloseIcon className=\"qadpt-design-close\"/>\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t</div>\r\n{/* Animation Settings Section */}\r\n<div\r\n    style={{\r\n        display: \"flex\",\r\n        flexDirection: \"column\",\r\n        gap: \"15px\",\r\n        padding: \"10px\",\r\n    }}\r\n>\r\n    {/* Entry Animation Dropdown */}\r\n    <FormControl fullWidth>\r\n        <div style={{ marginBottom: \"5px\", fontSize: \"14px\" }}>\r\n            Entry Animation\r\n        </div>\r\n        <Select\r\n    labelId=\"entry-animation-label\"\r\n    value={entryAnimation}\r\n    onChange={handleEntryAnimationChange}\r\n    MenuProps={{\r\n\t\tPopoverClasses: {\r\n            root: \"custom-popover-root\"\r\n        }\r\n    }}\r\n    style={{\r\n        border: \"none\",\r\n        borderRadius: \"8px\",\r\n        fontSize: \"14px\",\r\n        boxShadow: \"none\",\r\n        backgroundColor: \"var(--back-light-color)\",\r\n    }}\r\n>\r\n    <MenuItem value=\"None\">None</MenuItem>\r\n    <MenuItem value=\"Top to Bottom\">Top to Bottom</MenuItem>\r\n    <MenuItem value=\"Fade\">Fade</MenuItem>\r\n</Select>\r\n\r\n    </FormControl>\r\n\r\n    {/* Delight Animation Dropdown */}\r\n    <FormControl fullWidth>\r\n        <div style={{ marginBottom: \"5px\", fontSize: \"14px\" }}>\r\n            Delight Animation\r\n        </div>\r\n        <Select\r\n    labelId=\"delight-animation-label\"\r\n    value={delightAnimation}\r\n    onChange={handleDelightAnimationChange}\r\n    MenuProps={{\r\n\t\tPopoverClasses: {\r\n            root: \"custom-popover-root\"\r\n        }\r\n    }}\r\n    style={{\r\n        borderRadius: \"8px\",\r\n        fontSize: \"14px\",\r\n        boxShadow: \"none\",\r\n        backgroundColor: \"var(--back-light-color)\",\r\n    }}\r\n>\r\n    <MenuItem value=\"None\">None</MenuItem>\r\n    <MenuItem value=\"Confetti\">Confetti</MenuItem>\r\n</Select>\r\n\r\n    </FormControl>\r\n</div>\r\n\r\n\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t);\r\n};\r\n\r\nexport default AnimationSettings;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAGCC,UAAU,EACVC,QAAQ,EACRC,MAAM,EACNC,WAAW,QAGL,eAAe;AACtB,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,2BAA2B,MAAM,6CAA6C;AACrF,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC;AAA4C,CAAC,KAAK;EAAAC,EAAA;EAC9E,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGb,QAAQ,CAAC,MAAM,CAAC;EAC5D,MAAM,CAACc,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGf,QAAQ,CAAC,MAAM,CAAC;EAChE,MAAM,CAACgB,MAAM,EAAEC,SAAS,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAE1C,MAAMkB,0BAA0B,GAAIC,KAAgC,IAAK;IACxEN,iBAAiB,CAACM,KAAK,CAACC,MAAM,CAACC,KAAe,CAAC;EAChD,CAAC;EAED,MAAMC,4BAA4B,GAAIH,KAAgC,IAAK;IAC1EJ,mBAAmB,CAACI,KAAK,CAACC,MAAM,CAACC,KAAe,CAAC;EAClD,CAAC;EAED,MAAME,WAAW,GAAGA,CAAA,KAAM;IACzBN,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;EACnB,CAAC;EAED,IAAI,CAACD,MAAM,EAAE,OAAO,IAAI;EAExB,oBACCR,OAAA;IAAKgB,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eACjCjB,OAAA;MAAKgB,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC7BjB,OAAA;QAAKgB,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBACnCjB,OAAA,CAACP,UAAU;UACV,cAAW,OAAO;UAClByB,OAAO,EAAEH,WAAY;UAAAE,QAAA,eAErBjB,OAAA,CAACF,2BAA2B;YAAEkB,SAAS,EAAC;UAAmB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eACbtB,OAAA;UAAKgB,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC5CtB,OAAA,CAACP,UAAU;UACV8B,IAAI,EAAC,OAAO;UACZ,cAAW,OAAO;UAClBL,OAAO,EAAEH,WAAY;UAAAE,QAAA,eAErBjB,OAAA,CAACH,SAAS;YAACmB,SAAS,EAAC;UAAoB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC,eAEVtB,OAAA;QACIwB,KAAK,EAAE;UACHC,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBC,GAAG,EAAE,MAAM;UACXC,OAAO,EAAE;QACb,CAAE;QAAAX,QAAA,gBAGFjB,OAAA,CAACJ,WAAW;UAACiC,SAAS;UAAAZ,QAAA,gBAClBjB,OAAA;YAAKwB,KAAK,EAAE;cAAEM,YAAY,EAAE,KAAK;cAAEC,QAAQ,EAAE;YAAO,CAAE;YAAAd,QAAA,EAAC;UAEvD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtB,OAAA,CAACL,MAAM;YACXqC,OAAO,EAAC,uBAAuB;YAC/BnB,KAAK,EAAET,cAAe;YACtB6B,QAAQ,EAAEvB,0BAA2B;YACrCwB,SAAS,EAAE;cACbC,cAAc,EAAE;gBACNC,IAAI,EAAE;cACV;YACJ,CAAE;YACFZ,KAAK,EAAE;cACHa,MAAM,EAAE,MAAM;cACdC,YAAY,EAAE,KAAK;cACnBP,QAAQ,EAAE,MAAM;cAChBQ,SAAS,EAAE,MAAM;cACjBC,eAAe,EAAE;YACrB,CAAE;YAAAvB,QAAA,gBAEFjB,OAAA,CAACN,QAAQ;cAACmB,KAAK,EAAC,MAAM;cAAAI,QAAA,EAAC;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACtCtB,OAAA,CAACN,QAAQ;cAACmB,KAAK,EAAC,eAAe;cAAAI,QAAA,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACxDtB,OAAA,CAACN,QAAQ;cAACmB,KAAK,EAAC,MAAM;cAAAI,QAAA,EAAC;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEQ,CAAC,eAGdtB,OAAA,CAACJ,WAAW;UAACiC,SAAS;UAAAZ,QAAA,gBAClBjB,OAAA;YAAKwB,KAAK,EAAE;cAAEM,YAAY,EAAE,KAAK;cAAEC,QAAQ,EAAE;YAAO,CAAE;YAAAd,QAAA,EAAC;UAEvD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtB,OAAA,CAACL,MAAM;YACXqC,OAAO,EAAC,yBAAyB;YACjCnB,KAAK,EAAEP,gBAAiB;YACxB2B,QAAQ,EAAEnB,4BAA6B;YACvCoB,SAAS,EAAE;cACbC,cAAc,EAAE;gBACNC,IAAI,EAAE;cACV;YACJ,CAAE;YACFZ,KAAK,EAAE;cACHc,YAAY,EAAE,KAAK;cACnBP,QAAQ,EAAE,MAAM;cAChBQ,SAAS,EAAE,MAAM;cACjBC,eAAe,EAAE;YACrB,CAAE;YAAAvB,QAAA,gBAEFjB,OAAA,CAACN,QAAQ;cAACmB,KAAK,EAAC,MAAM;cAAAI,QAAA,EAAC;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACtCtB,OAAA,CAACN,QAAQ;cAACmB,KAAK,EAAC,UAAU;cAAAI,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAER,CAAC;AAACnB,EAAA,CA5GIF,iBAAiB;AAAAwC,EAAA,GAAjBxC,iBAAiB;AA8GvB,eAAeA,iBAAiB;AAAC,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}