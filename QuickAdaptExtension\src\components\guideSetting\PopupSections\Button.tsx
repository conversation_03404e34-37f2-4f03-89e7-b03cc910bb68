import React, { useState,useEffect } from "react";
import { Box, Button, Popover, Typography, Text<PERSON>ield, IconButton, Tooltip } from "@mui/material";
import { ChromePicker, ColorResult } from "react-color";
import { deleteicon, copyicon, settingsicon, backgroundcoloricon, editicon } from "../../../assets/icons/icons";
import useDrawerStore, { TButton } from "../../../store/drawerStore";
import AddIcon from "@mui/icons-material/Add";
import CloseIcon from "@mui/icons-material/Close";
import ButtonSettings from "../../guideBanners/selectedpopupfields/ImageProperties";
import { useTranslation } from 'react-i18next';

const ButtonSection: React.FC<{ buttonColor: string; setButtonColor: (str: string) => void; isBanner: boolean; index?: number; isCloneDisabled?: boolean; onDelete?: () => void; onClone?: () => void;}> = ({
	buttonColor,
	setButtonColor,
	isBanner,
	isCloneDisabled,
	onDelete,
	onClone
}) => {
	const {
		buttonsContainer,
		cloneButtonContainer,
		updateButton,
		addNewButton,
		deleteButton,
		deleteButtonContainer,
		updateContainer,
		setSettingAnchorEl,
		selectedTemplate,
		selectedTemplateTour,
		setSelectedTemplate,
		buttonProperty,
		setButtonProperty,
		btnBgColor,
		btnBorderColor,
		btnTextColor,
		setButtonId,
		setCuntainerId,
		createWithAI,
		currentStep,
		ensureAnnouncementButtonContainer,
		closeAllButtonPopups,
		selectedTheme,
		applyThemeToAllButtons
	} = useDrawerStore((state: any) => state);
	const { t: translate } = useTranslation();
	const [isEditingPrevious, setIsEditingPrevious] = useState<boolean>(false);
	const [isEditingContinue, setIsEditingContinue] = useState<boolean>(false);
	const [previousButtonText, setPreviousButtonText] = useState<string>("Previous");
	const [continueButtonText, setContinueButtonText] = useState<string>("Continue");
	const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
	const [selectedColor, setSelectedColor] = useState<string>("#313030");
	const [colorPickerAnchorEl, setColorPickerAnchorEl] = useState<HTMLElement | null>(null);
	const [buttonText, setButtonText] = useState<string>("Continue");
	const [buttonToEdit, setButtonToEdit] = useState<string | null>(null);
	const [isDeleteIcon, setIsDeleteIcon] = useState("");
	const [currentContainerId, setCurrentContainerId] = useState("");
	const [currentButtonId, setCurrentButtonId] = useState("");
	const [isEditingButton, setIsEditingButton] = useState(false);
	const [isEditing, setIsEditing] = useState<string | null>(null);

	// Default button color
	let clickTimeout: NodeJS.Timeout;

	useEffect(() => {
		setAnchorEl(null);
		setButtonProperty(false);
	}, []); // Empty dependency array ensures it runs only once

	// Listen for global close all button popups trigger
	useEffect(() => {
		if (closeAllButtonPopups > 0) {
			setAnchorEl(null);
			setColorPickerAnchorEl(null);
			setButtonToEdit(null);
		}
	}, [closeAllButtonPopups]);

	const handleClick = (event: React.MouseEvent<HTMLElement>, buttonId: string) => {
		const target = event.currentTarget;

		clickTimeout = setTimeout(() => {
			setAnchorEl(target);
			setCurrentButtonId(buttonId);
			setIsEditingButton(false);
			handleEditButtonName(currentContainerId, buttonId, "isEditing", false);
		}, 200);
	};

	const handleClose = () => {
		setAnchorEl(null);
		setSettingAnchorEl({ containerId: "", buttonId: "", value: null });
		setButtonToEdit(null);
	};

	const handlePreviousTextChange = (event: React.ChangeEvent<HTMLInputElement>) => {
		setPreviousButtonText(event.target.value);
	};

	const handleContinueTextChange = (event: React.ChangeEvent<HTMLInputElement>) => {
		setContinueButtonText(event.target.value);
	};

	const handleBackgroundColorClick = (event: React.MouseEvent<HTMLElement>) => {
		setColorPickerAnchorEl(event.currentTarget);
	};

	const handleColorChange = (color: ColorResult) => {
		setSelectedColor(color.hex);
		// Update the backgroundColor in the container's style
		updateContainer(currentContainerId, "style", {
			backgroundColor: color.hex,
		});

		// Also update the BackgroundColor property at the ButtonSection level
		updateContainer(currentContainerId, "BackgroundColor", color.hex);
	};

	const handleCloseColorPicker = () => {
		setColorPickerAnchorEl(null);
	};

	const open = Boolean(anchorEl);
	// const open = Boolean(anchorEl && !isEditingButton);
	const id = open ? "button-popover" : undefined;
	const colorPickerOpen = Boolean(colorPickerAnchorEl);
	const toggleEdit = (button: "Previous" | "Continue") => {
		if (button === "Previous") {
			setIsEditingPrevious(true);
		} else if (button === "Continue") {
			setIsEditingContinue(true);
		}
	};

	const handlePreviousBlur = () => {
		setIsEditingPrevious(false);
	};

	const handleContinueBlur = () => {
		setIsEditingContinue(false);
	};
	const handleEditButtonName = (
		containerId: string,
		buttonId: string,
		isEditing: keyof TButton,
		value: TButton[keyof TButton]
	) => {
		clearTimeout(clickTimeout);
		setIsEditingButton(true);
		//updateButton(containerId, buttonId, isEditing, value);
	};

	const handleChangeButton = (containerId: string, buttonId: string, value: TButton[keyof TButton]) => {
		updateButton(containerId, buttonId, "type", value);
	};
	const handleEditButtonText = (containerId: string, buttonId: string, newText: string) => {
		updateButton(containerId, buttonId, "name", newText);
		setButtonToEdit(null); // Exit edit mode after saving
	};
	//   const [buttonCount, setButtonCount] = useState(0);
	const handleAddIconClick = (containerId: string) => {
		//  const buttonName = buttonCount === 0 ? "Got It" : `Button${buttonCount + 1}`;
		addNewButton(
			{
				id: crypto.randomUUID(),
				name: "Button 1",
				position: "center",
				type: "primary",
				isEditing: false,
				index: 0,
				style: {
					backgroundColor: "var(--Theme-accentColor)",
				},
			},
			containerId
		);
		applyThemeToAllButtons(selectedTheme.ThemeStyles.Button);
		// setButtonCount(buttonCount + 1);
	};

	// shouldShowAddBtn will be calculated per section inside the map

	const currentContainerColor =
		buttonsContainer.find((item: any) => item.id === currentContainerId)?.style?.backgroundColor || "var(--Theme-accentColor)";

	const handleDelteContainer = () => {
		deleteButtonContainer(currentContainerId);
		setAnchorEl(null);

		// Call the onDelete callback if provided
		if (onDelete) {
			onDelete();
		}
	};
	setButtonId(currentButtonId);
	setCuntainerId(currentButtonId);

	const handleSettingIconClick = (event: React.MouseEvent<HTMLElement>) => {
		if (selectedTemplate === "Banner" || selectedTemplateTour === "Banner") {
			setSettingAnchorEl({
				containerId: currentContainerId,
				buttonId: currentButtonId,
				// @ts-ignore
				value: event.currentTarget,
			});
			setButtonProperty(true);
		} else {
			setSettingAnchorEl({
				containerId: currentContainerId,
				buttonId: currentButtonId,
				// @ts-ignore
				value: event.currentTarget,
			});
		}
		setAnchorEl(null);
		setButtonToEdit(null);

		//setAnchorEl(null);
	};

	// Determine which containers to use based on whether this is an AI announcement
	const isAIAnnouncement = createWithAI && (selectedTemplate === "Announcement" || selectedTemplateTour === "Announcement");
	const isTourAnnouncement = createWithAI && selectedTemplate === "Tour" && selectedTemplateTour === "Announcement";
	const currentStepIndex = currentStep - 1;

	let containersToRender: any[] = [];

	if (isAIAnnouncement) {
		// Use the store function to ensure Button containers exist
		containersToRender = ensureAnnouncementButtonContainer(currentStepIndex, isTourAnnouncement);
	} else {
		// For banners and non-AI content, use buttonsContainer
		containersToRender = buttonsContainer;
	}

	return (
		<>
			{containersToRender.map((buttonItem: any) => {
				return (
					<Box
						component="div"
						id={buttonItem.id}
						// className={isBanner ? "qadpt-banner-btn" : ""}
						sx={{
							height: isBanner ? "40px !important" : "60px !important",
							width: "100%",
							display: "flex",
							alignItems: "center",
							gap: "8px",
							padding: "0 16px",
							boxSizing: "border-box",
							backgroundColor: buttonItem.style.backgroundColor ? buttonItem.style.backgroundColor : btnBgColor,
							justifyContent: "center",
							// "&.qadpt-banner-btn": { height: "40px" },
						}}
						onMouseEnter={(e) => setCurrentContainerId(e.currentTarget.id)}
					>
						{buttonItem.buttons.map((item: any) => (
							<Box
								key={item.id}
								sx={{
									position: "relative",
									display: "flex",
									justifyContent: "center",
									alignItems: "center",
									"&:hover .delete-icon": {
										// Add this hover effect to display the delete icon when the button is hovered
										opacity: 1,
									},
								}}
								onMouseEnter={() => setCurrentContainerId(buttonItem.id)}
								onMouseLeave={() => setIsDeleteIcon("")}
							>
								<Button
									id={item.id}
									variant={"contained"}
									sx={{
										lineHeight: "var(--button-lineheight)",
										padding: "var(--button-padding)",
										borderRadius: "8px",
										color: item.style.color || "#fff",
										border: item.style.borderColor
											? `2px solid ${item.style.borderColor}`
											: "none",
										textTransform: "none",
										backgroundColor: item.style.backgroundColor,
										boxShadow: "none",
										"&:hover": {
											boxShadow: "none !important",
										}
									}}
									onClick={(e) => handleClick(e, item.id)}
								>
									<Typography style={{ color: btnTextColor }}>{item.name}</Typography>
								</Button>

								{/* Delete Icon - Right Side, only visible on hover */}
								{buttonItem.buttons.length > 1 && (
									<IconButton
										size="small"
										className="delete-icon"
										sx={{
											position: "absolute",
											right: "-10px",
											top: "0",
											transform: "translateY(-50%)",
											backgroundColor: "#fff",
											boxShadow: "rgba(0, 0, 0, 0.4) 0px 2px 6px",
											opacity: 0, // Initially hidden
											transition: "opacity 0.3s ease", // Smooth transition
											zIndex: 1,
											padding: "3px !important",
											"&:hover": {
												backgroundColor: "#fff",
												boxShadow: "none !important",
											},
											span: {
												height: "16px"
											},
											"& svg": {
												width: "14px", // Set the width of the SVG
												height: "14px", // Set the height of the SVG
												path: {
													fill:"#ff0000"
												}
											},
										}}
										onClick={() => deleteButton(buttonItem.id, item.id)}
									>
										<span dangerouslySetInnerHTML={{ __html: deleteicon }} />
									</IconButton>
								)}
							</Box>
						))}

						{(selectedTemplate === "Announcement" || selectedTemplateTour === "Announcement") &&
						buttonItem.buttons.length < 4  ? (
							<IconButton
								sx={{
									backgroundColor: "var(--Theme-accentColor)",
									cursor: "pointer",
									zIndex: 1000,
									padding: "6px !important",
									"&:hover": {
										backgroundColor: "#70afaf",
									},
								}}
								// sx={sideAddButtonStyle}
								onClick={() => {
									handleAddIconClick(buttonItem.id);
									if (onClone) {
										onClone();
									}
								}}
							>
								<AddIcon
									fontSize="small"
									sx={{ color: "#fff" }}
								/>
							</IconButton>
						) : null}
					</Box>
				);
			})}
			<Popover
								className="qadpt-bunprop"
				id={id}
				open={open}
				anchorEl={anchorEl}
				onClose={handleClose}
				anchorOrigin={{
					vertical: "top",
					horizontal: "left",
				}}
				transformOrigin={{
					vertical: "bottom",
					horizontal: "left",
				}}
				sx={{
					marginTop: isBanner ? "100px !important" : "-5px",
				}}
				// className={isBanner ? "qadpt-banner-btn" : ""}
			>
				<Box
					sx={{
						display: "flex",
						alignItems: "center",
						gap: "8px",
						padding: "8px",
					}}
				>
					{/* <Tooltip
				title="Coming Soon"
				PopperProps={{
					sx: {
						zIndex: 9999,
					},
				}}
			>
				<span>  */}
					{/* <Typography
						variant="body2"
						sx={{ cursor: "pointer", fontWeight: "bold", opacity: 0.5 }}
						component={"div"}
						id="primary"
						onClick={(e) => handleChangeButton(currentContainerId, currentButtonId, e.currentTarget.id)}
					>
						Primary
					</Typography>
					{/* </span>
					</Tooltip>
					<Tooltip
						title="Coming Soon"
						PopperProps={{
							sx: {
								zIndex: 9999,
							},
						}}
					>
						<span>
							<Typography
								variant="body2"
								sx={{ cursor: "pointer", color: "gray", opacity: 0.5 }}
								component={"div"}
								id="secondary"
								//onClick={(e) => handleChangeButton(currentContainerId, currentButtonId, e.currentTarget.id)}
							>
								Secondary
							</Typography>
						</span>
					</Tooltip>

					<Box sx={{ borderLeft: "1px solid #ccc", height: "24px", marginLeft: "8px" }}></Box>
					*/}

					{/* Icons for additional options */}
					<Tooltip title={translate("Settings")}>
						<IconButton
							size="small"
							onClick={handleSettingIconClick}
						>
							<span dangerouslySetInnerHTML={{ __html: settingsicon }} />
						</IconButton>
					</Tooltip>
					{(selectedTemplate === "Announcement" || selectedTemplateTour === "Announcement") && (
						<Tooltip title={translate("Background Color")}>
							<IconButton
								size="small"
								onClick={handleBackgroundColorClick}
							>
								<span
									style={{
										backgroundColor: selectedColor,
										borderRadius: "100%",
										width: "20px",
										height: "20px",
										display: "inline-block",
										marginTop: "-3px",
									}}
								/>
							</IconButton>
						</Tooltip>
					)}
					{(selectedTemplate === "Announcement" || selectedTemplateTour === "Announcement") && (
						<Tooltip title={isCloneDisabled ? translate("Maximum limit of 3 Button sections reached") : translate("Clone Button")}>
							<IconButton
								size="small"
								onClick={() => {
									cloneButtonContainer(currentContainerId);
									if (onClone) {
										onClone();
									}
								}}
								disabled={isCloneDisabled}
							>
								<span
									dangerouslySetInnerHTML={{ __html: copyicon }}
									style={{ opacity: isCloneDisabled ? 0.5 : 1 }}
								/>
							</IconButton>
						</Tooltip>
					)}
					{(selectedTemplate === "Announcement" || selectedTemplateTour === "Announcement") && (
						<Tooltip title={translate("Delete Button")}>
							<IconButton
								size="small"
								onClick={handleDelteContainer}
							>
								<span
									dangerouslySetInnerHTML={{ __html: deleteicon }}
									style={{ marginTop: "-3px" }}
								/>
							</IconButton>
						</Tooltip>
					)}
				</Box>
			</Popover>

			{/* Color Picker Popover */}
			<Popover
				open={colorPickerOpen}
				anchorEl={colorPickerAnchorEl}
				onClose={handleCloseColorPicker}
				anchorOrigin={{
					vertical: "bottom",
					horizontal: "center",
				}}
				transformOrigin={{
					vertical: "top",
					horizontal: "center",
				}}
			>
				<Box>
					<ChromePicker
						color={currentContainerColor}
						onChange={handleColorChange}
					/>
					<style>
						{`
      .chrome-picker input {
        padding: 0 !important;
      }
    `}
					</style>
				</Box>
			</Popover>
		</>
	);
};

export default ButtonSection;
