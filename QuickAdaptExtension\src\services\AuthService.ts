import axios from 'axios';


const API_URL = process.env.IDS_BASE_URL;

const getAuthToken = () => {
    return localStorage.getItem('authToken');
};

const setAuthToken = (token:any) => {
    localStorage.setItem('authToken', token);
};

const removeAuthToken = () => {
    localStorage.removeItem('authToken');
};

const login = async (credentials:any) => {
    const response = await axios.post(`${API_URL}/account/login`, credentials);
    return response.data;
};

const logout = () => {
    removeAuthToken();
};

export {
    getAuthToken,
    setAuthToken,
    removeAuthToken,
    login,
    logout
};
