import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import { getLabels, getLanguages, LanguageType } from './LanguageService';

// Constants
const DEFAULT_LANGUAGE = 'en';
const FALLBACK_LANGUAGE = 'en';
const LANGUAGE_STORAGE_KEY_PREFIX = 'quickadapt_language_';

// Internal state
let availableLanguages: LanguageType[] = [];
let initialized = false;
let isLoggedIn = false;
let currentOrgId: string | undefined = undefined;

// Get organization-specific language key
const getOrgLanguageKey = (orgId: string | undefined): string => {
  return `${LANGUAGE_STORAGE_KEY_PREFIX}${orgId || 'default'}`;
};

// Language persistence utilities
const getSavedLanguage = (orgId?: string): string => {
  try {
    const key = getOrgLanguageKey(orgId || currentOrgId);
    return localStorage.getItem(key) || DEFAULT_LANGUAGE;
  } catch {
    return DEFAULT_LANGUAGE;
  }
};

const saveLanguage = (language: string, orgId?: string): void => {
  try {
    const key = getOrgLanguageKey(orgId || currentOrgId);
    localStorage.setItem(key, language);
    console.log(`💾 Saved language ${language} for org ${orgId || currentOrgId}`);
  } catch (err) {
    console.error("Failed to save language preference:", err);
  }
};
const clearSavedLanguage = (orgId?: string): void => {
  try {
    const key = getOrgLanguageKey(orgId || currentOrgId);
    localStorage.removeItem(key);
    console.log(`🗑️ Cleared saved language for org ${orgId || currentOrgId}`);
  } catch (err) {
    console.error("Failed to clear language preference:", err);
  }
};

// Custom backend to load translations from API
class CustomBackend {
  static type = 'backend' as const;

  init() {}

  async read(language: string, namespace: string, callback: (error: any, data?: any) => void) {
    console.log(`🔍 CustomBackend.read() called with language: ${language}, namespace: ${namespace}`);
    
    try {
      const langCode = language.toLowerCase();
      
      // For English, return empty object (keys will be used as values)
      if (langCode === 'en') {
        console.log('🚀 English detected - returning empty translations');
        callback(null, {});
        return;
      }

      // For non-English, check if user is logged in
      if (!isLoggedIn) {
        console.log('⚠️ User not logged in - returning empty translations');
        callback(null, {});
        return;
      }

      // Load available languages if not already loaded
      if (availableLanguages.length === 0) {
        console.log('🔄 Loading available languages...');
        try {
          availableLanguages = await getLanguages();
          console.log(`✅ Loaded ${availableLanguages.length} available languages`);
        } catch (error) {
          console.error('❌ Failed to load available languages:', error);
          callback(null, {});
          return;
        }
      }

      // Find the language metadata
      const langMeta = availableLanguages.find(l => l.LanguageCode.toLowerCase() === langCode);
      if (!langMeta) {
        console.warn(`⚠️ Language code ${langCode} not found in available languages`);
        callback(null, {});
        return;
      }

      // Load translations from API
      console.log(`🌐 Loading translations for ${langMeta.Language} (${langCode})`);
      const response = await getLabels(langMeta.Language);
      const translations = response[langCode] || response;
      
      console.log(`✅ Loaded ${Object.keys(translations || {}).length} translations for ${langCode}`);
      callback(null, translations || {});
      
    } catch (err) {
      console.error(`❌ Error loading translations for ${language}:`, err);
      callback(null, {}); // Return empty object on error to prevent crashes
    }
  }
}

// Initialize i18n (call this once from App.tsx)
export const initializeI18n = async (): Promise<void> => {
  if (initialized) return;

  await i18n
    .use(CustomBackend as any)
    .use(initReactI18next)
    .init({
      lng: DEFAULT_LANGUAGE, // Start with default language, will be set from user profile
      fallbackLng: FALLBACK_LANGUAGE,
      interpolation: {
        escapeValue: false,
      },
      react: {
        useSuspense: false,
      },
      backend: {
        crossDomain: true,
      },
    });

  initialized = true;
  console.log('✅ i18n initialized successfully');
};

// Load saved language translations after login
export const loadSavedLanguageTranslations = async (orgId?: string): Promise<void> => {
  if (!isLoggedIn) {
    console.log('⏳ User not logged in - skipping language load');
    return;
  }

  // Note: Language is now set from user profile instead of localStorage
  console.log('ℹ️ Language will be set from user profile preference');
};

// Set login status and organization (call this when user logs in/out)
export const setLoginStatus = (loggedIn: boolean, orgId?: string): void => {
  if (!loggedIn) {
    // Reset to default language when logging out
    i18n.changeLanguage(DEFAULT_LANGUAGE);
  }
  isLoggedIn = loggedIn;
  currentOrgId = orgId;
  console.log(`🔐 Login status set to: ${loggedIn}, org: ${orgId}`);
};

// Language management functions
export const loadAvailableLanguages = async (): Promise<LanguageType[]> => {
  try {
    availableLanguages = await getLanguages();
    return availableLanguages;
  } catch (err) {
    console.error("Failed to load available languages:", err);
    return [];
  }
};

export const getAvailableLanguages = (): LanguageType[] => availableLanguages;

export const getCurrentLanguage = (): string => i18n.language || DEFAULT_LANGUAGE;

export const changeLanguage = async (languageCode: string): Promise<void> => {
  try {
    console.log(`🔄 Changing language to: ${languageCode}`);
    
    // For non-English languages, force reload resources to trigger backend
    if (languageCode.toLowerCase() !== 'en') {
      console.log('🔄 Reloading resources for:', languageCode);
      await i18n.reloadResources(languageCode);
    }
    
    // Change the language
    await i18n.changeLanguage(languageCode);
    
    // Note: No need to save to localStorage since language preference is managed via API
    
    console.log(`✅ Language changed to: ${languageCode}`);
  } catch (err) {
    console.error("Failed to change language:", err);
    throw err;
  }
};

export const getCurrentLanguageInfo = (): LanguageType | undefined => {
  const currentCode = getCurrentLanguage().toLowerCase();
  return availableLanguages.find(l => l.LanguageCode.toLowerCase() === currentCode);
};

export const isI18nInitialized = (): boolean => initialized;

export default i18n;
export type { LanguageType };
