{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideDesign\\\\CanvasSettings.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Box, Typography, TextField, Grid, IconButton, Button } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\n// import Draggable from \"react-draggable\";\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\nimport \"./Canvas.module.css\";\nimport useDrawerStore from \"../../store/drawerStore\";\nimport { defaultDots, topLeft, topRight, middleLeft, middleCenter, middleRight, bottomLeft, bottomMiddle, bottomRight, topcenter, warning, centercenter } from \"../../assets/icons/icons\";\nimport { useTranslation } from \"react-i18next\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CanvasSettings = ({\n  zindeex,\n  setZindeex,\n  setShowCanvasSettings,\n  selectedTemplate\n}) => {\n  _s();\n  const {\n    setCanvasSetting,\n    borderColor,\n    announcementJson,\n    width,\n    setWidth,\n    backgroundColor,\n    setBorderColor,\n    setBackgroundColor,\n    borderRadius,\n    setBorderRadius,\n    Annpadding,\n    setAnnPadding,\n    AnnborderSize,\n    setAnnBorderSize,\n    Bposition,\n    setBposition,\n    setIsUnSavedChanges,\n    setIsThemeChanges,\n    currentStep,\n    syncAIAnnouncementCanvasSettings,\n    createWithAI,\n    //selectedTemplate,\n    selectedTemplateTour,\n    selectedTheme\n  } = useDrawerStore(state => state);\n  const {\n    t: translate\n  } = useTranslation();\n  const [isOpen, setIsOpen] = useState(true);\n  const [selectedPosition, setSelectedPosition] = useState(\"middle-center\");\n  const [widthError, setWidthError] = useState(false);\n  const [paddingError, setPaddingError] = useState(false);\n  const [borderRadiusError, setBorderRadiusError] = useState(false);\n  const [borderSizeError, setBorderSizeError] = useState(false);\n  const positions = [{\n    label: translate(\"Top Left\"),\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: topLeft\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 41\n    }, this),\n    value: \"top-left\"\n  }, {\n    label: translate(\"Top Center\"),\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: topcenter\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 43\n    }, this),\n    value: \"top-center\"\n  }, {\n    label: translate(\"Top Right\"),\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: topRight\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 42\n    }, this),\n    value: \"top-right\"\n  }, {\n    label: translate(\"Middle Left\"),\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: middleLeft\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 44\n    }, this),\n    value: \"left-center\"\n  }, {\n    label: translate(\"Middle Center\"),\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: middleCenter\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 46\n    }, this),\n    value: \"center-center\"\n  }, {\n    label: translate(\"Middle Right\"),\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: middleRight\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 45\n    }, this),\n    value: \"right-center\"\n  }, {\n    label: translate(\"Bottom Left\"),\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: bottomLeft\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 44\n    }, this),\n    value: \"bottom-left\"\n  }, {\n    label: translate(\"Bottom Center\"),\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: bottomMiddle\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 46\n    }, this),\n    value: \"bottom-center\"\n  }, {\n    label: translate(\"Bottom Right\"),\n    icon: /*#__PURE__*/_jsxDEV(\"span\", {\n      dangerouslySetInnerHTML: {\n        __html: bottomRight\n      },\n      style: {\n        fontSize: \"small\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 45\n    }, this),\n    value: \"bottom-right\"\n  }];\n  const renderPositionIcon = positionValue => {\n    const isSelected = Bposition === positionValue;\n    if (!isSelected) {\n      return /*#__PURE__*/_jsxDEV(\"span\", {\n        dangerouslySetInnerHTML: {\n          __html: defaultDots\n        },\n        style: {\n          fontSize: \"small\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 12\n      }, this);\n    }\n    switch (positionValue) {\n      case \"top-left\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          dangerouslySetInnerHTML: {\n            __html: topLeft\n          },\n          style: {\n            fontSize: \"small\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this);\n      case \"top-center\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          dangerouslySetInnerHTML: {\n            __html: topcenter\n          },\n          style: {\n            fontSize: \"small\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this);\n      case \"top-right\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          dangerouslySetInnerHTML: {\n            __html: topRight\n          },\n          style: {\n            fontSize: \"small\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this);\n      case \"left-center\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          dangerouslySetInnerHTML: {\n            __html: middleLeft\n          },\n          style: {\n            fontSize: \"small\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this);\n      case \"center-center\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          dangerouslySetInnerHTML: {\n            __html: middleCenter\n          },\n          style: {\n            fontSize: \"small\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this);\n      case \"right-center\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          dangerouslySetInnerHTML: {\n            __html: middleRight\n          },\n          style: {\n            fontSize: \"small\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this);\n      case \"bottom-left\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          dangerouslySetInnerHTML: {\n            __html: bottomLeft\n          },\n          style: {\n            fontSize: \"small\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this);\n      case \"bottom-center\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          dangerouslySetInnerHTML: {\n            __html: bottomMiddle\n          },\n          style: {\n            fontSize: \"small\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this);\n      case \"bottom-right\":\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          dangerouslySetInnerHTML: {\n            __html: bottomRight\n          },\n          style: {\n            fontSize: \"small\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(\"span\", {\n          dangerouslySetInnerHTML: {\n            __html: defaultDots\n          },\n          style: {\n            fontSize: \"small\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this);\n    }\n  };\n  useEffect(() => {\n    var _announcementJson$Gui, _announcementJson$Gui2, _announcementJson$Gui3;\n    const currentStepIndex = announcementJson === null || announcementJson === void 0 ? void 0 : (_announcementJson$Gui = announcementJson.GuideStep) === null || _announcementJson$Gui === void 0 ? void 0 : _announcementJson$Gui.findIndex(step => {\n      let stepNum = step.stepName; // \n      if (typeof stepNum === \"string\" && stepNum.toLowerCase().startsWith(\"step\")) {\n        stepNum = parseInt(stepNum.replace(/[^0-9]/g, \"\"), 10);\n      }\n      return String(stepNum) === String(currentStep);\n    });\n\n    // Get canvas data for the current step (if found), otherwise use the first step\n    const canvasData = currentStepIndex !== -1 ? announcementJson === null || announcementJson === void 0 ? void 0 : (_announcementJson$Gui2 = announcementJson.GuideStep) === null || _announcementJson$Gui2 === void 0 ? void 0 : (_announcementJson$Gui3 = _announcementJson$Gui2[currentStepIndex]) === null || _announcementJson$Gui3 === void 0 ? void 0 : _announcementJson$Gui3.Canvas : {\n      Position: \"center-center\",\n      Width: 500,\n      Padding: \"12\",\n      Radius: 8,\n      BorderSize: 0,\n      BorderColor: \"#000000\",\n      BackgroundColor: \"#ffffff\"\n    };\n    ;\n    let initialWidth;\n    let initialPadding;\n    let initialBorderRadius;\n    let initialBorderSize;\n    if (canvasData) {\n      var _canvasData$Radius;\n      setSelectedPosition(canvasData.Position || \"center-center\");\n      setBposition(canvasData.Position || \"center-center\");\n      initialWidth = canvasData.Width || 500;\n      setBackgroundColor(canvasData.BackgroundColor || \"#ffffff\");\n      initialBorderRadius = (_canvasData$Radius = canvasData.Radius) !== null && _canvasData$Radius !== void 0 ? _canvasData$Radius : 8;\n      initialPadding = canvasData.Padding || \"12\";\n      setBorderColor(canvasData.BorderColor || \"#000000\");\n      initialBorderSize = canvasData.BorderSize || 0;\n    } else {\n      setSelectedPosition(\"center-center\");\n      setBposition(\"center-center\");\n      initialWidth = 500;\n      setBackgroundColor(\"#ffffff\");\n      initialBorderRadius = 8;\n      initialPadding = \"12\";\n      setBorderColor(\"#000000\");\n      initialBorderSize = 0;\n    }\n\n    // Validate initial width\n    if (initialWidth < 300 || initialWidth > 1200) {\n      setWidthError(true);\n      // Set width to closest valid value\n      setWidth(initialWidth < 300 ? 300 : 1200);\n    } else {\n      setWidthError(false);\n      setWidth(initialWidth);\n    }\n\n    // Validate initial padding\n    const paddingValue = parseInt(initialPadding) || 12;\n    if (paddingValue < 0 || paddingValue > 20) {\n      setPaddingError(true);\n      // Set padding to closest valid value\n      setAnnPadding(paddingValue < 0 ? \"0\" : \"20\");\n    } else {\n      setPaddingError(false);\n      setAnnPadding(initialPadding);\n    }\n\n    // Validate initial border radius\n    if (initialBorderRadius < 0 || initialBorderRadius > 20) {\n      setBorderRadiusError(true);\n      // Set border radius to closest valid value\n      setBorderRadius(initialBorderRadius < 0 ? 0 : 20);\n    } else {\n      setBorderRadiusError(false);\n      setBorderRadius(initialBorderRadius);\n    }\n\n    // Validate initial border size\n    if (initialBorderSize < 0 || initialBorderSize > 10) {\n      setBorderSizeError(true);\n      // Set border size to closest valid value\n      setAnnBorderSize(initialBorderSize < 0 ? 0 : 10);\n    } else {\n      setBorderSizeError(false);\n      setAnnBorderSize(initialBorderSize);\n    }\n  }, [announcementJson, currentStep, setBposition, setWidth, setBackgroundColor, setBorderRadius, setAnnPadding, setBorderColor, setAnnBorderSize]);\n  // useEffect(() => {\n  // \tconst canvas = selectedTheme?.ThemeStyles?.Canvas;\n  // \tconsole.log(\"Canvas use effect\");\n  // \tif (canvas) {\n  // \t  setBackgroundColor(canvas.canvasBgColor || \"#ffffff\");\n  // \t  setBorderColor(canvas.canvasBorderColor || \"#000000\");\n  // \t  setBorderRadius(canvas.canvasRadius ?? 8);\n  // \t  setAnnPadding(canvas.canvasPadding?.toString() || \"12\");\n  // \t  setAnnBorderSize(canvas.canvasBorderSize ?? 2);\n  // \t  setWidth(500); // optional: set if needed\n  // \t}\n  // \thandleApplyChanges();\n  //   }, [selectedTheme]);\n  const handlePositionClick = positionValue => {\n    setSelectedPosition(positionValue);\n    setBposition(positionValue);\n  };\n  const handleBorderColorChange = e => setBorderColor(e.target.value);\n  const handleBackgroundColorChange = e => setBackgroundColor(e.target.value);\n  const handleClose = () => {\n    setIsOpen(false);\n    setShowCanvasSettings(false);\n  };\n  const handleApplyChanges = () => {\n    var _selectedTheme$ThemeS;\n    const canvas = selectedTheme === null || selectedTheme === void 0 ? void 0 : (_selectedTheme$ThemeS = selectedTheme.ThemeStyles) === null || _selectedTheme$ThemeS === void 0 ? void 0 : _selectedTheme$ThemeS.Canvas;\n    // Don't apply changes if there's any validation error\n    if (widthError || paddingError || borderRadiusError || borderSizeError) {\n      return;\n    }\n    const canvasData = {\n      Position: selectedPosition,\n      BackgroundColor: backgroundColor,\n      Width: width || 500,\n      Radius: borderRadius !== undefined ? borderRadius : 0,\n      Padding: Annpadding || \"12\",\n      BorderColor: borderColor,\n      BorderSize: AnnborderSize || 0,\n      Zindex: 9999\n    };\n    setCanvasSetting(canvasData);\n\n    // Sync AI announcement canvas settings after applying changes\n    if (createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\")) {\n      // Use setTimeout to ensure setCanvasSetting completes first\n      setTimeout(() => {\n        syncAIAnnouncementCanvasSettings(canvasData);\n      }, 0);\n    }\n    setBposition(selectedPosition);\n    handleClose();\n    setIsUnSavedChanges(true);\n    setIsThemeChanges(true);\n  };\n  if (!isOpen) return null;\n  return (\n    /*#__PURE__*/\n    //<Draggable>\n    _jsxDEV(\"div\", {\n      id: \"qadpt-designpopup\",\n      className: \"qadpt-designpopup\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-design-header\",\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            \"aria-label\": \"close\",\n            onClick: handleClose,\n            children: /*#__PURE__*/_jsxDEV(ArrowBackIosNewOutlinedIcon, {\n              className: \"qadpt-design-back\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 7\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 6\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-title\",\n            children: translate(\"Canvas\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 6\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            \"aria-label\": \"close\",\n            onClick: handleClose,\n            children: /*#__PURE__*/_jsxDEV(CloseIcon, {\n              className: \"qadpt-design-close\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 7\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 6\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 5\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-canblock\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-controls qadpt-errmsg\",\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-position-grid\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                className: \"qadpt-ctrl-title\",\n                children: translate(\"Position\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                id: \"pos-container\",\n                container: true,\n                spacing: 1\n                //onClick={handlePositionClick}\n                ,\n                children: positions.map(position => /*#__PURE__*/_jsxDEV(Grid, {\n                  item: true,\n                  xs: 4,\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    disableRipple: true\n                    // sx={{\n                    //     backgroundColor: Bposition === position.value ? \"var(--border-color)\" : \"transparent\",\n                    // }}\n                    ,\n                    onClick: () => handlePositionClick(position.value) // Pass value directly\n                    ,\n                    children: renderPositionIcon(position.value)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 278,\n                    columnNumber: 9\n                  }, this)\n                }, position.value, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 8\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 6\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 5\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate(\"Width\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  variant: \"outlined\",\n                  value: `${width}`,\n                  size: \"small\",\n                  autoFocus: true,\n                  className: \"qadpt-control-input\",\n                  onChange: e => {\n                    const inputValue = parseInt(e.target.value) || 0;\n\n                    // Validate width between 300px and 1200px\n                    if (inputValue < 300 || inputValue > 1200) {\n                      setWidthError(true);\n                    } else {\n                      setWidthError(false);\n                    }\n                    setWidth(inputValue);\n                  },\n                  InputProps: {\n                    endAdornment: \"px\",\n                    sx: {\n                      \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"& fieldset\": {\n                        border: \"none\"\n                      }\n                    }\n                  },\n                  error: widthError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 7\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 6\n            }, this), widthError && /*#__PURE__*/_jsxDEV(Typography, {\n              style: {\n                fontSize: \"12px\",\n                color: \"#e9a971\",\n                textAlign: \"left\",\n                top: \"100%\",\n                left: 0,\n                marginBottom: \"5px\",\n                display: \"flex\",\n                alignItems: centercenter\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  display: \"flex\",\n                  fontSize: \"12px\",\n                  alignItems: \"center\",\n                  marginRight: \"4px\"\n                },\n                dangerouslySetInnerHTML: {\n                  __html: warning\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 10\n              }, this), translate(\"Value must be between 300px and 1200px.\")]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 7\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                className: \"qadpt-control-label\",\n                children: translate(\"Padding\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  variant: \"outlined\",\n                  value: `${Annpadding}`,\n                  fullWidth: true,\n                  size: \"small\",\n                  className: \"qadpt-control-input\",\n                  onChange: e => {\n                    const inputValue = parseInt(e.target.value) || 0;\n\n                    // Validate padding between 0px and 20px\n                    if (inputValue < 0 || inputValue > 20) {\n                      setPaddingError(true);\n                    } else {\n                      setPaddingError(false);\n                    }\n                    setAnnPadding(inputValue.toString());\n                  },\n                  InputProps: {\n                    endAdornment: \"px\",\n                    sx: {\n                      \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"& fieldset\": {\n                        border: \"none\"\n                      }\n                    }\n                  },\n                  error: paddingError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 375,\n                  columnNumber: 7\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 6\n            }, this), paddingError && /*#__PURE__*/_jsxDEV(Typography, {\n              style: {\n                fontSize: \"12px\",\n                color: \"#e9a971\",\n                textAlign: \"left\",\n                top: \"100%\",\n                left: 0,\n                marginBottom: \"5px\",\n                display: \"flex\",\n                alignItems: centercenter\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  display: \"flex\",\n                  fontSize: \"12px\",\n                  alignItems: \"center\",\n                  marginRight: \"4px\"\n                },\n                dangerouslySetInnerHTML: {\n                  __html: warning\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 9\n              }, this), translate(\"Value must be between 0px and 20px.\")]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 7\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                className: \"qadpt-control-label\",\n                children: translate(\"Border Radius\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  variant: \"outlined\",\n                  value: `${borderRadius}`,\n                  fullWidth: true,\n                  size: \"small\",\n                  className: \"qadpt-control-input\",\n                  onChange: e => {\n                    const inputValue = parseInt(e.target.value) || 0;\n\n                    // Validate border radius between 0px and 20px\n                    if (inputValue < 0 || inputValue > 20) {\n                      setBorderRadiusError(true);\n                      // Set border radius to closest valid value\n                      // setBorderRadius(inputValue < 0 ? 0 : 20);\n                    } else {\n                      setBorderRadiusError(false);\n                    }\n                    setBorderRadius(inputValue);\n                  },\n                  InputProps: {\n                    endAdornment: \"px\",\n                    sx: {\n                      \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"& fieldset\": {\n                        border: \"none\"\n                      }\n                    }\n                  },\n                  error: borderRadiusError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 432,\n                  columnNumber: 7\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 6\n            }, this), borderRadiusError && /*#__PURE__*/_jsxDEV(Typography, {\n              style: {\n                fontSize: \"12px\",\n                color: \"#e9a971\",\n                textAlign: \"left\",\n                top: \"100%\",\n                left: 0,\n                marginBottom: \"5px\",\n                display: \"flex\",\n                alignItems: centercenter\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  display: \"flex\",\n                  fontSize: \"12px\",\n                  alignItems: \"center\",\n                  marginRight: \"4px\"\n                },\n                dangerouslySetInnerHTML: {\n                  __html: warning\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 9\n              }, this), translate(\"Value must be between 0px and 20px.\")]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 7\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                className: \"qadpt-control-label\",\n                children: translate(\"Border Size\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  variant: \"outlined\",\n                  value: `${AnnborderSize}`,\n                  fullWidth: true,\n                  size: \"small\",\n                  className: \"qadpt-control-input\",\n                  onChange: e => {\n                    const inputValue = parseInt(e.target.value) || 0;\n\n                    // Validate border size between 0px and 10px\n                    if (inputValue < 0 || inputValue > 10) {\n                      setBorderSizeError(true);\n                    } else {\n                      setBorderSizeError(false);\n                    }\n                    setAnnBorderSize(inputValue);\n                  },\n                  InputProps: {\n                    endAdornment: \"px\",\n                    sx: {\n                      \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"& fieldset\": {\n                        border: \"none\"\n                      }\n                    }\n                  },\n                  error: borderSizeError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 490,\n                  columnNumber: 7\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 489,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 6\n            }, this), borderSizeError && /*#__PURE__*/_jsxDEV(Typography, {\n              style: {\n                fontSize: \"12px\",\n                color: \"#e9a971\",\n                textAlign: \"left\",\n                top: \"100%\",\n                left: 0,\n                marginBottom: \"5px\",\n                display: \"flex\",\n                alignItems: centercenter\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  display: \"flex\",\n                  fontSize: \"12px\",\n                  alignItems: \"center\",\n                  marginRight: \"4px\"\n                },\n                dangerouslySetInnerHTML: {\n                  __html: warning\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 535,\n                columnNumber: 9\n              }, this), translate(\"Value must be between 0px and 10px.\")]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 7\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                className: \"qadpt-control-label\",\n                children: translate(\"Border\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"color\",\n                  value: borderColor,\n                  onChange: handleBorderColorChange,\n                  className: \"qadpt-color-input\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 562,\n                  columnNumber: 7\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 6\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                className: \"qadpt-control-label\",\n                children: translate(\"Background\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 573,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"color\",\n                  value: backgroundColor,\n                  onChange: handleBackgroundColorChange,\n                  className: \"qadpt-color-input\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 575,\n                  columnNumber: 7\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 574,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 6\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 5\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 5\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-drawerFooter\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            onClick: handleApplyChanges,\n            className: `qadpt-btn ${widthError || paddingError || borderRadiusError || borderSizeError ? \"disabled\" : \"\"}`,\n            disabled: widthError || paddingError || borderRadiusError || borderSizeError,\n            children: translate(\"Apply\")\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 588,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 587,\n          columnNumber: 5\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 4\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 3\n    }, this)\n    //</Draggable>\n  );\n};\n_s(CanvasSettings, \"S96JeDzgXWUZWU2ZM0znnim67FM=\", false, function () {\n  return [useDrawerStore, useTranslation];\n});\n_c = CanvasSettings;\nexport default CanvasSettings;\nvar _c;\n$RefreshReg$(_c, \"CanvasSettings\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Box", "Typography", "TextField", "Grid", "IconButton", "<PERSON><PERSON>", "CloseIcon", "ArrowBackIosNewOutlinedIcon", "useDrawerStore", "defaultDots", "topLeft", "topRight", "middleLeft", "middleCenter", "middleRight", "bottomLeft", "bottomMiddle", "bottomRight", "topcenter", "warning", "centercenter", "useTranslation", "jsxDEV", "_jsxDEV", "CanvasSettings", "zindeex", "setZindeex", "setShowCanvasSettings", "selectedTemplate", "_s", "setCanvasSetting", "borderColor", "announcement<PERSON><PERSON>", "width", "<PERSON><PERSON><PERSON><PERSON>", "backgroundColor", "setBorderColor", "setBackgroundColor", "borderRadius", "setBorderRadius", "Annpadding", "setAnnPadding", "AnnborderSize", "setAnnBorderSize", "Bposition", "setBposition", "setIsUnSavedChanges", "setIsThemeChanges", "currentStep", "syncAIAnnouncementCanvasSettings", "createWithAI", "selectedTemplateTour", "selectedTheme", "state", "t", "translate", "isOpen", "setIsOpen", "selectedPosition", "setSelectedPosition", "widthError", "setWidthError", "paddingError", "setPaddingError", "borderRadiusError", "setBorderRadiusError", "borderSizeError", "setBorderSizeError", "positions", "label", "icon", "dangerouslySetInnerHTML", "__html", "style", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "renderPositionIcon", "positionValue", "isSelected", "_announcementJson$Gui", "_announcementJson$Gui2", "_announcementJson$Gui3", "currentStepIndex", "GuideStep", "findIndex", "step", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "toLowerCase", "startsWith", "parseInt", "replace", "String", "canvasData", "<PERSON><PERSON>", "Position", "<PERSON><PERSON><PERSON>", "Padding", "<PERSON><PERSON>", "BorderSize", "BorderColor", "BackgroundColor", "initialWidth", "initialPadding", "initialBorderRadius", "initialBorderSize", "_canvasData$Radius", "paddingValue", "handlePositionClick", "handleBorderColorChange", "e", "target", "handleBackgroundColorChange", "handleClose", "handleApplyChanges", "_selectedTheme$ThemeS", "canvas", "ThemeStyles", "undefined", "Zindex", "setTimeout", "id", "className", "children", "onClick", "size", "container", "spacing", "map", "position", "item", "xs", "disable<PERSON><PERSON><PERSON>", "variant", "autoFocus", "onChange", "inputValue", "InputProps", "endAdornment", "sx", "border", "error", "color", "textAlign", "top", "left", "marginBottom", "display", "alignItems", "marginRight", "fullWidth", "toString", "type", "disabled", "_c", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/guideDesign/CanvasSettings.tsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { Box, Typography, TextField, Grid, IconButton, Button, Tooltip } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport RadioButtonUncheckedIcon from \"@mui/icons-material/RadioButtonUnchecked\";\r\nimport RadioButtonCheckedIcon from \"@mui/icons-material/RadioButtonChecked\";\r\n// import Draggable from \"react-draggable\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport \"./Canvas.module.css\";\r\nimport useDrawerStore from \"../../store/drawerStore\";\r\nimport { defaultDots,topLeft,topCenter,topRight,middleLeft,middleCenter,middleRight,bottomLeft,bottomMiddle,bottomRight, topcenter, warning, centercenter } from \"../../assets/icons/icons\";\r\nimport { useTranslation } from \"react-i18next\";\r\nconst CanvasSettings = ({ zindeex, setZindeex, setShowCanvasSettings, selectedTemplate }: any) => {\r\n\tconst {\r\n\t\tsetCanvasSetting,\r\n\t\tborderColor,\r\n\t\tannouncementJson,\r\n\t\twidth,\r\n\t\tsetWidth,\r\n\t\tbackgroundColor,\r\n\t\tsetBorderColor,\r\n\t\tsetBackgroundColor,\r\n\t\tborderRadius,\r\n\t\tsetBorderRadius,\r\n\t\tAnnpadding,\r\n\t\tsetAnnPadding,\r\n\t\tAnnborderSize,\r\n\t\tsetAnnBorderSize,\r\n\t\tBposition,\r\n\t\tsetBposition,\r\n\t\tsetIsUnSavedChanges,\r\n\t\tsetIsThemeChanges,\r\n\t\tcurrentStep,\r\n\t\tsyncAIAnnouncementCanvasSettings,\r\n\t\tcreateWithAI,\r\n\t\t//selectedTemplate,\r\n\t\tselectedTemplateTour,\r\n\t\tselectedTheme\r\n\t} = useDrawerStore((state: any) => state);\r\n\tconst { t: translate } = useTranslation();\r\n\tconst [isOpen, setIsOpen] = useState(true);\r\n\tconst [selectedPosition, setSelectedPosition] = useState(\"middle-center\");\r\n\tconst [widthError, setWidthError] = useState(false);\r\n\tconst [paddingError, setPaddingError] = useState(false);\r\n\tconst [borderRadiusError, setBorderRadiusError] = useState(false);\r\n\tconst [borderSizeError, setBorderSizeError] = useState(false);\r\n\tconst positions = [\r\n\t\t{ label: translate(\"Top Left\"), icon: <span dangerouslySetInnerHTML={{ __html: topLeft }} style={{ fontSize: \"small\" }} />, value: \"top-left\" },\r\n\t\t{ label: translate(\"Top Center\"), icon: <span dangerouslySetInnerHTML={{ __html: topcenter }} style={{ fontSize: \"small\" }} />, value: \"top-center\" },\r\n\t\t{ label: translate(\"Top Right\"), icon: <span dangerouslySetInnerHTML={{ __html: topRight }} style={{ fontSize: \"small\" }} />, value: \"top-right\" },\r\n\t\t{ label: translate(\"Middle Left\"), icon: <span dangerouslySetInnerHTML={{ __html: middleLeft }} style={{ fontSize: \"small\" }} />, value: \"left-center\" },\r\n\t\t{ label: translate(\"Middle Center\"), icon: <span dangerouslySetInnerHTML={{ __html: middleCenter }} style={{ fontSize: \"small\" }} />, value: \"center-center\" },\r\n\t\t{ label: translate(\"Middle Right\"), icon: <span dangerouslySetInnerHTML={{ __html: middleRight }} style={{ fontSize: \"small\" }} />, value: \"right-center\" },\r\n\t\t{ label: translate(\"Bottom Left\"), icon: <span dangerouslySetInnerHTML={{ __html: bottomLeft }} style={{ fontSize: \"small\" }} />, value: \"bottom-left\" },\r\n\t\t{ label: translate(\"Bottom Center\"), icon: <span dangerouslySetInnerHTML={{ __html: bottomMiddle }} style={{ fontSize: \"small\" }} />, value: \"bottom-center\" },\r\n\t\t{ label: translate(\"Bottom Right\"), icon: <span dangerouslySetInnerHTML={{ __html: bottomRight }} style={{ fontSize: \"small\" }} />, value: \"bottom-right\" },\r\n\t];\r\n\tconst renderPositionIcon = (positionValue:any) => {\r\n\t\tconst isSelected = Bposition === positionValue;\r\n\r\n\t\tif (!isSelected) {\r\n\t\t  return <span dangerouslySetInnerHTML={{ __html: defaultDots }} style={{ fontSize: \"small\" }} />;\r\n\t\t}\r\n\r\n\t\tswitch (positionValue) {\r\n\t\t  case \"top-left\":\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: topLeft }} style={{ fontSize: \"small\" }} />;\r\n\t\t  case \"top-center\":\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: topcenter }} style={{ fontSize: \"small\" }} />;\r\n\t\t  case \"top-right\":\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: topRight }} style={{ fontSize: \"small\" }} />;\r\n\t\t  case \"left-center\":\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: middleLeft }} style={{ fontSize: \"small\" }} />;\r\n\t\t  case \"center-center\":\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: middleCenter }} style={{ fontSize: \"small\" }} />;\r\n\t\t  case \"right-center\":\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: middleRight }} style={{ fontSize: \"small\" }} />;\r\n\t\t  case \"bottom-left\":\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: bottomLeft }} style={{ fontSize: \"small\" }} />;\r\n\t\t  case \"bottom-center\":\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: bottomMiddle }} style={{ fontSize: \"small\" }} />;\r\n\t\t  case \"bottom-right\":\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: bottomRight }} style={{ fontSize: \"small\" }} />;\r\n\t\t  default:\r\n\t\t\treturn <span dangerouslySetInnerHTML={{ __html: defaultDots }} style={{ fontSize: \"small\" }} />;\r\n\t\t}\r\n\t  };\r\n\r\n\tuseEffect(() => {\r\n\t\tconst currentStepIndex = announcementJson?.GuideStep?.findIndex(\r\n\t\t\t(step: any) => {\r\n\t\t\t\tlet stepNum = step.stepName;// \r\n\t\t\t\tif (typeof stepNum === \"string\" && stepNum.toLowerCase().startsWith(\"step\")) {\r\n\t\t\t\t\tstepNum = parseInt(stepNum.replace(/[^0-9]/g, \"\"), 10);\r\n\t\t\t\t}\r\n\t\t\t\treturn String(stepNum) === String(currentStep); \r\n\t\t\t}\r\n\t\t);\r\n\r\n\t\t// Get canvas data for the current step (if found), otherwise use the first step\r\n\t\tconst canvasData = (currentStepIndex !== -1)\r\n\t\t\t? announcementJson?.GuideStep?.[currentStepIndex]?.Canvas\r\n\t\t\t:  {\r\n\t\t\t\t\t\t\t\t\tPosition: \"center-center\",\r\n\t\t\t\t\t\t\t\t\tWidth:500,\r\n\t\t\t\t\t\t\t\t\tPadding: \"12\",\r\n\t\t\t\t\t\t\t\t\tRadius: 8,\r\n\t\t\t\t\t\t\t\t\tBorderSize: 0,\r\n\t\t\t\t\t\t\t\t\tBorderColor: \"#000000\",\r\n\t\t\t\t\t\t\t\t\tBackgroundColor: \"#ffffff\"\r\n\t\t\t\t\t\t\t\t};;\r\n\r\n\t\tlet initialWidth;\r\n\t\tlet initialPadding;\r\n\t\tlet initialBorderRadius;\r\n\t\tlet initialBorderSize;\r\n\r\n\t\tif (canvasData) {\r\n\t\t\tsetSelectedPosition(canvasData.Position || \"center-center\");\r\n\t\t\tsetBposition(canvasData.Position || \"center-center\");\r\n\t\t\tinitialWidth = canvasData.Width || 500;\r\n\t\t\tsetBackgroundColor(canvasData.BackgroundColor || \"#ffffff\");\r\n\t\t\tinitialBorderRadius = canvasData.Radius ?? 8;\r\n\t\t\tinitialPadding = canvasData.Padding || \"12\";\r\n\t\t\tsetBorderColor(canvasData.BorderColor || \"#000000\");\r\n\t\t\tinitialBorderSize = canvasData.BorderSize || 0;\r\n\t\t} else {\r\n\t\t\tsetSelectedPosition(\"center-center\");\r\n\t\t\tsetBposition(\"center-center\");\r\n\t\t\tinitialWidth = 500;\r\n\t\t\tsetBackgroundColor(\"#ffffff\");\r\n\t\t\tinitialBorderRadius = 8;\r\n\t\t\tinitialPadding = \"12\";\r\n\t\t\tsetBorderColor(\"#000000\");\r\n\t\t\tinitialBorderSize = 0;\r\n\t\t}\r\n\r\n\t\t// Validate initial width\r\n\t\tif (initialWidth < 300 || initialWidth > 1200) {\r\n\t\t\tsetWidthError(true);\r\n\t\t\t// Set width to closest valid value\r\n\t\t\tsetWidth(initialWidth < 300 ? 300 : 1200);\r\n\t\t} else {\r\n\t\t\tsetWidthError(false);\r\n\t\t\tsetWidth(initialWidth);\r\n\t\t}\r\n\r\n\t\t// Validate initial padding\r\n\t\tconst paddingValue = parseInt(initialPadding) || 12;\r\n\t\tif (paddingValue < 0 || paddingValue > 20) {\r\n\t\t\tsetPaddingError(true);\r\n\t\t\t// Set padding to closest valid value\r\n\t\t\tsetAnnPadding(paddingValue < 0 ? \"0\" : \"20\");\r\n\t\t} else {\r\n\t\t\tsetPaddingError(false);\r\n\t\t\tsetAnnPadding(initialPadding);\r\n\t\t}\r\n\r\n\t\t// Validate initial border radius\r\n\t\tif (initialBorderRadius < 0 || initialBorderRadius > 20) {\r\n\t\t\tsetBorderRadiusError(true);\r\n\t\t\t// Set border radius to closest valid value\r\n\t\t\tsetBorderRadius(initialBorderRadius < 0 ? 0 : 20);\r\n\t\t} else {\r\n\t\t\tsetBorderRadiusError(false);\r\n\t\t\tsetBorderRadius(initialBorderRadius);\r\n\t\t}\r\n\r\n\t\t// Validate initial border size\r\n\t\tif (initialBorderSize < 0 || initialBorderSize > 10) {\r\n\t\t\tsetBorderSizeError(true);\r\n\t\t\t// Set border size to closest valid value\r\n\t\t\tsetAnnBorderSize(initialBorderSize < 0 ? 0 : 10);\r\n\t\t} else {\r\n\t\t\tsetBorderSizeError(false);\r\n\t\t\tsetAnnBorderSize(initialBorderSize);\r\n\t\t}\r\n\t}, [announcementJson, currentStep, setBposition, setWidth, setBackgroundColor, setBorderRadius, setAnnPadding, setBorderColor, setAnnBorderSize]);\r\n\t// useEffect(() => {\r\n\t// \tconst canvas = selectedTheme?.ThemeStyles?.Canvas;\r\n\t// \tconsole.log(\"Canvas use effect\");\r\n\t// \tif (canvas) {\r\n\t// \t  setBackgroundColor(canvas.canvasBgColor || \"#ffffff\");\r\n\t// \t  setBorderColor(canvas.canvasBorderColor || \"#000000\");\r\n\t// \t  setBorderRadius(canvas.canvasRadius ?? 8);\r\n\t// \t  setAnnPadding(canvas.canvasPadding?.toString() || \"12\");\r\n\t// \t  setAnnBorderSize(canvas.canvasBorderSize ?? 2);\r\n\t// \t  setWidth(500); // optional: set if needed\r\n\t// \t}\r\n\t// \thandleApplyChanges();\r\n\t//   }, [selectedTheme]);\r\n\tconst handlePositionClick = (positionValue: string) => {\r\n\t\tsetSelectedPosition(positionValue);\r\n\t\tsetBposition(positionValue);\r\n\t};\r\n\tconst handleBorderColorChange = (e: any) => setBorderColor(e.target.value);\r\n\tconst handleBackgroundColorChange = (e: any) => setBackgroundColor(e.target.value);\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetIsOpen(false);\r\n\t\tsetShowCanvasSettings(false);\r\n\t};\r\n\tconst handleApplyChanges = () => {\r\n\t\tconst canvas = selectedTheme?.ThemeStyles?.Canvas;\r\n\t\t// Don't apply changes if there's any validation error\r\n\t\tif (widthError || paddingError || borderRadiusError || borderSizeError) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tconst canvasData = {\r\n\t\t\tPosition: selectedPosition,\r\n\t\t\tBackgroundColor: backgroundColor,\r\n\t\t\tWidth: width || 500,\r\n\t\t\tRadius:  borderRadius !== undefined ? borderRadius : 0,\r\n\t\t\tPadding:  Annpadding || \"12\",\r\n\t\t\tBorderColor: borderColor,\r\n\t\t\tBorderSize: AnnborderSize || 0,\r\n\t\t\tZindex: 9999,\r\n\t\t};\r\n\r\n\t\tsetCanvasSetting(canvasData);\r\n\r\n\t\t// Sync AI announcement canvas settings after applying changes\r\n\t\tif (createWithAI && (selectedTemplate === \"Announcement\" || selectedTemplateTour === \"Announcement\")) {\r\n\t\t\t// Use setTimeout to ensure setCanvasSetting completes first\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tsyncAIAnnouncementCanvasSettings(canvasData);\r\n\t\t\t}, 0);\r\n\t\t}\r\n\r\n\t\tsetBposition(selectedPosition);\r\n\t\thandleClose();\r\n\t\tsetIsUnSavedChanges(true);\r\n\t\tsetIsThemeChanges(true);\r\n\t};\r\n\r\n\tif (!isOpen) return null;\r\n\r\n\treturn (\r\n\t\t//<Draggable>\r\n\t\t<div\r\n\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\tclassName=\"qadpt-designpopup\"\r\n\t\t>\r\n\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<ArrowBackIosNewOutlinedIcon  className=\"qadpt-design-back\"/>\r\n\t\t\t\t\t\t{/* Header */}\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t<div className=\"qadpt-title\">{translate(\"Canvas\")}</div>\r\n\t\t\t\t\t{/* Close Button */}\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<CloseIcon className=\"qadpt-design-close\"/>\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t{/* Position Grid */}\r\n\t\t\t\t<div className=\"qadpt-canblock\">\r\n\t\t\t\t<div className=\"qadpt-controls qadpt-errmsg\">\r\n\t\t\t\t<Box className=\"qadpt-position-grid\">\r\n\t\t\t\t\t\t\t<Typography className=\"qadpt-ctrl-title\">{translate(\"Position\")}</Typography>\r\n\t\t\t\t\t<Grid\r\n\t\t\t\t\t\tid=\"pos-container\"\r\n\t\t\t\t\t\tcontainer\r\n\t\t\t\t\t\tspacing={1}\r\n\t\t\t\t\t\t//onClick={handlePositionClick}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{/* Position Icons */}\r\n\t\t\t\t\t\t{positions.map((position) => (\r\n\t\t\t\t\t\t\t<Grid item xs={4} key={position.value}>\r\n\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\t\t\tdisableRipple\r\n\t\t\t\t\t\t\t\t\t// sx={{\r\n\t\t\t\t\t\t\t\t\t//     backgroundColor: Bposition === position.value ? \"var(--border-color)\" : \"transparent\",\r\n\t\t\t\t\t\t\t\t\t// }}\r\n\t\t\t\t\t\t\t\t\tonClick={() => handlePositionClick(position.value)} // Pass value directly\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{renderPositionIcon(position.value)}\r\n\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t</Grid>\r\n\t\t\t\t\t\t))}\r\n\t\t\t\t\t</Grid>\r\n\t\t\t\t</Box>\r\n\r\n\r\n\t\t\t\t\t{/* Width Control */}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate(\"Width\")}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={`${width}`}\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tautoFocus\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\tconst inputValue = parseInt(e.target.value) || 0;\r\n\r\n\t\t\t\t\t\t\t\t// Validate width between 300px and 1200px\r\n\t\t\t\t\t\t\t\tif (inputValue < 300 || inputValue > 1200) {\r\n\t\t\t\t\t\t\t\t\tsetWidthError(true);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tsetWidthError(false);\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tsetWidth(inputValue);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\terror={widthError}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t{widthError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\talignItems:centercenter\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t><span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\r\n\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate(\"Value must be between 300px and 1200px.\")}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t)}\r\n\r\n\r\n\t\t\t\t\t{/* Height Control */}\r\n\t\t\t\t\t{/* <Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">Height</Typography>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={`${height}`}\r\n\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\tmargin=\"dense\"\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\tsetHeight(parseInt(e.target.value) || 0);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Box> */}\r\n\r\n\t\t\t\t\t{/* Padding Control */}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Padding\")}</Typography>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={`${Annpadding}`}\r\n\t\t\t\t\t\t\tfullWidth\r\n\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\tconst inputValue = parseInt(e.target.value) || 0;\r\n\r\n\t\t\t\t\t\t\t\t// Validate padding between 0px and 20px\r\n\t\t\t\t\t\t\t\tif (inputValue < 0 || inputValue > 20) {\r\n\t\t\t\t\t\t\t\t\tsetPaddingError(true);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tsetPaddingError(false);\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tsetAnnPadding(inputValue.toString());\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\terror={paddingError}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t{paddingError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\talignItems:centercenter\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t><span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\r\n\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate(\"Value must be between 0px and 20px.\")}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t{/* Border Radius Control */}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Border Radius\")}</Typography>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={`${borderRadius}`}\r\n\t\t\t\t\t\t\tfullWidth\r\n\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\tconst inputValue = parseInt(e.target.value) || 0;\r\n\r\n\t\t\t\t\t\t\t\t// Validate border radius between 0px and 20px\r\n\t\t\t\t\t\t\t\tif (inputValue < 0 || inputValue > 20) {\r\n\t\t\t\t\t\t\t\t\tsetBorderRadiusError(true);\r\n\t\t\t\t\t\t\t\t\t// Set border radius to closest valid value\r\n\t\t\t\t\t\t\t\t\t// setBorderRadius(inputValue < 0 ? 0 : 20);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tsetBorderRadiusError(false);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\tsetBorderRadius(inputValue);\r\n\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\terror={borderRadiusError}\r\n\t\t\t\t\t\t\t/>\r\n\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t{borderRadiusError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\talignItems:centercenter\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t><span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\r\n\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate(\"Value must be between 0px and 20px.\")}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t)}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Border Size\")}</Typography>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={`${AnnborderSize}`}\r\n\t\t\t\t\t\t\tfullWidth\r\n\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\tconst inputValue = parseInt(e.target.value) || 0;\r\n\r\n\t\t\t\t\t\t\t\t// Validate border size between 0px and 10px\r\n\t\t\t\t\t\t\t\tif (inputValue < 0 || inputValue > 10) {\r\n\t\t\t\t\t\t\t\t\tsetBorderSizeError(true);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tsetBorderSizeError(false);\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tsetAnnBorderSize(inputValue);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\terror={borderSizeError}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t{borderSizeError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\talignItems:centercenter\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t><span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\r\n\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate(\"Value must be between 0px and 10px.\")}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t)}\r\n\t\t\t\t\t{/* Zindex value Control */}\r\n\t\t\t\t\t{/* <Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">Z-Index</Typography>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={`${zindeex}`}\r\n\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\tmargin=\"dense\"\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\tsetZindeex(parseInt(e.target.value) || 0);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Box> */}\r\n\r\n\t\t\t\t\t{/* Border Color Control */}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Border\")}</Typography>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\tvalue={borderColor}\r\n\t\t\t\t\t\t\tonChange={handleBorderColorChange}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t{/* Background Color Control */}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<Typography className=\"qadpt-control-label\">{translate(\"Background\")}</Typography>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\tvalue={backgroundColor}\r\n\t\t\t\t\t\t\tonChange={handleBackgroundColorChange}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Box>\r\n\r\n\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\tonClick={handleApplyChanges}\r\n\t\t\t\t\t\t\tclassName={`qadpt-btn ${widthError || paddingError || borderRadiusError || borderSizeError ? \"disabled\" : \"\"}`}\r\n\t\t\t\t\t\t\tdisabled={widthError || paddingError || borderRadiusError || borderSizeError}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t{translate(\"Apply\")}\r\n\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t</div>\r\n\t\t\t</div>\r\n\r\n\t\t</div>\r\n\t\t//</Draggable>\r\n\t);\r\n};\r\n\r\nexport default CanvasSettings;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,UAAU,EAAEC,SAAS,EAAEC,IAAI,EAAEC,UAAU,EAAEC,MAAM,QAAiB,eAAe;AAC7F,OAAOC,SAAS,MAAM,2BAA2B;AAGjD;AACA,OAAOC,2BAA2B,MAAM,6CAA6C;AACrF,OAAO,qBAAqB;AAC5B,OAAOC,cAAc,MAAM,yBAAyB;AACpD,SAASC,WAAW,EAACC,OAAO,EAAWC,QAAQ,EAACC,UAAU,EAACC,YAAY,EAACC,WAAW,EAACC,UAAU,EAACC,YAAY,EAACC,WAAW,EAAEC,SAAS,EAAEC,OAAO,EAAEC,YAAY,QAAQ,0BAA0B;AAC3L,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAC/C,MAAMC,cAAc,GAAGA,CAAC;EAAEC,OAAO;EAAEC,UAAU;EAAEC,qBAAqB;EAAEC;AAAsB,CAAC,KAAK;EAAAC,EAAA;EACjG,MAAM;IACLC,gBAAgB;IAChBC,WAAW;IACXC,gBAAgB;IAChBC,KAAK;IACLC,QAAQ;IACRC,eAAe;IACfC,cAAc;IACdC,kBAAkB;IAClBC,YAAY;IACZC,eAAe;IACfC,UAAU;IACVC,aAAa;IACbC,aAAa;IACbC,gBAAgB;IAChBC,SAAS;IACTC,YAAY;IACZC,mBAAmB;IACnBC,iBAAiB;IACjBC,WAAW;IACXC,gCAAgC;IAChCC,YAAY;IACZ;IACAC,oBAAoB;IACpBC;EACD,CAAC,GAAG5C,cAAc,CAAE6C,KAAU,IAAKA,KAAK,CAAC;EACzC,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGlC,cAAc,CAAC,CAAC;EACzC,MAAM,CAACmC,MAAM,EAAEC,SAAS,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAAC2D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5D,QAAQ,CAAC,eAAe,CAAC;EACzE,MAAM,CAAC6D,UAAU,EAAEC,aAAa,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC+D,YAAY,EAAEC,eAAe,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACiE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACmE,eAAe,EAAEC,kBAAkB,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAMqE,SAAS,GAAG,CACjB;IAAEC,KAAK,EAAEd,SAAS,CAAC,UAAU,CAAC;IAAEe,IAAI,eAAE/C,OAAA;MAAMgD,uBAAuB,EAAE;QAAEC,MAAM,EAAE9D;MAAQ,CAAE;MAAC+D,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAW,CAAC,EAC/I;IAAEV,KAAK,EAAEd,SAAS,CAAC,YAAY,CAAC;IAAEe,IAAI,eAAE/C,OAAA;MAAMgD,uBAAuB,EAAE;QAAEC,MAAM,EAAEtD;MAAU,CAAE;MAACuD,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAa,CAAC,EACrJ;IAAEV,KAAK,EAAEd,SAAS,CAAC,WAAW,CAAC;IAAEe,IAAI,eAAE/C,OAAA;MAAMgD,uBAAuB,EAAE;QAAEC,MAAM,EAAE7D;MAAS,CAAE;MAAC8D,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAY,CAAC,EAClJ;IAAEV,KAAK,EAAEd,SAAS,CAAC,aAAa,CAAC;IAAEe,IAAI,eAAE/C,OAAA;MAAMgD,uBAAuB,EAAE;QAAEC,MAAM,EAAE5D;MAAW,CAAE;MAAC6D,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAc,CAAC,EACxJ;IAAEV,KAAK,EAAEd,SAAS,CAAC,eAAe,CAAC;IAAEe,IAAI,eAAE/C,OAAA;MAAMgD,uBAAuB,EAAE;QAAEC,MAAM,EAAE3D;MAAa,CAAE;MAAC4D,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAgB,CAAC,EAC9J;IAAEV,KAAK,EAAEd,SAAS,CAAC,cAAc,CAAC;IAAEe,IAAI,eAAE/C,OAAA;MAAMgD,uBAAuB,EAAE;QAAEC,MAAM,EAAE1D;MAAY,CAAE;MAAC2D,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAe,CAAC,EAC3J;IAAEV,KAAK,EAAEd,SAAS,CAAC,aAAa,CAAC;IAAEe,IAAI,eAAE/C,OAAA;MAAMgD,uBAAuB,EAAE;QAAEC,MAAM,EAAEzD;MAAW,CAAE;MAAC0D,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAc,CAAC,EACxJ;IAAEV,KAAK,EAAEd,SAAS,CAAC,eAAe,CAAC;IAAEe,IAAI,eAAE/C,OAAA;MAAMgD,uBAAuB,EAAE;QAAEC,MAAM,EAAExD;MAAa,CAAE;MAACyD,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAgB,CAAC,EAC9J;IAAEV,KAAK,EAAEd,SAAS,CAAC,cAAc,CAAC;IAAEe,IAAI,eAAE/C,OAAA;MAAMgD,uBAAuB,EAAE;QAAEC,MAAM,EAAEvD;MAAY,CAAE;MAACwD,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAQ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAAEC,KAAK,EAAE;EAAe,CAAC,CAC3J;EACD,MAAMC,kBAAkB,GAAIC,aAAiB,IAAK;IACjD,MAAMC,UAAU,GAAGtC,SAAS,KAAKqC,aAAa;IAE9C,IAAI,CAACC,UAAU,EAAE;MACf,oBAAO3D,OAAA;QAAMgD,uBAAuB,EAAE;UAAEC,MAAM,EAAE/D;QAAY,CAAE;QAACgE,KAAK,EAAE;UAAEC,QAAQ,EAAE;QAAQ;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IACjG;IAEA,QAAQG,aAAa;MACnB,KAAK,UAAU;QAChB,oBAAO1D,OAAA;UAAMgD,uBAAuB,EAAE;YAAEC,MAAM,EAAE9D;UAAQ,CAAE;UAAC+D,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC1F,KAAK,YAAY;QAClB,oBAAOvD,OAAA;UAAMgD,uBAAuB,EAAE;YAAEC,MAAM,EAAEtD;UAAU,CAAE;UAACuD,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC5F,KAAK,WAAW;QACjB,oBAAOvD,OAAA;UAAMgD,uBAAuB,EAAE;YAAEC,MAAM,EAAE7D;UAAS,CAAE;UAAC8D,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC3F,KAAK,aAAa;QACnB,oBAAOvD,OAAA;UAAMgD,uBAAuB,EAAE;YAAEC,MAAM,EAAE5D;UAAW,CAAE;UAAC6D,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7F,KAAK,eAAe;QACrB,oBAAOvD,OAAA;UAAMgD,uBAAuB,EAAE;YAAEC,MAAM,EAAE3D;UAAa,CAAE;UAAC4D,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/F,KAAK,cAAc;QACpB,oBAAOvD,OAAA;UAAMgD,uBAAuB,EAAE;YAAEC,MAAM,EAAE1D;UAAY,CAAE;UAAC2D,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC9F,KAAK,aAAa;QACnB,oBAAOvD,OAAA;UAAMgD,uBAAuB,EAAE;YAAEC,MAAM,EAAEzD;UAAW,CAAE;UAAC0D,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7F,KAAK,eAAe;QACrB,oBAAOvD,OAAA;UAAMgD,uBAAuB,EAAE;YAAEC,MAAM,EAAExD;UAAa,CAAE;UAACyD,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC/F,KAAK,cAAc;QACpB,oBAAOvD,OAAA;UAAMgD,uBAAuB,EAAE;YAAEC,MAAM,EAAEvD;UAAY,CAAE;UAACwD,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC9F;QACD,oBAAOvD,OAAA;UAAMgD,uBAAuB,EAAE;YAAEC,MAAM,EAAE/D;UAAY,CAAE;UAACgE,KAAK,EAAE;YAAEC,QAAQ,EAAE;UAAQ;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAChG;EACC,CAAC;EAEHhF,SAAS,CAAC,MAAM;IAAA,IAAAqF,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACf,MAAMC,gBAAgB,GAAGtD,gBAAgB,aAAhBA,gBAAgB,wBAAAmD,qBAAA,GAAhBnD,gBAAgB,CAAEuD,SAAS,cAAAJ,qBAAA,uBAA3BA,qBAAA,CAA6BK,SAAS,CAC7DC,IAAS,IAAK;MACd,IAAIC,OAAO,GAAGD,IAAI,CAACE,QAAQ,CAAC;MAC5B,IAAI,OAAOD,OAAO,KAAK,QAAQ,IAAIA,OAAO,CAACE,WAAW,CAAC,CAAC,CAACC,UAAU,CAAC,MAAM,CAAC,EAAE;QAC5EH,OAAO,GAAGI,QAAQ,CAACJ,OAAO,CAACK,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;MACvD;MACA,OAAOC,MAAM,CAACN,OAAO,CAAC,KAAKM,MAAM,CAAChD,WAAW,CAAC;IAC/C,CACD,CAAC;;IAED;IACA,MAAMiD,UAAU,GAAIX,gBAAgB,KAAK,CAAC,CAAC,GACxCtD,gBAAgB,aAAhBA,gBAAgB,wBAAAoD,sBAAA,GAAhBpD,gBAAgB,CAAEuD,SAAS,cAAAH,sBAAA,wBAAAC,sBAAA,GAA3BD,sBAAA,CAA8BE,gBAAgB,CAAC,cAAAD,sBAAA,uBAA/CA,sBAAA,CAAiDa,MAAM,GACtD;MACGC,QAAQ,EAAE,eAAe;MACzBC,KAAK,EAAC,GAAG;MACTC,OAAO,EAAE,IAAI;MACbC,MAAM,EAAE,CAAC;MACTC,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE,SAAS;MACtBC,eAAe,EAAE;IAClB,CAAC;IAAC;IAER,IAAIC,YAAY;IAChB,IAAIC,cAAc;IAClB,IAAIC,mBAAmB;IACvB,IAAIC,iBAAiB;IAErB,IAAIZ,UAAU,EAAE;MAAA,IAAAa,kBAAA;MACfnD,mBAAmB,CAACsC,UAAU,CAACE,QAAQ,IAAI,eAAe,CAAC;MAC3DtD,YAAY,CAACoD,UAAU,CAACE,QAAQ,IAAI,eAAe,CAAC;MACpDO,YAAY,GAAGT,UAAU,CAACG,KAAK,IAAI,GAAG;MACtC/D,kBAAkB,CAAC4D,UAAU,CAACQ,eAAe,IAAI,SAAS,CAAC;MAC3DG,mBAAmB,IAAAE,kBAAA,GAAGb,UAAU,CAACK,MAAM,cAAAQ,kBAAA,cAAAA,kBAAA,GAAI,CAAC;MAC5CH,cAAc,GAAGV,UAAU,CAACI,OAAO,IAAI,IAAI;MAC3CjE,cAAc,CAAC6D,UAAU,CAACO,WAAW,IAAI,SAAS,CAAC;MACnDK,iBAAiB,GAAGZ,UAAU,CAACM,UAAU,IAAI,CAAC;IAC/C,CAAC,MAAM;MACN5C,mBAAmB,CAAC,eAAe,CAAC;MACpCd,YAAY,CAAC,eAAe,CAAC;MAC7B6D,YAAY,GAAG,GAAG;MAClBrE,kBAAkB,CAAC,SAAS,CAAC;MAC7BuE,mBAAmB,GAAG,CAAC;MACvBD,cAAc,GAAG,IAAI;MACrBvE,cAAc,CAAC,SAAS,CAAC;MACzByE,iBAAiB,GAAG,CAAC;IACtB;;IAEA;IACA,IAAIH,YAAY,GAAG,GAAG,IAAIA,YAAY,GAAG,IAAI,EAAE;MAC9C7C,aAAa,CAAC,IAAI,CAAC;MACnB;MACA3B,QAAQ,CAACwE,YAAY,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC;IAC1C,CAAC,MAAM;MACN7C,aAAa,CAAC,KAAK,CAAC;MACpB3B,QAAQ,CAACwE,YAAY,CAAC;IACvB;;IAEA;IACA,MAAMK,YAAY,GAAGjB,QAAQ,CAACa,cAAc,CAAC,IAAI,EAAE;IACnD,IAAII,YAAY,GAAG,CAAC,IAAIA,YAAY,GAAG,EAAE,EAAE;MAC1ChD,eAAe,CAAC,IAAI,CAAC;MACrB;MACAtB,aAAa,CAACsE,YAAY,GAAG,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC;IAC7C,CAAC,MAAM;MACNhD,eAAe,CAAC,KAAK,CAAC;MACtBtB,aAAa,CAACkE,cAAc,CAAC;IAC9B;;IAEA;IACA,IAAIC,mBAAmB,GAAG,CAAC,IAAIA,mBAAmB,GAAG,EAAE,EAAE;MACxD3C,oBAAoB,CAAC,IAAI,CAAC;MAC1B;MACA1B,eAAe,CAACqE,mBAAmB,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;IAClD,CAAC,MAAM;MACN3C,oBAAoB,CAAC,KAAK,CAAC;MAC3B1B,eAAe,CAACqE,mBAAmB,CAAC;IACrC;;IAEA;IACA,IAAIC,iBAAiB,GAAG,CAAC,IAAIA,iBAAiB,GAAG,EAAE,EAAE;MACpD1C,kBAAkB,CAAC,IAAI,CAAC;MACxB;MACAxB,gBAAgB,CAACkE,iBAAiB,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;IACjD,CAAC,MAAM;MACN1C,kBAAkB,CAAC,KAAK,CAAC;MACzBxB,gBAAgB,CAACkE,iBAAiB,CAAC;IACpC;EACD,CAAC,EAAE,CAAC7E,gBAAgB,EAAEgB,WAAW,EAAEH,YAAY,EAAEX,QAAQ,EAAEG,kBAAkB,EAAEE,eAAe,EAAEE,aAAa,EAAEL,cAAc,EAAEO,gBAAgB,CAAC,CAAC;EACjJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAMqE,mBAAmB,GAAI/B,aAAqB,IAAK;IACtDtB,mBAAmB,CAACsB,aAAa,CAAC;IAClCpC,YAAY,CAACoC,aAAa,CAAC;EAC5B,CAAC;EACD,MAAMgC,uBAAuB,GAAIC,CAAM,IAAK9E,cAAc,CAAC8E,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAC;EAC1E,MAAMqC,2BAA2B,GAAIF,CAAM,IAAK7E,kBAAkB,CAAC6E,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAC;EAElF,MAAMsC,WAAW,GAAGA,CAAA,KAAM;IACzB5D,SAAS,CAAC,KAAK,CAAC;IAChB9B,qBAAqB,CAAC,KAAK,CAAC;EAC7B,CAAC;EACD,MAAM2F,kBAAkB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAChC,MAAMC,MAAM,GAAGpE,aAAa,aAAbA,aAAa,wBAAAmE,qBAAA,GAAbnE,aAAa,CAAEqE,WAAW,cAAAF,qBAAA,uBAA1BA,qBAAA,CAA4BrB,MAAM;IACjD;IACA,IAAItC,UAAU,IAAIE,YAAY,IAAIE,iBAAiB,IAAIE,eAAe,EAAE;MACvE;IACD;IAEA,MAAM+B,UAAU,GAAG;MAClBE,QAAQ,EAAEzC,gBAAgB;MAC1B+C,eAAe,EAAEtE,eAAe;MAChCiE,KAAK,EAAEnE,KAAK,IAAI,GAAG;MACnBqE,MAAM,EAAGhE,YAAY,KAAKoF,SAAS,GAAGpF,YAAY,GAAG,CAAC;MACtD+D,OAAO,EAAG7D,UAAU,IAAI,IAAI;MAC5BgE,WAAW,EAAEzE,WAAW;MACxBwE,UAAU,EAAE7D,aAAa,IAAI,CAAC;MAC9BiF,MAAM,EAAE;IACT,CAAC;IAED7F,gBAAgB,CAACmE,UAAU,CAAC;;IAE5B;IACA,IAAI/C,YAAY,KAAKtB,gBAAgB,KAAK,cAAc,IAAIuB,oBAAoB,KAAK,cAAc,CAAC,EAAE;MACrG;MACAyE,UAAU,CAAC,MAAM;QAChB3E,gCAAgC,CAACgD,UAAU,CAAC;MAC7C,CAAC,EAAE,CAAC,CAAC;IACN;IAEApD,YAAY,CAACa,gBAAgB,CAAC;IAC9B2D,WAAW,CAAC,CAAC;IACbvE,mBAAmB,CAAC,IAAI,CAAC;IACzBC,iBAAiB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,IAAI,CAACS,MAAM,EAAE,OAAO,IAAI;EAExB;IAAA;IACC;IACAjC,OAAA;MACCsG,EAAE,EAAC,mBAAmB;MACtBC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAE7BxG,OAAA;QAAKuG,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC7BxG,OAAA;UAAKuG,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBACnCxG,OAAA,CAACnB,UAAU;YACV,cAAW,OAAO;YAClB4H,OAAO,EAAEX,WAAY;YAAAU,QAAA,eAErBxG,OAAA,CAAChB,2BAA2B;cAAEuH,SAAS,EAAC;YAAmB;cAAAnD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAElD,CAAC,eACbvD,OAAA;YAAKuG,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAExE,SAAS,CAAC,QAAQ;UAAC;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAExDvD,OAAA,CAACnB,UAAU;YACV6H,IAAI,EAAC,OAAO;YACZ,cAAW,OAAO;YAClBD,OAAO,EAAEX,WAAY;YAAAU,QAAA,eAErBxG,OAAA,CAACjB,SAAS;cAACwH,SAAS,EAAC;YAAoB;cAAAnD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAGNvD,OAAA;UAAKuG,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC/BxG,OAAA;YAAKuG,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC5CxG,OAAA,CAACvB,GAAG;cAAC8H,SAAS,EAAC,qBAAqB;cAAAC,QAAA,gBACjCxG,OAAA,CAACtB,UAAU;gBAAC6H,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAExE,SAAS,CAAC,UAAU;cAAC;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC/EvD,OAAA,CAACpB,IAAI;gBACJ0H,EAAE,EAAC,eAAe;gBAClBK,SAAS;gBACTC,OAAO,EAAE;gBACT;gBAAA;gBAAAJ,QAAA,EAGC3D,SAAS,CAACgE,GAAG,CAAEC,QAAQ,iBACvB9G,OAAA,CAACpB,IAAI;kBAACmI,IAAI;kBAACC,EAAE,EAAE,CAAE;kBAAAR,QAAA,eAChBxG,OAAA,CAACnB,UAAU;oBACV6H,IAAI,EAAC,OAAO;oBACZO,aAAa;oBACb;oBACA;oBACA;oBAAA;oBACAR,OAAO,EAAEA,CAAA,KAAMhB,mBAAmB,CAACqB,QAAQ,CAACtD,KAAK,CAAE,CAAC;oBAAA;oBAAAgD,QAAA,EAEnD/C,kBAAkB,CAACqD,QAAQ,CAACtD,KAAK;kBAAC;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB;gBAAC,GAVSuD,QAAQ,CAACtD,KAAK;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAW/B,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAILvD,OAAA,CAACvB,GAAG;cAAC8H,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCxG,OAAA;gBAAKuG,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAExE,SAAS,CAAC,OAAO;cAAC;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/DvD,OAAA;gBAAAwG,QAAA,eACDxG,OAAA,CAACrB,SAAS;kBACTuI,OAAO,EAAC,UAAU;kBAClB1D,KAAK,EAAE,GAAG9C,KAAK,EAAG;kBAClBgG,IAAI,EAAC,OAAO;kBACZS,SAAS;kBACTZ,SAAS,EAAC,qBAAqB;kBAC/Ba,QAAQ,EAAGzB,CAAC,IAAK;oBAChB,MAAM0B,UAAU,GAAG9C,QAAQ,CAACoB,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAC,IAAI,CAAC;;oBAEhD;oBACA,IAAI6D,UAAU,GAAG,GAAG,IAAIA,UAAU,GAAG,IAAI,EAAE;sBAC1C/E,aAAa,CAAC,IAAI,CAAC;oBACpB,CAAC,MAAM;sBACNA,aAAa,CAAC,KAAK,CAAC;oBACrB;oBAEA3B,QAAQ,CAAC0G,UAAU,CAAC;kBACrB,CAAE;kBACFC,UAAU,EAAE;oBACXC,YAAY,EAAE,IAAI;oBAClBC,EAAE,EAAE;sBAEH,0CAA0C,EAAE;wBAAEC,MAAM,EAAE;sBAAO,CAAC;sBAC9D,gDAAgD,EAAE;wBAAEA,MAAM,EAAE;sBAAO,CAAC;sBACpE,YAAY,EAAC;wBAACA,MAAM,EAAC;sBAAM;oBAE5B;kBACD,CAAE;kBACFC,KAAK,EAAErF;gBAAW;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEF,CAAC,EACLlB,UAAU,iBACXrC,OAAA,CAACtB,UAAU;cACVwE,KAAK,EAAE;gBACNC,QAAQ,EAAE,MAAM;gBAChBwE,KAAK,EAAE,SAAS;gBAChBC,SAAS,EAAE,MAAM;gBACjBC,GAAG,EAAE,MAAM;gBACXC,IAAI,EAAE,CAAC;gBACPC,YAAY,EAAE,KAAK;gBACnBC,OAAO,EAAE,MAAM;gBACfC,UAAU,EAACpI;cACZ,CAAE;cAAA2G,QAAA,gBACAxG,OAAA;gBAAMkD,KAAK,EAAE;kBAAE8E,OAAO,EAAE,MAAM;kBAAE7E,QAAQ,EAAE,MAAM;kBAAE8E,UAAU,EAAE,QAAQ;kBAAEC,WAAW,EAAC;gBAAM,CAAE;gBAE9FlF,uBAAuB,EAAE;kBAAEC,MAAM,EAAErD;gBAAQ;cAAE;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,EACCvB,SAAS,CAAC,yCAAyC,CAAC;YAAA;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CACZ,eAuBDvD,OAAA,CAACvB,GAAG;cAAC8H,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCxG,OAAA,CAACtB,UAAU;gBAAC6H,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAExE,SAAS,CAAC,SAAS;cAAC;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC/EvD,OAAA;gBAAAwG,QAAA,eACDxG,OAAA,CAACrB,SAAS;kBACTuI,OAAO,EAAC,UAAU;kBAClB1D,KAAK,EAAE,GAAGvC,UAAU,EAAG;kBACvBkH,SAAS;kBAETzB,IAAI,EAAC,OAAO;kBACZH,SAAS,EAAC,qBAAqB;kBAC/Ba,QAAQ,EAAGzB,CAAC,IAAK;oBAChB,MAAM0B,UAAU,GAAG9C,QAAQ,CAACoB,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAC,IAAI,CAAC;;oBAEhD;oBACA,IAAI6D,UAAU,GAAG,CAAC,IAAIA,UAAU,GAAG,EAAE,EAAE;sBACtC7E,eAAe,CAAC,IAAI,CAAC;oBACtB,CAAC,MAAM;sBACNA,eAAe,CAAC,KAAK,CAAC;oBACvB;oBAEAtB,aAAa,CAACmG,UAAU,CAACe,QAAQ,CAAC,CAAC,CAAC;kBACrC,CAAE;kBACFd,UAAU,EAAE;oBACXC,YAAY,EAAE,IAAI;oBAClBC,EAAE,EAAE;sBAEH,0CAA0C,EAAE;wBAAEC,MAAM,EAAE;sBAAO,CAAC;sBAC9D,gDAAgD,EAAE;wBAAEA,MAAM,EAAE;sBAAO,CAAC;sBACpE,YAAY,EAAC;wBAACA,MAAM,EAAC;sBAAM;oBAE5B;kBACD,CAAE;kBACFC,KAAK,EAAEnF;gBAAa;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACLhB,YAAY,iBACZvC,OAAA,CAACtB,UAAU;cACXwE,KAAK,EAAE;gBACNC,QAAQ,EAAE,MAAM;gBAChBwE,KAAK,EAAE,SAAS;gBAChBC,SAAS,EAAE,MAAM;gBACjBC,GAAG,EAAE,MAAM;gBACXC,IAAI,EAAE,CAAC;gBACPC,YAAY,EAAE,KAAK;gBACnBC,OAAO,EAAE,MAAM;gBACfC,UAAU,EAACpI;cACZ,CAAE;cAAA2G,QAAA,gBACAxG,OAAA;gBAAMkD,KAAK,EAAE;kBAAE8E,OAAO,EAAE,MAAM;kBAAE7E,QAAQ,EAAE,MAAM;kBAAE8E,UAAU,EAAE,QAAQ;kBAAEC,WAAW,EAAC;gBAAM,CAAE;gBAE9FlF,uBAAuB,EAAE;kBAAEC,MAAM,EAAErD;gBAAQ;cAAE;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,EACEvB,SAAS,CAAC,qCAAqC,CAAC;YAAA;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CACZ,eAGDvD,OAAA,CAACvB,GAAG;cAAC8H,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCxG,OAAA,CAACtB,UAAU;gBAAC6H,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAExE,SAAS,CAAC,eAAe;cAAC;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACrFvD,OAAA;gBAAAwG,QAAA,eACDxG,OAAA,CAACrB,SAAS;kBACTuI,OAAO,EAAC,UAAU;kBAClB1D,KAAK,EAAE,GAAGzC,YAAY,EAAG;kBACzBoH,SAAS;kBAETzB,IAAI,EAAC,OAAO;kBACZH,SAAS,EAAC,qBAAqB;kBAC/Ba,QAAQ,EAAGzB,CAAC,IAAK;oBAChB,MAAM0B,UAAU,GAAG9C,QAAQ,CAACoB,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAC,IAAI,CAAC;;oBAEhD;oBACA,IAAI6D,UAAU,GAAG,CAAC,IAAIA,UAAU,GAAG,EAAE,EAAE;sBACtC3E,oBAAoB,CAAC,IAAI,CAAC;sBAC1B;sBACA;oBACD,CAAC,MAAM;sBACNA,oBAAoB,CAAC,KAAK,CAAC;oBAC5B;oBACC1B,eAAe,CAACqG,UAAU,CAAC;kBAE7B,CAAE;kBACFC,UAAU,EAAE;oBACXC,YAAY,EAAE,IAAI;oBAClBC,EAAE,EAAE;sBAEH,0CAA0C,EAAE;wBAAEC,MAAM,EAAE;sBAAO,CAAC;sBAC9D,gDAAgD,EAAE;wBAAEA,MAAM,EAAE;sBAAO,CAAC;sBACpE,YAAY,EAAC;wBAACA,MAAM,EAAC;sBAAM;oBAE5B;kBACC,CAAE;kBACFC,KAAK,EAAEjF;gBAAkB;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,EACLd,iBAAiB,iBACjBzC,OAAA,CAACtB,UAAU;cACXwE,KAAK,EAAE;gBACNC,QAAQ,EAAE,MAAM;gBAChBwE,KAAK,EAAE,SAAS;gBAChBC,SAAS,EAAE,MAAM;gBACjBC,GAAG,EAAE,MAAM;gBACXC,IAAI,EAAE,CAAC;gBACPC,YAAY,EAAE,KAAK;gBACnBC,OAAO,EAAE,MAAM;gBACfC,UAAU,EAACpI;cACZ,CAAE;cAAA2G,QAAA,gBACAxG,OAAA;gBAAMkD,KAAK,EAAE;kBAAE8E,OAAO,EAAE,MAAM;kBAAE7E,QAAQ,EAAE,MAAM;kBAAE8E,UAAU,EAAE,QAAQ;kBAAEC,WAAW,EAAC;gBAAM,CAAE;gBAE9FlF,uBAAuB,EAAE;kBAAEC,MAAM,EAAErD;gBAAQ;cAAE;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,EACEvB,SAAS,CAAC,qCAAqC,CAAC;YAAA;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CACZ,eACDvD,OAAA,CAACvB,GAAG;cAAC8H,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCxG,OAAA,CAACtB,UAAU;gBAAC6H,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAExE,SAAS,CAAC,aAAa;cAAC;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eACnFvD,OAAA;gBAAAwG,QAAA,eACDxG,OAAA,CAACrB,SAAS;kBACTuI,OAAO,EAAC,UAAU;kBAClB1D,KAAK,EAAE,GAAGrC,aAAa,EAAG;kBAC1BgH,SAAS;kBAETzB,IAAI,EAAC,OAAO;kBACZH,SAAS,EAAC,qBAAqB;kBAC/Ba,QAAQ,EAAGzB,CAAC,IAAK;oBAChB,MAAM0B,UAAU,GAAG9C,QAAQ,CAACoB,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAC,IAAI,CAAC;;oBAEhD;oBACA,IAAI6D,UAAU,GAAG,CAAC,IAAIA,UAAU,GAAG,EAAE,EAAE;sBACtCzE,kBAAkB,CAAC,IAAI,CAAC;oBACzB,CAAC,MAAM;sBACNA,kBAAkB,CAAC,KAAK,CAAC;oBAC1B;oBAEAxB,gBAAgB,CAACiG,UAAU,CAAC;kBAC7B,CAAE;kBACFC,UAAU,EAAE;oBACXC,YAAY,EAAE,IAAI;oBAClBC,EAAE,EAAE;sBAEH,0CAA0C,EAAE;wBAAEC,MAAM,EAAE;sBAAO,CAAC;sBAC9D,gDAAgD,EAAE;wBAAEA,MAAM,EAAE;sBAAO,CAAC;sBACpE,YAAY,EAAC;wBAACA,MAAM,EAAC;sBAAM;oBAE5B;kBACD,CAAE;kBACFC,KAAK,EAAE/E;gBAAgB;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EACLZ,eAAe,iBACf3C,OAAA,CAACtB,UAAU;cACXwE,KAAK,EAAE;gBACNC,QAAQ,EAAE,MAAM;gBAChBwE,KAAK,EAAE,SAAS;gBAChBC,SAAS,EAAE,MAAM;gBACjBC,GAAG,EAAE,MAAM;gBACXC,IAAI,EAAE,CAAC;gBACPC,YAAY,EAAE,KAAK;gBACnBC,OAAO,EAAE,MAAM;gBACfC,UAAU,EAACpI;cACZ,CAAE;cAAA2G,QAAA,gBACAxG,OAAA;gBAAMkD,KAAK,EAAE;kBAAE8E,OAAO,EAAE,MAAM;kBAAE7E,QAAQ,EAAE,MAAM;kBAAE8E,UAAU,EAAE,QAAQ;kBAAEC,WAAW,EAAC;gBAAM,CAAE;gBAE9FlF,uBAAuB,EAAE;kBAAEC,MAAM,EAAErD;gBAAQ;cAAE;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,EACEvB,SAAS,CAAC,qCAAqC,CAAC;YAAA;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CACZ,eAkBDvD,OAAA,CAACvB,GAAG;cAAC8H,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCxG,OAAA,CAACtB,UAAU;gBAAC6H,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAExE,SAAS,CAAC,QAAQ;cAAC;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC9EvD,OAAA;gBAAAwG,QAAA,eACDxG,OAAA;kBACCqI,IAAI,EAAC,OAAO;kBACZ7E,KAAK,EAAEhD,WAAY;kBACnB4G,QAAQ,EAAE1B,uBAAwB;kBAClCa,SAAS,EAAC;gBAAmB;kBAAAnD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAGNvD,OAAA,CAACvB,GAAG;cAAC8H,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCxG,OAAA,CAACtB,UAAU;gBAAC6H,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAExE,SAAS,CAAC,YAAY;cAAC;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAClFvD,OAAA;gBAAAwG,QAAA,eACDxG,OAAA;kBACCqI,IAAI,EAAC,OAAO;kBACZ7E,KAAK,EAAE5C,eAAgB;kBACvBwG,QAAQ,EAAEvB,2BAA4B;kBACtCU,SAAS,EAAC;gBAAmB;kBAAAnD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACPvD,OAAA;UAAKuG,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjCxG,OAAA,CAAClB,MAAM;YACNoI,OAAO,EAAC,WAAW;YACnBT,OAAO,EAAEV,kBAAmB;YAC5BQ,SAAS,EAAE,aAAalE,UAAU,IAAIE,YAAY,IAAIE,iBAAiB,IAAIE,eAAe,GAAG,UAAU,GAAG,EAAE,EAAG;YAC/G2F,QAAQ,EAAEjG,UAAU,IAAIE,YAAY,IAAIE,iBAAiB,IAAIE,eAAgB;YAAA6D,QAAA,EAE7ExE,SAAS,CAAC,OAAO;UAAC;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEF;IACL;EAAA;AAEF,CAAC;AAACjD,EAAA,CA9kBIL,cAAc;EAAA,QA0BfhB,cAAc,EACOa,cAAc;AAAA;AAAAyI,EAAA,GA3BlCtI,cAAc;AAglBpB,eAAeA,cAAc;AAAC,IAAAsI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}