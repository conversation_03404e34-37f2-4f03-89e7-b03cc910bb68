import React, { useEffect, useState } from 'react';
import { Card, CardContent } from "@mui/material";
import { Button } from "@mui/material";
import useDrawerStore, { DrawerState } from '../../store/drawerStore';
import { ToursAnnouncementsIcon, ToursBannerIcon, ToursHotspotIcon, ToursTooltipIcon } from '../../assets/icons/icons';
import { useTranslation } from "react-i18next";


const FeatureSelectionModal: React.FC<{ isOpen: boolean; onClose: () => void; guideName: any; setStepData: any; stepData:any,count:any}> = (props) => {
  const { t: translate } = useTranslation();
  const { isOpen, onClose, guideName, setStepData, stepData, count } = props;
    const [hoveredItem, setHoveredItem] = useState<Number|null>();
    const [selectedStepType, setSelectedStepType] = useState<string | null>(null); // Track selected step type
    const {
        setSelectedTemplate,
        setBannerPopup,
      setSelectedTemplateTour,
      setSteps,
      steps,
      setTooltipCount,
      tooltipCount,
      HotspotGuideDetails,
      setElementSelected,
      TooltipGuideDetails,
      HotspotGuideDetailsNew,
      setSelectedStepTypeHotspot,
      selectedTemplate,
      selectedTemplateTour,
      createWithAI
  } = useDrawerStore((state: DrawerState) => state);
  const [selectedStepStyle, setSelectedStepStyle] = useState({});
    const features = [
      {
        icon: <span dangerouslySetInnerHTML={{ __html: ToursAnnouncementsIcon }} />,
        title: "Announcement",
        description: translate("An announcement of any new feature", { defaultValue: "An announcement of any new feature" }),
        action: () => {
          setSelectedStepType("Announcement");
          setSelectedStepStyle({
            borderColor: "var(--Theme-accentColor)",
            background: "#F6FFFF",
          });
        },
      },
      {
        icon: <span dangerouslySetInnerHTML={{ __html: ToursHotspotIcon }} />,
        title: "Hotspot",
        description: translate("Offer users quick tips", { defaultValue: "Offer users quick tips" }),
        action: () => {
          setSelectedStepType("Hotspot");
        },
      },
      {
        icon: <span dangerouslySetInnerHTML={{ __html: ToursBannerIcon }} />,
        title: "Banner",
        description: translate("Create in-line banners that get noticed", { defaultValue: "Create in-line banners that get noticed" }),
        action: () => {
          setSelectedStepType("Banner");
        },
      },
      {
        icon: <span dangerouslySetInnerHTML={{ __html: ToursTooltipIcon }} />,
        title: "Tooltip",
        description: translate("Anchored to selected elements", { defaultValue: "Anchored to selected elements" }),
        action: () => {
          setSelectedStepType("Tooltip");
        },
      },
    ];

    if (!isOpen) return null;
  const handleNextClick = () => {

    if ( (selectedTemplate==="Tour" &&(selectedStepType==="Banner"))) {
			let styleTag = document.getElementById("dynamic-body-style") as HTMLStyleElement;
			const bodyElement = document.body;

			// Add a dynamic class to the body
			bodyElement.classList.add("dynamic-body-style");

			if (!styleTag) {
				styleTag = document.createElement("style");
				styleTag.id = "dynamic-body-style";

				// Add styles for body and nested elements
				let styles = `
					.dynamic-body-style {
						padding-top: 50px !important;
						max-height:calc(100% - 55px);
					}

				`;

				styleTag.innerHTML = styles;
				document.head.appendChild(styleTag);
			}
		}


      if (selectedStepType) {
        // Based on selectedStepType, navigate and update steps
        if (selectedStepType === "Announcement") {
          TooltipGuideDetails();
          setSelectedTemplateTour("Announcement");
          setSelectedTemplate("Tour");
          setStepData({ ...stepData, type: "Announcement" });
        } else if (selectedStepType === "Hotspot") {
          HotspotGuideDetails();
          setSelectedTemplateTour("Hotspot");
          setSelectedTemplate("Tour");
          setSelectedStepTypeHotspot(true);
          setStepData({ ...stepData, type: "Hotspot" });
          setTooltipCount(tooltipCount + 1);
          setElementSelected(false);
        } else if (selectedStepType === "Banner") {
          TooltipGuideDetails();
          setSelectedTemplate("Tour");
          setSelectedTemplateTour("Banner");
          setStepData({ ...stepData, type: "Banner" });
          // Reset all banner canvas settings to defaults for new banner steps
          useDrawerStore.getState().resetBannerCanvasToDefaults();
          setBannerPopup(true);
        } else if (selectedStepType === "Tooltip") {
          TooltipGuideDetails();
          setSelectedTemplateTour("Tooltip");
          setSelectedTemplate("Tour");
          setStepData({ ...stepData, type: "Tooltip" });
          setTooltipCount(tooltipCount + 1);
        }

        const updatedSteps = steps.map(step => ({
          ...step,
          stepType: selectedStepType,
        }));

        setSteps(updatedSteps);
        onClose(); // Close the modal after proceeding
      }
    };
    if (createWithAI) {
      onClose();
      return null;
  }
    const isSelected = (title: string) => selectedStepType === title;
    const isHovered = (index: number) => hoveredItem === index;
  return (
      <div className="qadpt-modal">
      <div className="qadpt-tours-container">
        {/* Header Section */}
        <div className="qadpt-tour-header">
          <div className="qadpt-header-content">
            <span className="qadpt-title">{guideName}</span>
            <span className="qadpt-step-label">Step-1</span>
          </div>
          <div
            style={{
              fontSize: "13px",
              color: "#B1B1B1",
              lineHeight: "19.5px",
              letterSpacing: "0.3px",
            }}
          >
            {translate("Choose Step-1: Tour Type", { defaultValue: "Choose Step-1: Tour Type" })}
          </div>
          </div>

        {/* Step Selection Section */}
        <div className="qadpt-tours-content">
          {features.map((feature, index) => {
            const isSelected = selectedStepType === feature.title;
            const isHovered = hoveredItem === index;

              return (
                <div
                  key={index}
                  className={`qadpt-feature-card ${isSelected || isHovered ? "qadpt-feature-active" : ""}`}
                  onClick={() => setSelectedStepType(feature.title)}
                  onMouseEnter={() => setHoveredItem(index)}
                  onMouseLeave={() => setHoveredItem(null)}
                >
                  <div className="qadpt-feature-icon">{feature.icon}</div>
                  <div className="qadpt-feature-title">{translate(feature.title)}</div>
                  <div className="qadpt-feature-description">{feature.description}</div>
                </div>
              );
            })}
        </div>

        {/* Footer Action Buttons */}
        <div className="qadpt-tours-actions">
          <button
            onClick={handleNextClick}
            disabled={!selectedStepType}
            className={`qadpt-next-button ${selectedStepType ? "" : "qadpt-disabled"}`}
          >
            {translate("NEXT", { defaultValue: "NEXT" })}
          </button>
        </div>
      </div>
    </div>
  );
};

export default FeatureSelectionModal;