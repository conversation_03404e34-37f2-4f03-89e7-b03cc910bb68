import React, { useEffect, useState, useRef, useCallback } from "react";
import {
  Button,
  Tooltip,
  Box,
  LinearProgress,
  Typography,
  tooltipClasses,
	TooltipProps,
  MobileStepper,
	Breadcrumbs,
  IconButton,
} from "@mui/material";
import { CustomIconButton } from "../../Button";
import CloseIcon from "@mui/icons-material/Close";
import { styled } from "@mui/material/styles";
import useDrawerStore, { DrawerState } from "../../../../store/drawerStore";
import PerfectScrollbar from 'react-perfect-scrollbar';
import 'react-perfect-scrollbar/dist/css/styles.css';

interface ButtonAction {
	Action: string;
	ActionValue: string;
	TargetUrl: string;
}
interface ButtonProperties {
	Padding: number;
	Width: number;
	Font: number;
	FontSize: number;
	ButtonTextColor: string;
	ButtonBackgroundColor: string;
	ButtonBorderColor: string;
}
interface Canvas {
	BackgroundColor: string;
	BorderColor: string;
	BorderSize: string;
	Padding: string;
	Position: string;
	Radius: string;
	Width: string;
	Zindex: string;
}
interface Modal {
	DismissOption: boolean;
	IncludeRequisiteButtons: boolean;
	InteractionWithPopup: boolean;
	ModalPlacedOn: string;
}

interface ButtonData {
	ButtonStyle: string;
	ButtonName: string;
	Alignment: string;
	BackgroundColor: string;
	ButtonAction: ButtonAction;
  Padding: {
		Top: number;
		Right: number;
		Bottom: number;
		Left: number;
	};
	ButtonProperties: ButtonProperties;
	Id: string;
  }
interface CustomImage {
	AltText: string;
	BackgroundColor: string;
	Fill: string;
	Fit: string;
	SectionHeight: string;
	Url: string;
}
interface Design {
	NextStep: string;
	ButtonName: string;
	ElementPath: string;
	Id: string;
	ButtonId: string;
}

interface Step {
	xpath: string;
	content: string | JSX.Element;
	targetUrl: string;
	imageUrl: string;
	buttonData: ButtonData[];
	overlay: boolean;
	positionXAxisOffset: string;
	positionYAxisOffset: string;
	canvas: Canvas;
	modal: Modal;
	imageproperties: any;
	autoposition: any;
	elementclick: Design;
  PossibleElementPath: string
}
interface TooltipGuideProps {
	steps: Step[];
	currentUrl: string;
	onClose: () => void;
	tooltipConfig: any;
	startStepIndex: any;
	data: any;
}
const CustomWidthTooltip = styled(({ className, canvasStyle, hasOnlyButtons, hasOnlyText, dynamicWidth, ...props }: TooltipProps & {
	canvasStyle?: any,
	hasOnlyButtons?: boolean,
	hasOnlyText?: boolean,
    dynamicWidth?: string | null
}) => (
	<Tooltip
		{...props}
		classes={{ popper: className }}
		id="Tooltip-unique"
	/>
))(({ canvasStyle, hasOnlyButtons, hasOnlyText, dynamicWidth }: {
	canvasStyle: any,
	hasOnlyButtons?: boolean,
	hasOnlyText?: boolean,
    dynamicWidth?: string | null
  }) => ({
    [`& .${tooltipClasses.tooltip}`]: {
      backgroundColor: canvasStyle?.BackgroundColor || "#ffffff",
		// color: "black", // Set static text color
		//fontSize: "14px", // Font size
      padding: hasOnlyButtons ? "0px" : canvasStyle.Padding,
		// padding: "0px !important",
      borderRadius: canvasStyle?.Radius || canvasStyle?.BorderRadius||"10px",
      boxShadow: "0px 4px 12px rgba(0, 0, 0, 0.2)",
		border: canvasStyle?.BorderSize && canvasStyle?.BorderSize !== "0px" ? `${canvasStyle?.BorderSize} solid ${canvasStyle?.BorderColor || "transparent"}` : "none",
		// width: (hasOnlyButtons ) ? "auto !important" :
		// 	   canvasStyle?.Width ? `${canvasStyle.Width} !important` : "300px",
		width : 'auto !important',
      maxWidth: canvasStyle?.Width ? `${canvasStyle.Width} !important` : "300px",
      left: `${canvasStyle?.XAxisOffset || "auto"} !important`,
      bottom: `${canvasStyle?.YAxisOffset || "auto"} !important`,
    },
    [`& .${tooltipClasses.arrow}`]: {
      color: canvasStyle?.BackgroundColor || "#ffffff",
      fontSize: "16px",
      "&:before": {
			outlineWidth: canvasStyle?.BorderSize && canvasStyle?.BorderSize !== "0px" ? `${canvasStyle?.BorderSize}` : "0px", // This controls the width of the border of the arrow
			outlineColor: canvasStyle?.BorderColor || "transparent", // This controls the color of the border of the arrow
			outlineStyle: "solid", // Ensure the border is applied properly
      },
    },
    [`&.MuiTooltip-popper`]: {
		zIndex: canvasStyle?.Zindex || 11000, // Tooltip z-index
    },
  }),
)

const TooltipGuide: React.FC<TooltipGuideProps> = ({
  steps,
  currentUrl,
  onClose,
  tooltipConfig,
  startStepIndex,
  data,
}) => {
  let rect: any
  const [currentStepIndex, setCurrentStepIndex] = useState(startStepIndex || 1)
  const contentRef = useRef<HTMLDivElement>(null)
  const buttonContainerRef = useRef<HTMLDivElement>(null)

  // Auto-scroll related refs and state
  const scrollOperationQueue = useRef<Promise<void>>(Promise.resolve())

  // State to track if scrolling is needed
  const [needsScrolling, setNeedsScrolling] = useState(false);
  const scrollbarRef = useRef<any>(null);

  useEffect(() => {
    if (startStepIndex !== undefined) {
			setCurrentStepIndex(startStepIndex);
    }
	}, [startStepIndex]);
	const { setCurrentStep, selectedTemplate, currentStep, ProgressColor, createWithAI, pageinteraction } = useDrawerStore((state: DrawerState) => state);

	const currentStepData = steps[currentStepIndex - 1];
	const [targetElement, setTargetElement] = useState<HTMLElement | null>(null);
	const [tooltipPosition, setTooltipPosition] = useState({ top: 0, left: 0 });
	const [tooltipPlacement, setTooltipPlacement] = useState<"top" | "left" | "right" | "bottom">("top");
	const [isElementVisible, setIsElementVisible] = useState(false);
  const observerRef = useRef<MutationObserver | null>(null)

  // Add refs to store previous position values to prevent unnecessary updates
  const prevPositionRef = useRef({ top: 0, left: 0 })
	const prevPlacementRef = useRef<"top" | "left" | "right" | "bottom">("top")


  const calculateBestPosition = (element: HTMLElement): "top" | "left" | "right" | "bottom" => {
		const rect = element.getBoundingClientRect();
		const viewportWidth = window.innerWidth;
		const viewportHeight = window.innerHeight;

		const spaceTop = rect.top;
		const spaceBottom = viewportHeight - rect.bottom;
		const spaceLeft = rect.left;
		const spaceRight = viewportWidth - rect.right;

		const maxSpace = Math.max(spaceTop, spaceBottom, spaceLeft, spaceRight);

		if (maxSpace === spaceTop) return "top";
		if (maxSpace === spaceBottom) return "bottom";
		if (maxSpace === spaceLeft) return "left";
		return "right";
	};
	// Removed unused isElementInViewport function

  const validateElementPosition = (element: HTMLElement) => {
		const rect = element.getBoundingClientRect();
    return (
      rect.width > 0 &&
      rect.height > 0 &&
      rect.top !== 0 &&
      rect.left !== 0 &&
      !Number.isNaN(rect.top) &&
      !Number.isNaN(rect.left)
		);
	};
  const getElementByXPath = (xpath: string, PossibleElementPath: string): HTMLElement | null => {
		// Validate XPath before using it
    if (!xpath || xpath.trim() === "") {
			console.log("XPath is empty or undefined, trying PossibleElementPath:", PossibleElementPath);
      if (PossibleElementPath && PossibleElementPath.trim() !== "") {
        try {
					const result = document.evaluate(PossibleElementPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
					const node = result.singleNodeValue;
          if (node instanceof HTMLElement) {
						return node;
          } else if (node?.parentElement) {
						return node.parentElement;
          }
        } catch (error) {
					console.error("Error evaluating PossibleElementPath:", PossibleElementPath, error);
        }
      }
			return null;
    }

    try {
			const query = `${xpath}[not(ancestor::div[@id='quickAdopt_banner']) and not(@id='quickAdopt_banner')]`;
			console.log("Evaluating XPath query:", query);
			const result = document.evaluate(query, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
			const node = result.singleNodeValue;
      if (node instanceof HTMLElement) {
				return node;
      } else if (node?.parentElement) {
				return node.parentElement;
      } else if (node === null) {
				// Try PossibleElementPath as fallback
        if (PossibleElementPath && PossibleElementPath.trim() !== "") {
					console.log("Primary XPath failed, trying PossibleElementPath:", PossibleElementPath);
					const fallbackResult = document.evaluate(PossibleElementPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
					const fallbackNode = fallbackResult.singleNodeValue;
          if (fallbackNode instanceof HTMLElement) {
						return fallbackNode;
          } else if (fallbackNode?.parentElement) {
						return fallbackNode.parentElement;
          }
        }
				return null;
      } else {
				return null;
      }
    } catch (error) {
			console.error("Error evaluating XPath:", xpath, error);
			// Try PossibleElementPath as fallback
      if (PossibleElementPath && PossibleElementPath.trim() !== "") {
        try {
					console.log("XPath evaluation failed, trying PossibleElementPath:", PossibleElementPath);
					const result = document.evaluate(PossibleElementPath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
					const node = result.singleNodeValue;
          if (node instanceof HTMLElement) {
						return node;
          } else if (node?.parentElement) {
						return node.parentElement;
          }
        } catch (fallbackError) {
					console.error("Error evaluating PossibleElementPath:", PossibleElementPath, fallbackError);
        }
      }
			return null;
    }
	};

	// Enhanced scrolling function with multiple fallback methods for cross-environment compatibility
	const smoothScrollTo = (element: HTMLElement, targetTop: number, duration: number = 300) => {
		// Ensure targetTop is within valid bounds
		const maxScroll = element.scrollHeight - element.clientHeight;
		const clampedTargetTop = Math.max(0, Math.min(targetTop, maxScroll));

		// Method 1: Try native smooth scrolling first
		try {
			if ('scrollTo' in element && typeof element.scrollTo === 'function') {
				element.scrollTo({
					top: clampedTargetTop,
					behavior: 'smooth'
				});
				return;
			}
		} catch (error) {
			console.log("Native smooth scrollTo failed, trying animation fallback");
		}

		// Method 2: Manual animation fallback
		try {
			const startTop = element.scrollTop;
			const distance = clampedTargetTop - startTop;
			const startTime = performance.now();

			const animateScroll = (currentTime: number) => {
				const elapsed = currentTime - startTime;
				const progress = Math.min(elapsed / duration, 1);

				// Easing function for smooth animation
				const easeInOutCubic = (t: number) => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
				const easedProgress = easeInOutCubic(progress);

				element.scrollTop = startTop + (distance * easedProgress);

				if (progress < 1) {
					requestAnimationFrame(animateScroll);
				}
			};

			requestAnimationFrame(animateScroll);
		} catch (error) {
			console.log("RequestAnimationFrame failed, using direct assignment");
			// Method 3: Direct assignment as final fallback
			element.scrollTop = clampedTargetTop;
		}
	};

	// Enhanced reusable element polling function with exponential backoff and better timing
	const pollForElement = useCallback((
		xpath: string,
		possibleElementPath: string,
		onElementFound: (element: HTMLElement) => void,
		maxAttempts: number = 30,
		initialIntervalMs: number = 16, // Start with one frame
		logPrefix: string = "Element",
		onElementNotFound?: () => void
	) => {
		let elementCheckTimeout: NodeJS.Timeout | null = null;
		let attempts = 0;
		let currentInterval = initialIntervalMs;
		let isCleanedUp = false;

		const cleanup = () => {
			if (elementCheckTimeout) {
				clearTimeout(elementCheckTimeout);
				elementCheckTimeout = null;
			}
			isCleanedUp = true;
		};

		const checkForElement = () => {
			if (isCleanedUp) return;

			attempts++;
			console.log(`${logPrefix}: Polling attempt ${attempts}/${maxAttempts} (interval: ${currentInterval}ms)`);

			const element = getElementByXPath(xpath, possibleElementPath);

			if (element && validateElementPosition(element)) {
				console.log(`${logPrefix}: Found and validated after ${attempts} attempts`);
				cleanup();
				onElementFound(element);
				return;
			}

			if (attempts >= maxAttempts) {
				console.log(`${logPrefix}: Max attempts (${maxAttempts}) reached, element not found`);
				cleanup();
				if (onElementNotFound) {
					onElementNotFound();
				}
				return;
			}

			// Exponential backoff with jitter to prevent thundering herd
			const jitter = Math.random() * 0.1; // 10% jitter
			currentInterval = Math.min(currentInterval * (1.2 + jitter), 1000); // Cap at 1 second

			elementCheckTimeout = setTimeout(checkForElement, currentInterval);
		};

		// Start the polling
		checkForElement();

		// Return cleanup function
		return cleanup;
	}, []);

	// Enhanced cross-environment scrolling function
	const universalScrollTo = (element: HTMLElement | Window, options: { top?: number; left?: number; behavior?: 'smooth' | 'auto' }) => {
		const isWindow = element === window;
		const targetElement = isWindow ? document.documentElement : element as HTMLElement;

		// Method 1: Try native scrollTo if available and not blocked
		if (!isWindow && 'scrollTo' in element && typeof (element as any).scrollTo === 'function') {
			try {
				(element as any).scrollTo(options);
				return true;
			} catch (error) {
				console.log("Native scrollTo blocked or failed:", error);
			}
		}

		// Method 2: Try window.scrollTo for window element
		if (isWindow && options.behavior === 'smooth') {
			try {
				window.scrollTo(options);
				return true;
			} catch (error) {
				console.log("Window scrollTo failed:", error);
			}
		}

		// Method 3: Try smooth scrolling with custom animation
		if (options.behavior === 'smooth' && options.top !== undefined) {
			try {
				smoothScrollTo(targetElement, options.top);
				return true;
			} catch (error) {
				console.log("Smooth scroll animation failed:", error);
			}
		}

		// Method 4: Direct property assignment (final fallback)
		try {
			if (options.top !== undefined) {
				targetElement.scrollTop = options.top;
			}
			if (options.left !== undefined) {
				targetElement.scrollLeft = options.left;
			}
			return true;
		} catch (error) {
			console.log("Direct property assignment failed:", error);
			return false;
		}
	};

	const scrollToTargetElement = useCallback(async (targetElement: HTMLElement, placement: "top" | "left" | "right" | "bottom", stepData?: Step) => {
		if (!targetElement) {
			console.log("ScrollToTargetElement: No target element provided");
			return;
		}

		console.log("🎯 Starting enhanced auto-scroll to target element:", {
			element: targetElement,
			tagName: targetElement.tagName,
			className: targetElement.className,
			id: targetElement.id,
			placement: placement
		});

		try {
			// Add the scroll operation to the queue to prevent conflicts
			scrollOperationQueue.current = scrollOperationQueue.current.then(async () => {
				const rect = targetElement.getBoundingClientRect();
				const viewportHeight = window.innerHeight;
				const viewportWidth = window.innerWidth;

				// Calculate optimal scroll position based on placement and viewport
				let targetScrollTop = window.scrollY;
				let targetScrollLeft = window.scrollX;

				// Enhanced positioning logic based on tooltip placement
				switch (placement) {
					case "top":
						// Position element in lower third of viewport to leave room for tooltip above
						targetScrollTop = window.scrollY + rect.top - (viewportHeight * 0.7);
						break;
					case "bottom":
						// Position element in upper third of viewport to leave room for tooltip below
						targetScrollTop = window.scrollY + rect.top - (viewportHeight * 0.3);
						break;
					case "left":
						// Position element towards right side to leave room for tooltip on left
						targetScrollTop = window.scrollY + rect.top - (viewportHeight * 0.5);
						targetScrollLeft = window.scrollX + rect.left - (viewportWidth * 0.7);
						break;
					case "right":
						// Position element towards left side to leave room for tooltip on right
						targetScrollTop = window.scrollY + rect.top - (viewportHeight * 0.5);
						targetScrollLeft = window.scrollX + rect.left - (viewportWidth * 0.3);
						break;
					default:
						// Default: center the element vertically
						targetScrollTop = window.scrollY + rect.top - (viewportHeight * 0.5);
				}

				// Ensure scroll positions are within valid bounds
				const maxScrollTop = Math.max(0, document.documentElement.scrollHeight - viewportHeight);
				const maxScrollLeft = Math.max(0, document.documentElement.scrollWidth - viewportWidth);

				targetScrollTop = Math.max(0, Math.min(targetScrollTop, maxScrollTop));
				targetScrollLeft = Math.max(0, Math.min(targetScrollLeft, maxScrollLeft));

				console.log("📍 Calculated scroll position:", {
					targetScrollTop,
					targetScrollLeft,
					currentScrollY: window.scrollY,
					currentScrollX: window.scrollX,
					elementRect: rect
				});

				// Perform the scroll with multiple fallback methods
				let scrollSuccess = false;

				// Method 1: Try smooth scrolling
				try {
					scrollSuccess = universalScrollTo(window, {
						top: targetScrollTop,
						left: targetScrollLeft,
						behavior: 'smooth'
					});
					console.log("✅ Universal scroll success:", scrollSuccess);
				} catch (error) {
					console.log("❌ Universal scroll failed:", error);
				}

				// Method 2: Fallback to immediate scroll if smooth scroll failed
				if (!scrollSuccess) {
					try {
						window.scrollTo(targetScrollLeft, targetScrollTop);
						scrollSuccess = true;
						console.log("✅ Fallback scroll successful");
					} catch (error) {
						console.log("❌ Fallback scroll failed:", error);
					}
				}

				// Method 3: Final fallback using direct property assignment
				if (!scrollSuccess) {
					try {
						document.documentElement.scrollTop = targetScrollTop;
						document.documentElement.scrollLeft = targetScrollLeft;
						document.body.scrollTop = targetScrollTop;
						document.body.scrollLeft = targetScrollLeft;
						console.log("✅ Direct property assignment completed");
					} catch (error) {
						console.log("❌ Direct property assignment failed:", error);
					}
				}

				// Small delay to allow scroll to complete
				await new Promise(resolve => setTimeout(resolve, 100));

				console.log("🏁 Auto-scroll operation completed");
			});

			await scrollOperationQueue.current;
		} catch (error) {
			console.error("❌ ScrollToTargetElement error:", error);
		}
	}, [smoothScrollTo, universalScrollTo]);

	const progress = ((currentStepIndex ) / steps.length) * 100;
	const interactWithPage = tooltipConfig?.InteractWithPage;

  useEffect(() => {
    if (currentStep && currentStepIndex >= 0 && interactWithPage === false) {
      console.log('[Tooltips.tsx] Setting document.body.style.pointerEvents =', selectedTemplate === "Tour" ? "auto" : "none");
      document.body.style.pointerEvents = selectedTemplate === "Tour" ? "auto" : "none";
      const style = document.createElement('style');
      style.innerHTML = `.qadpt-editor  { pointer-events: auto !important; }`;
      document.head.appendChild(style);
      return () => {
        console.log('[Tooltips.tsx] Resetting document.body.style.pointerEvents = auto');
        document.body.style.pointerEvents = "auto";
        document.head.removeChild(style);
      };
    }
    return () => {
      // In case there's no active step or tooltip is closed
      console.log('[Tooltips.tsx] Cleanup: Resetting document.body.style.pointerEvents = auto');
      document.body.style.pointerEvents = "auto"; // Enable interactions
    };
  }, [currentStepIndex, interactWithPage]);

	// Handle overflow hidden based on overlay and page interaction settings
  useEffect(() => {
    if (currentStep && currentStepIndex >= 0 && currentStepData?.overlay && interactWithPage === false) {
			document.body.style.overflow = "hidden";
    } else {
			document.body.style.overflow = "";
    }

		// Cleanup on unmount
    return () => {
			document.body.style.overflow = "";
		};
  }, [currentStepData?.overlay, interactWithPage]);
	// Check if content needs scrolling with improved detection
	useEffect(() => {
		const checkScrollNeeded = () => {
			if (contentRef.current) {
				// Force a reflow to get accurate measurements
				contentRef.current.style.height = 'auto';
				const contentHeight = contentRef.current.scrollHeight;
				const containerHeight = 320; // max-height value
				const shouldScroll = contentHeight > containerHeight;


				setNeedsScrolling(shouldScroll);

				// Force update scrollbar
				if (scrollbarRef.current) {
					// Try multiple methods to update the scrollbar
					if (scrollbarRef.current.updateScroll) {
						scrollbarRef.current.updateScroll();
					}
					// Force re-initialization if needed
					setTimeout(() => {
						if (scrollbarRef.current && scrollbarRef.current.updateScroll) {
							scrollbarRef.current.updateScroll();
						}
					}, 10);
				}
			}
		};

		
		checkScrollNeeded();

		
		const timeouts = [
			setTimeout(checkScrollNeeded, 50),
			setTimeout(checkScrollNeeded, 100),
			setTimeout(checkScrollNeeded, 200),
			setTimeout(checkScrollNeeded, 500)
		];

		
		let resizeObserver: ResizeObserver | null = null;
		let mutationObserver: MutationObserver | null = null;

		if (contentRef.current && window.ResizeObserver) {
			resizeObserver = new ResizeObserver(() => {
				setTimeout(checkScrollNeeded, 10);
			});
			resizeObserver.observe(contentRef.current);
		}

		
		if (contentRef.current && window.MutationObserver) {
			mutationObserver = new MutationObserver(() => {
				setTimeout(checkScrollNeeded, 10);
			});
			mutationObserver.observe(contentRef.current, {
				childList: true,
				subtree: true,
				attributes: true,
				attributeFilter: ['style', 'class']
			});
		}

		return () => {
			timeouts.forEach(clearTimeout);
			if (resizeObserver) {
				resizeObserver.disconnect();
			}
			if (mutationObserver) {
				mutationObserver.disconnect();
			}
		};
	}, [currentStepData, currentStep]);

  const updateTargetAndPosition = () => {
		// if (currentUrl !== currentStep?.targetUrl) {
		// 	setTargetElement(null);
		// 	setIsElementVisible(false);
		// 	return;
		// }

		// Debug logging for XPath data
    console.log("Tooltip updateTargetAndPosition - currentStepData:", {
      xpath: currentStepData?.xpath,
      PossibleElementPath: currentStepData?.PossibleElementPath,
      stepIndex: currentStepIndex,
			createWithAI: createWithAI
		});

		const element = getElementByXPath(currentStepData?.xpath,currentStepData?.PossibleElementPath);
    if (!element) {
			console.log("Tooltip element not found for XPath:", currentStepData?.xpath, "PossibleElementPath:", currentStepData?.PossibleElementPath);
			setTargetElement(null);
			setIsElementVisible(false);
			return;
    }

		const isValid = validateElementPosition(element);
		//const isVisible = isElementInViewport(element);

    if (!isValid) {
			setTargetElement(null);
			setIsElementVisible(false);
			return;
    }

		const rect = element.getBoundingClientRect();
		const xOffset = parseFloat(currentStepData?.positionXAxisOffset || "0");
		const yOffset = parseFloat(currentStepData?.positionYAxisOffset || "0");

		setTargetElement(element);
		setIsElementVisible(true);

    // Calculate placement
    let newPlacement: "top" | "left" | "right" | "bottom"
    if (currentStepData?.autoposition) {
      newPlacement = calculateBestPosition(element)
    } else {
      const validPlacements = ["top", "left", "right", "bottom"] as const
      const placement = currentStepData?.canvas?.Position || "bottom"
      newPlacement = validPlacements.includes(placement as any)
        ? (placement as "top" | "left" | "right" | "bottom")
        : "bottom"
    }


    if (prevPlacementRef.current !== newPlacement) {
      prevPlacementRef.current = newPlacement
      setTooltipPlacement(newPlacement)
    }


    const newPosition = {
      top: Math.round(rect.top + window.scrollY + yOffset),
      left: Math.round(rect.left + window.scrollX + xOffset),
    }


    const positionChanged =
      Math.abs(prevPositionRef.current.top - newPosition.top) > 1 ||
      Math.abs(prevPositionRef.current.left - newPosition.left) > 1

    if (positionChanged) {
      prevPositionRef.current = newPosition
      setTooltipPosition(newPosition)
    }
  }

  // Enhanced auto-scroll navigation system for tooltip next functionality
	const handleNext = useCallback(async () => {
		if (selectedTemplate !== "Tour") {
			// Calculate the next index first (currentStepIndex is 1-based, so we need to check against steps.length)
			const nextIndex = currentStepIndex + 1;

			if (nextIndex <= steps.length) {
				// Get the next step data for scrolling (convert to 0-based for array access)
				const nextStepData = steps[nextIndex - 1];

				console.log("🔍 Starting navigation to next step:", {
					currentIndex: currentStepIndex,
					nextIndex: nextIndex,
					xpath: nextStepData?.xpath,
					stepTitle: `Step ${nextIndex}`
				});

				// Smart auto-scroll: Only scroll if element is not reasonably visible
				if (nextStepData?.xpath) {
					console.log("🔍 Checking auto-scroll for next element:", {
						xpath: nextStepData.xpath,
						stepIndex: nextIndex,
						stepTitle: `Step ${nextIndex}`
					});

					// Create a promise to handle element finding and scrolling
					const scrollPromise = new Promise<void>((resolve) => {
						// Use polling to find the element
						pollForElement(
							nextStepData.xpath || "",
							nextStepData.PossibleElementPath || "",
							async (foundElement) => {
								console.log("✅ Next element found:", {
									element: foundElement,
									tagName: foundElement.tagName,
									className: foundElement.className,
									id: foundElement.id
								});

								// Check if element needs scrolling
								const rect = foundElement.getBoundingClientRect();
								const viewportHeight = window.innerHeight;
								const viewportWidth = window.innerWidth;

								// More generous visibility check - element should be reasonably visible
								const isReasonablyVisible = (
									rect.top >= -50 && // Allow some element to be above viewport
									rect.left >= -50 && // Allow some element to be left of viewport
									rect.bottom <= viewportHeight + 50 && // Allow some element below viewport
									rect.right <= viewportWidth + 50 && // Allow some element right of viewport
									rect.width > 0 &&
									rect.height > 0
								);

								if (isReasonablyVisible) {
									console.log("✅ Element is reasonably visible, minimal adjustment");
									// Element is mostly visible, just ensure it's well positioned
									try {
										foundElement.scrollIntoView({
											behavior: 'smooth',
											block: 'nearest', // Don't force center if already visible
											inline: 'nearest'
										});
									} catch (error) {
										console.log("Minimal scroll adjustment failed:", error);
									}
								} else {
									console.log("🎯 Element not visible, performing auto-scroll");
									// Element is not visible, scroll to bring it into view
									try {
										foundElement.scrollIntoView({
											behavior: 'smooth',
											block: 'center', // Center it for better visibility
											inline: 'nearest'
										});
										console.log("✅ Auto-scroll completed successfully");
									} catch (scrollError) {
										console.error("❌ Auto-scroll failed:", scrollError);
									}
								}

								resolve();
							},
							20, // Reasonable maxAttempts
							30, // Reasonable initial interval
							"Next step element",
							() => {
								console.log("❌ Next element not found after polling, continuing without scroll");
								resolve();
							}
						);
					});

					try {
						await scrollPromise;
						console.log("✅ Element finding and scroll check completed");
					} catch (error) {
						console.error("❌ Scroll promise failed:", error);
					}
				} else {
					console.log("ℹ️ No xpath provided for next step, skipping auto-scroll");
				}

				// Update the step index AFTER scrolling is complete
				console.log("🔄 Updating step index from", currentStepIndex, "to", nextIndex);
				setCurrentStepIndex(nextIndex);
				setCurrentStep(currentStep + 1);

				// Small delay to allow DOM to update after step change
				await new Promise(resolve => setTimeout(resolve, 50));

			} else {
				console.log("🏁 Reached end of tooltip steps", {
					currentStepIndex,
					nextIndex,
					totalSteps: steps.length
				});
				// onClose(); // Close tooltip if no more steps
				return;
			}
		} else {
			// Tour template logic (consistent with 1-based indexing)
			if (currentStep < steps?.length) {
				setCurrentStepIndex((prev: any) => Math.max(prev + 1, 1));
				setCurrentStep(currentStep + 1);
			}
		}
	}, [currentStepIndex, steps, selectedTemplate, currentStep, pollForElement, calculateBestPosition, scrollToTargetElement, setCurrentStep, setCurrentStepIndex]);

  // Enhanced handlePrevious with auto-scroll functionality
	const handlePrevious = useCallback(async () => {
		if (selectedTemplate !== "Tour") {
			const prevIndex = Math.max(currentStepIndex - 1, 1);

			if (prevIndex >= 1) {
				console.log("🔙 Navigating to previous step:", {
					currentIndex: currentStepIndex,
					prevIndex: prevIndex
				});

				// Smart previous navigation scrolling
				if (prevIndex === 1) {
					console.log("HandlePrevious: Going back to first step, scroll to top");
					try {
						window.scrollTo({
							top: 0,
							behavior: 'smooth'
						});
					} catch (error) {
						console.log("HandlePrevious: Scroll to top failed:", error);
					}
				} else {
					// For other steps, check if previous step element needs scrolling
					const prevStepData = steps[prevIndex - 1];
					if (prevStepData?.xpath) {
						console.log("HandlePrevious: Checking if previous step element needs scrolling");

						setTimeout(() => {
							const prevElement = getElementByXPath(prevStepData.xpath, prevStepData.PossibleElementPath || "");
							if (prevElement) {
								const rect = prevElement.getBoundingClientRect();
								const isOutOfView = (
									rect.bottom < 0 ||
									rect.top > window.innerHeight ||
									rect.right < 0 ||
									rect.left > window.innerWidth
								);

								if (isOutOfView) {
									console.log("HandlePrevious: Previous element out of view, scrolling");
									try {
										prevElement.scrollIntoView({
											behavior: 'smooth',
											block: 'center',
											inline: 'nearest'
										});
									} catch (error) {
										console.log("HandlePrevious: Element scroll failed:", error);
									}
								}
							}
						}, 100);
					}
				}

				// Update step index
				setCurrentStepIndex(prevIndex);
				setCurrentStep(currentStep - 1);

				// Small delay to allow DOM to update
				await new Promise(resolve => setTimeout(resolve, 50));
			}
		} else {
			setCurrentStep(currentStep - 1);
		}
	}, [currentStepIndex, selectedTemplate, currentStep, universalScrollTo, setCurrentStep, setCurrentStepIndex]);
  useEffect(() => {
    // Debug logging for AI tooltip button click functionality
    console.log("🔍 Tooltip useEffect - Element click setup:", {
      currentStepIndex,
      elementclick: currentStepData?.elementclick,
      NextStep: currentStepData?.elementclick?.NextStep,
      Id: currentStepData?.elementclick?.Id,
      xpath: currentStepData?.xpath,
      hasButtons: currentStepData?.buttonData?.length > 0
    });

    // Determine if element click should be enabled
    const shouldEnableElementClick =
      // Explicit element click configuration
      currentStepData?.elementclick?.NextStep === "element" ||
      // Explicit button click configuration
      currentStepData?.elementclick?.NextStep === "button" ||
      // Default behavior: if no buttons are configured and no explicit NextStep is set,
      // enable element click navigation (this handles the case described in the issue)
      (!currentStepData?.buttonData?.length &&
       (!currentStepData?.elementclick?.NextStep ||
        currentStepData?.elementclick?.NextStep === "" ||
        currentStepData?.elementclick?.NextStep === "element"));

    if (shouldEnableElementClick) {
			const element = getElementByXPath(currentStepData?.xpath,currentStepData?.PossibleElementPath);
      if (element) {
        console.log("✅ Element found for click handler:", element, {
          reason: currentStepData?.elementclick?.NextStep === "element" ? "explicit element" :
                  currentStepData?.elementclick?.NextStep === "button" ? "explicit button" :
                  "default behavior (no buttons configured)"
        });
        const handleClick = () => {
					console.log("🖱️ Element clicked - advancing to next step");
					handleNext();
				};

				element.addEventListener("click", handleClick);
        return () => {
					element.removeEventListener("click", handleClick);
				};
        } else {
        console.log("❌ Element not found for xpath:", currentStepData?.xpath);
      }
		} else {
		  console.log("ℹ️ No element click setup - NextStep:", currentStepData?.elementclick?.NextStep, "hasButtons:", currentStepData?.buttonData?.length > 0);
		}
	}, [currentStepData, handleNext]);

  useEffect(() => {
    const handleDOMChanges = () => {
			requestAnimationFrame(updateTargetAndPosition);
		};
		observerRef.current = new MutationObserver(handleDOMChanges);
		const targetNode = document.body;
    observerRef.current.observe(targetNode, {
      childList: true,
      subtree: true,
      attributes: true,
      characterData: true,
		});
		updateTargetAndPosition();
    return () => {
			observerRef.current?.disconnect();
		};
	}, [currentStepData, currentUrl]);

  useEffect(() => {
		const handleViewportChanges = () => {
			requestAnimationFrame(updateTargetAndPosition);
		};

		window.addEventListener("scroll", handleViewportChanges);
		window.addEventListener("resize", handleViewportChanges);

    return () => {
			window.removeEventListener("scroll", handleViewportChanges);
			window.removeEventListener("resize", handleViewportChanges);
		};
	}, [currentStepData, currentUrl]);
  useEffect(() => {
		updateTargetAndPosition();
	}, [currentStepData, currentUrl, rect]); // Ensure all dependencies are included

	// Smart auto-scroll for current step changes - only when element is not visible
	useEffect(() => {
		if (!currentStepData?.xpath) {
			return;
		}

		// Small delay to allow DOM to settle
		const timeoutId = setTimeout(() => {
			const currentElement = getElementByXPath(currentStepData.xpath, currentStepData.PossibleElementPath || "");

			if (currentElement) {
				const rect = currentElement.getBoundingClientRect();
				const viewportHeight = window.innerHeight;
				const viewportWidth = window.innerWidth;

				// Check if element is completely out of view
				const isCompletelyOutOfView = (
					rect.bottom < 0 || // Completely above viewport
					rect.top > viewportHeight || // Completely below viewport
					rect.right < 0 || // Completely left of viewport
					rect.left > viewportWidth // Completely right of viewport
				);

				if (isCompletelyOutOfView) {
					console.log("🔄 Current step element is out of view, gentle auto-scroll");
					try {
						currentElement.scrollIntoView({
							behavior: 'smooth',
							block: 'center',
							inline: 'nearest'
						});
					} catch (error) {
						console.log("Current step auto-scroll failed:", error);
					}
				}
			}
		}, 100);

		return () => {
			clearTimeout(timeoutId);
		};
	}, [currentStepData]);
	const canvasStyle = currentStepData?.canvas || {}; // Assuming canvas is an array, take the first item
	const enableProgress = tooltipConfig.EnableProgress || false;
  function getProgressTemplate(tooltipConfig: any) {
    if (tooltipConfig?.ProgressTemplate === "1") {
			return "dots";
    } else if (tooltipConfig?.ProgressTemplate === "2") {
			return "linear";
    } else if (tooltipConfig?.ProgressTemplate === "3") {
			return "BreadCrumbs";
    } else {
			return "breadcrumbs";
    }
  }
	const progressTemplate = getProgressTemplate(tooltipConfig);

	const enabelCross = currentStepData?.modal?.DismissOption;
  const renderContent = () => {
		const hasImage =
			currentStepData?.imageUrl.startsWith("data:image/") || currentStepData?.imageUrl.startsWith("http");
    const hasText = Array.isArray(currentStepData?.content)
      ? currentStepData.content.some((item) => item?.Text && typeof item.Text === "string" && item.Text.trim() !== "")
			: typeof currentStepData?.content === "string" || React.isValidElement(currentStepData?.content);
    const textStyle = {
      fontSize: "14px",
      lineHeight: "1.5",
      whiteSpace: "pre-wrap",
      wordBreak: "break-word",
      color: "black",
		};
    return (
      <Box>
        {hasImage && (
          <Box
            component="img"
            src={currentStepData?.imageproperties?.Url}
            alt={currentStepData?.imageproperties?.AltText || "Step Image"}
            sx={{
              backgroundColor: currentStepData?.imageproperties?.BackgroundColor || "transparent",
              objectFit: currentStepData?.imageproperties?.Fit || "cover",
              maxHeight: currentStepData?.imageproperties?.SectionHeight || "auto",
              width: "100%",
            }}
          />
        )}
        {hasText && (
          <Box
            className="qadpt-preview"
					sx={{ margin: "0 !important", ...textStyle ,"& p": {
						margin: "4px 0",
					  },}}					dangerouslySetInnerHTML={{
              __html: Array.isArray(currentStepData.content)
                ? currentStepData.content
									.map((item: any) =>
										item.Text.replace(/<a /g, '<a target="_blank" rel="noopener noreferrer" ')
									)
                    .join("<br/>")
                : typeof currentStepData.content === "string"
                  ? currentStepData.content.replace(/<a /g, '<a target="_blank" rel="noopener noreferrer" ')
                  : "",
            }}
          />

				)}
			</Box>
		);
	};

	// Helper function to check if tooltip has only buttons (no text or images)
	const hasOnlyButtons = () => {
		const hasImage =
			currentStepData?.imageUrl.startsWith("data:image/") || currentStepData?.imageUrl.startsWith("http");
		const hasText = Array.isArray(currentStepData?.content)
			? currentStepData.content.some((item) => item?.Text && typeof item.Text === "string" && item.Text.trim() !== "")
			: typeof currentStepData?.content === "string" || React.isValidElement(currentStepData?.content);
		const hasButtons = currentStepData?.buttonData?.length > 0;

		return hasButtons && !hasImage && !hasText;
	};

	// Helper function to check if tooltip has only text (no buttons or images)
	const hasOnlyText = () => {
		const hasImage =
			currentStepData?.imageUrl.startsWith("data:image/") || currentStepData?.imageUrl.startsWith("http");
		const hasText = Array.isArray(currentStepData?.content)
			? currentStepData.content.some((item) => item?.Text && typeof item.Text === "string" && item.Text.trim() !== "")
			: typeof currentStepData?.content === "string" || React.isValidElement(currentStepData?.content);
		const hasButtons = currentStepData?.buttonData?.length > 0;

		return hasText && !hasImage && !hasButtons;
	};
	const hasHtmlMeaningfulContent = (htmlContent: string): boolean => {
		if (!htmlContent || htmlContent.trim() === '') {
			return false;
		}

		// Clean up common empty HTML patterns before checking
		let cleanedContent = htmlContent;

		// Remove empty paragraph tags
		cleanedContent = cleanedContent.replace(/<p>\s*(&nbsp;)*\s*<\/p>/gi, '');

		// Remove empty div tags
		cleanedContent = cleanedContent.replace(/<div>\s*(&nbsp;)*\s*<\/div>/gi, '');

		// Remove empty span tags
		cleanedContent = cleanedContent.replace(/<span>\s*(&nbsp;)*\s*<\/span>/gi, '');

		// Remove <br> tags
		cleanedContent = cleanedContent.replace(/<br\s*\/?>/gi, '');

		// Remove &nbsp; entities
		cleanedContent = cleanedContent.replace(/&nbsp;/gi, ' ');

		// If after cleaning there's no content left, return false
		if (cleanedContent.trim() === '') {
			return false;
		}

		// Create a temporary div to parse the cleaned HTML content
		const tempDiv = document.createElement('div');
		tempDiv.innerHTML = cleanedContent;

		// Get the text content (strips all HTML tags)
		const textContent = tempDiv.textContent || tempDiv.innerText;

		// Check if there's any non-whitespace text content
		if (textContent === null || textContent.trim() === '') {
			return false;
		}

		// Additional check for common empty HTML patterns
		// This handles cases like "<div><br></div>" or "<p>&nbsp;</p>" that might appear non-empty
		const lowerContent = cleanedContent.toLowerCase();
		const emptyPatterns = [
			'<div><br></div>',
			'<p><br></p>',
			'<div></div>',
			'<p></p>',
			'<span></span>',
			'<p>&nbsp;</p>',
			'<div>&nbsp;</div>',
			'<p> </p>',
			'<div> </div>'
		];

		if (emptyPatterns.some(pattern => lowerContent.includes(pattern)) && textContent.trim().length <= 1) {
			return false;
		}

		return true;
	};

	const hasValidTextContent =
		(typeof currentStepData?.content === "string" && hasHtmlMeaningfulContent(currentStepData?.content)) ||
		React.isValidElement(currentStepData?.content) ||
		(Array.isArray(currentStepData?.content) && currentStepData.content.some(
			(item: any) => item?.Text && typeof item.Text === "string" && hasHtmlMeaningfulContent(item.Text)
		));
	 // Check if there are buttons
	const hasButtons = currentStepData?.buttonData?.length > 0;

	//Check if there's a valid image
	const hasValidImage =
		currentStepData?.imageUrl?.startsWith("data:image/") ||
		currentStepData?.imageUrl?.startsWith("http");
	// Check if there's only text content (no images or buttons)
	const hasOnlyTextContent = hasValidTextContent && !hasValidImage && !hasButtons;

	// Check if there's only a button (no text or images)
	const hasOnlyButton = hasButtons && !hasValidTextContent && !hasValidImage && currentStepData?.buttonData?.length === 1;

	// Check if there's any meaningful content to display
	const hasValidContent = hasValidTextContent || hasValidImage;
		//Function to determine padding based on content and buttons
		const getPadding = () => {
			// Check if we have exactly one button and it's a previous button
			const hasPreviousButton = currentStepData?.buttonData?.length === 1 &&
				currentStepData?.buttonData?.[0]?.ButtonAction?.Action?.toLocaleLowerCase() === "previous";

			// Special case for previous button
			if (hasPreviousButton) {
				return "0px";
			}

			// Original logic
			if (!hasValidContent) {
				return currentStepData?.buttonData?.length === 1 ? "0px" : "4px";
			} else {
				return "0px";
    }
		};


	// Function to calculate the optimal width based on content and buttons
	// const calculateOptimalWidth = () => {
	// 	// If we have a fixed width from canvas settings and not a compact tooltip, use that
	// 	if (canvasStyle?.Width && !hasOnlyButtons() && !hasOnlyText()) {
	// 		return `${canvasStyle.Width}`;
	// 	}

	// 	// For tooltips with only buttons or only text, use auto width
	// 	if (hasOnlyButtons() || hasOnlyText()) {
	// 		return "auto";
	// 	}

	// 	// Get the width of content and button container
	// 	const contentWidth = contentRef.current?.scrollWidth || 0;
	// 	const buttonWidth = buttonContainerRef.current?.scrollWidth || 0;

	// 	// Use the larger of the two, with some minimum and maximum constraints
	// 	const optimalWidth = Math.max(contentWidth, buttonWidth);

	// 	// Add some padding to ensure text has room to wrap naturally
	// 	const paddedWidth = optimalWidth + 20; // 10px padding on each side

	// 	// Ensure width is between reasonable bounds
	// 	const minWidth = 250; // Minimum width
	// 	const maxWidth = 800; // Maximum width

	// 	const finalWidth = Math.max(minWidth, Math.min(paddedWidth, maxWidth));

	// 	return `${finalWidth}px`;
	// };

	// Update dynamic width when content or buttons change
	// useEffect(() => {
	// 	// Use requestAnimationFrame to ensure DOM has been updated
	// 	requestAnimationFrame(() => {
	// 		const newWidth = calculateOptimalWidth();
	// 		setDynamicWidth(newWidth);
	// 	});
	// }, [currentStepData, currentStepIndex]);
  const renderProgress = () => {
		if (!enableProgress) return null;

    if (progressTemplate === "dots") {
      return (
        <MobileStepper
          variant="dots"
          steps={steps.length}
          position="static"
          activeStep={currentStepIndex - 1}
          sx={{
						backgroundColor: "transparent", "& .MuiMobileStepper-dotActive": {
							backgroundColor: ProgressColor, // Active dot
            },
            placeContent: "center",
            padding: "2px  !important",
            "& .MuiMobileStepper-dot": {
              width: "6px !important",
              height: "6px !important",
						}
          }}
          backButton={<Button style={{ display: "none" }} />}
          nextButton={<Button style={{ display: "none" }} />}
        />
			);
    }

		// if (progressTemplate === "breadcrumbs") {
		// 	return (
		// 		<Breadcrumbs
		// 			aria-label="breadcrumb"
		// 			sx={{ marginTop: "10px" }}
		// 		>
		// 			{steps.map((_, index) => (
		// 				<Typography
		// 					key={index}
		// 					color={index === currentStepIndex ? "primary" : "text.secondary"}
		// 				>
		// 					Step {index + 1} of {steps.length}
		// 				</Typography>
		// 			))}
		// 		</Breadcrumbs>
		// 	);
		// }
    if (progressTemplate === "BreadCrumbs") {
      return (
                <Box sx={{display: "flex",
					alignItems: "center",
					placeContent: "center",
					gap: "5px",padding:"8px"}}>
                  {/* Custom Step Indicators */}

          {Array.from({ length: steps.length }).map((_, index) => (
            <div
              key={index}
              style={{
                          width: '14px',
                          height: '4px',
                          backgroundColor: index === currentStep - 1 ? ProgressColor : '#e0e0e0', // Active color and inactive color
                          borderRadius: '100px',
              }}
            />
          ))}

                </Box>
              );
		}

		if (progressTemplate === "breadcrumbs") {
			return (
				<Box sx={{paddingTop:"8px"}}>
					<Typography
						variant="body2"
						sx={{ padding: "8px", color: ProgressColor}}
					>
						 Step {currentStepIndex} of {steps.length}
					</Typography>
				</Box>
			);
		}

		if (progressTemplate === "linear") {
			return (
				<Box sx={{padding: hasOnlyButtons() ? "8px" : "0",}}>
					<Typography variant="body2">
						<LinearProgress
							variant="determinate"
							value={progress}
							sx={{
								height: "6px",
										borderRadius: "20px",
										margin: hasOnlyButtons() ? "0" : "6px 10px",
								'& .MuiLinearProgress-bar': {
                                backgroundColor: ProgressColor, // progress bar color
                              },}}
						/>
					</Typography>
				</Box>
			);
		}

		return null;
	};

	const renderButtons = () => {
		return currentStepData?.buttonData?.length > 0
			? currentStepData.buttonData.map((button: any, index: any) => {
					const buttonStyle = {
						backgroundColor: button.ButtonProperties.ButtonBackgroundColor,
						color: button.ButtonProperties.ButtonTextColor,
						border: button.ButtonProperties.ButtonBorderColor,
						padding: "4px 8px !important",
						lineHeight: "normal",
						width: "auto",
						fontSize: "12px",
						fontFamily: "Poppins",
						borderRadius: "8px",
						textTransform: "none",
						minWidth: "fit-content",
						boxShadow: "none !important", // Remove box shadow in normal state
						"&:hover": {
							boxShadow: "none !important", // Remove box shadow in hover state
							backgroundColor: button.ButtonProperties.ButtonBackgroundColor, // Keep the same background color on hover
							opacity: 0.9, // Slightly reduce opacity on hover for visual feedback
						},
					};
					const handleClick = () => {
						console.log("🔍 Button clicked:", {
							buttonId: button.Id,
							buttonAction: button.ButtonAction.Action,
							elementclick: currentStepData?.elementclick,
							NextStep: currentStepData?.elementclick?.NextStep,
							expectedButtonId: currentStepData?.elementclick?.Id
						});

						if (button.ButtonAction.Action.toLocaleLowerCase() === "close") {
							//onClose();
						} else if (
							button.ButtonAction.Action.toLocaleLowerCase() === "next" &&
							currentStepData?.elementclick?.NextStep !== "button"
						) {
							console.log("🚀 Regular next button - advancing step");
							handleNext();
						} else if (
							button.ButtonAction.Action.toLocaleLowerCase() === "next" &&
							currentStepData?.elementclick?.NextStep === "button" &&
							(button.Id === currentStepData?.elementclick?.ButtonId ||
							 button.Id === currentStepData?.elementclick?.Id)
						) {
							console.log("🎯 Button click with element interaction - clicking element and advancing", {
								buttonId: button.Id,
								expectedButtonId: currentStepData?.elementclick?.ButtonId,
								expectedId: currentStepData?.elementclick?.Id,
								elementclick: currentStepData?.elementclick,
								stepIndex: currentStepIndex,
								currentStepData: currentStepData,
								xpath: currentStepData?.xpath,
								possibleElementPath: currentStepData?.PossibleElementPath
							});
							const element = getElementByXPath(currentStepData?.xpath,currentStepData?.PossibleElementPath);
              if (element) {
								console.log("✅ Element found, clicking it:", element);
								element.click();
								handleNext();
              } else {
              	console.log("❌ Element not found for button click interaction", {
									xpath: currentStepData?.xpath,
									possibleElementPath: currentStepData?.PossibleElementPath,
									stepIndex: currentStepIndex
								});
              }
            } else if (button.ButtonAction.Action.toLocaleLowerCase() === "previous") {
							handlePrevious();
            } else if (button.ButtonAction.Action === "restart" || button.ButtonAction.Action === "Restart") {
							// Enhanced restart functionality with auto-scroll
							console.log("🔄 Restarting tooltip guide");

							// Reset to the first step (1-based indexing)
							setCurrentStepIndex(1);
							setCurrentStep(1);

							// Check if we need to navigate to a different page (multi-page) or stay on current page (single-page)
							if (steps[0]?.targetUrl && steps[0].targetUrl.trim() !== window.location.href.trim()) {
								// Multi-page: Navigate to the first step's URL
								window.location.href = steps[0].targetUrl;
							} else {
								// Single-page: Gentle scroll to first step element or top
								if (steps[0]?.xpath) {
									console.log("🎯 Gentle scroll to first step element on restart");

									// Check if first step element exists and use gentle scroll
									const firstElement = getElementByXPath(steps[0].xpath, steps[0]?.PossibleElementPath || "");
									if (firstElement) {
										try {
											firstElement.scrollIntoView({
												behavior: 'smooth',
												block: 'center',
												inline: 'nearest'
											});
											console.log("✅ Gentle restart scroll completed");
										} catch (error) {
											console.log("❌ Gentle restart scroll failed, scrolling to top");
											window.scrollTo({ top: 0, behavior: 'smooth' });
										}
									} else {
										console.log("❌ First step element not found, scrolling to top");
										window.scrollTo({ top: 0, behavior: 'smooth' });
									}
								} else {
									// No xpath available, just scroll to top
									console.log("ℹ️ No xpath for first step, scrolling to top");
									window.scrollTo({ top: 0, behavior: 'smooth' });
								}
							}
            } else if (button.ButtonAction.Action === "open-url" && button.ButtonAction.ActionValue === "new-tab") {
							window.open(button.ButtonAction.TargetUrl, "_blank");
            } else if (button.ButtonAction.Action === "open-url" && button.ButtonAction.ActionValue === "same-tab") {
							window.location.href = button.ButtonAction.TargetUrl;
            } else {
							//onClose();
            }
					};
          return (
						<Button
							key={index}
							variant="contained"
							sx={buttonStyle}
							onClick={handleClick}
						>
              {button.ButtonName}
            </Button>
					);
        })
			: null;
	};

  const TooltipContent = (
    <>
      <div style={{ placeContent: "end", display: "flex" }}>
        {enabelCross && (
          <IconButton 
            sx={{
              position: "absolute",
              boxShadow: "rgba(0, 0, 0, 0.06) 0px 4px 8px",
              background: "#fff !important",
              border: "1px solid #ccc",
              zIndex: "999",
              borderRadius: "50px",
              padding: "1px !important",
              float: "right",
              top: "-12px",
              right: "-12px",
							margin: canvasStyle?.BorderSize && canvasStyle?.BorderSize !== "0px" ? `-${parseInt(canvasStyle?.BorderSize) - 3}px` : "0px",
					  }}
					  
          >
            <CloseIcon sx={{ zoom: "1", color: "#000" }} />
          </IconButton>
        )}
      </div>
	  <PerfectScrollbar
				key={`scrollbar-${needsScrolling}`}
				ref={scrollbarRef}
				style={{ maxHeight: "270px" }}
				options={{
					suppressScrollY: !needsScrolling,
					suppressScrollX: true,
					wheelPropagation: false,
					swipeEasing: true,
					minScrollbarLength: 20,
					scrollingThreshold: 1000,
					scrollYMarginOffset: 0
				}}
			>
			<div ref={contentRef}
			style={{
				// maxHeight: "270px",
            overflow: "hidden",
            borderRadius: "4px",
            padding: getPadding(),
            position: "relative",
            zIndex: "999",
				// border: canvasStyle?.BorderSize && canvasStyle?.BorderSize !== "0px" ? `${canvasStyle?.BorderSize} solid ${canvasStyle?.BorderColor || "transparent"}` : "none",

			}}>
          <Box>
					{(!hasOnlyButtons()) && (
              <Box
                display="flex"
                flexDirection="column"
                alignItems={hasOnlyText() ? "flex-start" : "center"}
                sx={{
                  width: hasOnlyText() ? "auto" : "100%",
								padding: hasOnlyText() ? "0" : undefined
                }}
              >
                {renderContent()}
              </Box>
            )}
            {currentStepData?.buttonData?.length > 0 && (
              <Box
                ref={buttonContainerRef}
                display="flex"
                sx={{
                  placeContent: "center",
								// padding: hasOnlyButtons() ? "4px" : "10px",
                  gap: "4px",
                  backgroundColor: currentStepData.buttonData[0]?.BackgroundColor,
                  width: hasOnlyButtons() ? "auto" : "100%",
                  alignItems: "center",
                }}
              >
                {renderButtons()}
              </Box>
            )}
          </Box>
        </div>
      </PerfectScrollbar>
      {enableProgress && steps.length>1 && selectedTemplate !== "Hotspot" && <Box>{renderProgress()}</Box>}{" "}
    </>
	);

	//const overlay = currentStep?.overlay || "";
  const overlayStyle = {
    position: "fixed" as const,
    top: 0,
    left: 0,
    width: "100vw",
    height: "100vh",
    backgroundColor: "transparent",
    pointerEvents: "none",
    zIndex: 9999,
	};

  const getOverlaySections = () => {
		if (!targetElement) return null;

		const rect = targetElement.getBoundingClientRect();
		const viewportHeight = window.innerHeight;
		const viewportWidth = window.innerWidth;

    const sections = {
      top: {
        position: "fixed" as const,
        top: 0,
        left: 0,
        width: "100%",
        height: `${rect.top}px`,
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        pointerEvents: "auto",
      },
      bottom: {
        position: "fixed" as const,
        top: `${rect.bottom}px`,
        left: 0,
        width: "100%",
        height: `${viewportHeight - rect.bottom}px`,
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        pointerEvents: "auto",
      },
      left: {
        position: "fixed" as const,
        top: `${rect.top}px`,
        left: 0,
        width: `${rect.left}px`,
        height: `${rect.height}px`,
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        pointerEvents: "auto",
      },
      right: {
        position: "fixed" as const,
        top: `${rect.top}px`,
        left: `${rect.right}px`,
        width: `${viewportWidth - rect.right}px`,
        height: `${rect.height}px`,
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        pointerEvents: "auto",
      },
		};

		return sections;
	};
  const highlightBoxStyle = targetElement
    ? {
        position: "fixed" as const,
        top: `${targetElement.getBoundingClientRect().top}px`,
        left: `${targetElement.getBoundingClientRect().left}px`,
        width: `${targetElement.getBoundingClientRect().width}px`,
        height: `${targetElement.getBoundingClientRect().height}px`,
				// border: '2px solid #fff',
        borderRadius: "4px",
        pointerEvents: interactWithPage ? "auto" : "none",
        zIndex: 0,
      }
    : {}

  return (
    <>
      {currentStepData?.overlay && targetElement && isElementVisible && selectedTemplate !== "Tour" && (
        <Box sx={overlayStyle}>
          {Object.entries(getOverlaySections() || {}).map(([key, style]) => (
						<Box
							key={key}
							sx={style}
						/>
          ))}

          <Box sx={highlightBoxStyle} />
        </Box>
      )}
      {targetElement && isElementVisible && (

        <CustomWidthTooltip
          open
          title={TooltipContent}
          placement={tooltipPlacement}
          arrow
          PopperProps={{
            anchorEl: targetElement,
            modifiers: [
              {
                name: "preventOverflow",
                options: {
                  boundary: window,
                  altAxis: true,
                  padding: 10,
                },
              },
              {
                name: "computeStyles",
                options: {
                  // Disable adaptive positioning to prevent micro-adjustments
                  adaptive: false,
                  // Round positions to prevent sub-pixel rendering
                  roundOffsets: ({ x, y }: { x: number; y: number }) => ({
                    x: Math.round(x),
                    y: Math.round(y),
                  }),
                },
              },
            ],
          }}
          canvasStyle={canvasStyle}
          hasOnlyButtons={hasOnlyButtons()}
          hasOnlyText={hasOnlyText()}
					//dynamicWidth={dynamicWidth}
        >
          <Box
            sx={{
              position: "absolute",
              top: targetElement.offsetTop,
              left: targetElement.offsetLeft,
              width: targetElement.offsetWidth,
              height: targetElement.offsetHeight,
            }}
          />
        </CustomWidthTooltip>
      )}
    </>
	);
};

export default TooltipGuide;
