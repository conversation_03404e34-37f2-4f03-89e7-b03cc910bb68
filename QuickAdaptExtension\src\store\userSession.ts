import { FALSE } from "sass";
import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";

interface ViewState {
	IsAnnouncementCreationBuilderOpen: boolean;
	IsBannerCreationBuilderOpen: boolean;
	IsHotspotCreationBuilderOpen: boolean;
	IsGuidesListOpen: boolean;
	IsInHomeScreen: boolean;
	IsAnnouncementListOpen: boolean;
	IsBannerlistOpen: boolean;
	currentGuideId: string;
	IsTooltipCreationBuilderOpen: boolean;
	isGuideInfoScreen: boolean;
  hasAnnouncementOpened: boolean;
  setHasAnnouncementOpened: (hasOpened: boolean) => void;
	setIsGuideInfoScreen(isGuideInfoScreen: boolean): void;
	guideName: string;
	SetGuideName(guideName: string): void;
	selectedTemplated: string;
	setSelectedTemplated: (template: string) => void;
	selectedTemplateTour: string;
	setSelectedTemplateTour: (selectedTemplateTour: string) => void;
	isHomeScreen: boolean;
	setIsHomeScreen(isHomeScreen: boolean): void;
	isTemplateScreen: boolean;
	setIsTemplateScreen(isTemplateScreen: boolean): void;
	setIsTooltipCreationBuilderOpen: (isOpen: boolean) => void;
	setIsAnnouncementCreationBuilderOpen: (isOpen: boolean) => void;
	setIsBannerCreationBuilderOpen: (isOpen: boolean) => void;
	setIsHotspotCreationBuilderOpen: (isOpen: boolean) => void;
	setIsGuidesListOpen: (isOpen: boolean) => void;
	setIsInHomeScreen: (isOpen: boolean) => void;
	setIsAnnouncementListOpen: (isOpen: boolean) => void;
	setIsBannerslistOpen: (isOpen: boolean) => void;
	clearUserSession: () => void;
	setCurrentGuideId: (params: string) => void;
	getCurrentGuideId: () => void;
}
const useUserSession = create<ViewState>()(
	devtools(
		persist(
			immer((set) => ({
				IsAnnouncementCreationBuilderOpen: false,
				IsBannerCreationBuilderOpen: false,
				IsHotspotCreationBuilderOpen: false,
				IsGuidesListOpen: false,
				IsInHomeScreen: false,
				IsAnnouncementListOpen: false,
				IsBannerlistOpen: false,
				currentGuideId: "",
				IsTooltipCreationBuilderOpen: false,
				isGuideInfoScreen: false,
        hasAnnouncementOpened: false,
				guideName: "",
				selectedTemplated: "",
				selectedTemplateTour: "",
				isHomeScreen: true,
				isTemplateScreen: false,
				setIsTemplateScreen: (isTemplateScreen: boolean) => {
					set((state) => {
						state.isTemplateScreen = isTemplateScreen;
					});
				},
				setIsHomeScreen: (isHomeScreen: boolean) => {
					set((state) => {
						state.isHomeScreen = isHomeScreen;
					});
				},
				setSelectedTemplated: (data) => {
					set((state) => {
						state.selectedTemplated = data;
					});
				},
				setSelectedTemplateTour: (data) => {
					set((state) => {
						state.selectedTemplateTour = data;
					});
				},
				SetGuideName: (data) => {
					set((state) => {
						state.guideName = data;
					});
				},
				setIsGuideInfoScreen: (isGuideInfoScreen: boolean) => {
					set((state) => {
						state.isGuideInfoScreen = isGuideInfoScreen;
					});
				},
        setHasAnnouncementOpened: (hasOpened: boolean) => {
          set((state) => {
            state.hasAnnouncementOpened = hasOpened;
          });
        },

				setIsTooltipCreationBuilderOpen: (isOpen: boolean) => {
					set((state) => {
						state.IsTooltipCreationBuilderOpen = isOpen;
					});
				},
				setIsAnnouncementCreationBuilderOpen: (isOpen: boolean) => {
					set((state) => {
						state.IsAnnouncementCreationBuilderOpen = isOpen;
					});
				},
				setIsBannerCreationBuilderOpen: (isOpen: boolean) => {
					set((state) => {
						state.IsBannerCreationBuilderOpen = isOpen;
					});
				},
				setIsHotspotCreationBuilderOpen: (isOpen: boolean) => {
					set((state) => {
						state.IsHotspotCreationBuilderOpen = isOpen;
					});
				},
				setIsGuidesListOpen: (isOpen: boolean) => {
					set((state) => {
						state.IsGuidesListOpen = isOpen;
					});
				},

				setIsInHomeScreen: (isOpen: boolean) => {
					set((state) => {
						state.IsInHomeScreen = isOpen;
					});
				},
				setIsAnnouncementListOpen: (isOpen: boolean) => {
					set((state) => {
						state.IsAnnouncementListOpen = isOpen;
					});
				},

				setIsBannerslistOpen: (isOpen: boolean) => {
					set((state) => {
						state.IsBannerlistOpen = isOpen;
					});
				},

				clearUserSession: () => {
					set((state) => ({
						...state,
						IsAnnouncementCreationBuilderOpen: false,
						IsBannerCreationBuilderOpen: false,
						IsHotspotCreationBuilderOpen: false,
						IsGuidesListOpen: false,
						IsInHomeScreen: false,
						IsAnnouncementListOpen: false,
						IsBannerlistOpen: false,
						isGuideInfoScreen: false,
						// guideName: state.guideName,
						// selectedTemplate: state.selectedTemplate,
						// selectedTemplateTour: state.selectedTemplateTour,
					}));
				},
				setCurrentGuideId: (guideId: string) => {
					set((state) => {
						localStorage.setItem("CurrentGuideId", JSON.stringify(guideId));
						state.currentGuideId = guideId;
					});
				},
				getCurrentGuideId: () => {
					const gId = JSON.parse(localStorage.getItem("CurrentGuideId") || "{}");
					set((state) => {
						state.currentGuideId = typeof gId === "string" ? gId : "";
					});
				},
			})),
			{
				name: "user-session-storage",
				partialize: (state) => ({
					IsAnnouncementCreationBuilderOpen: state.IsAnnouncementCreationBuilderOpen,
					IsBannerCreationBuilderOpen: state.IsBannerCreationBuilderOpen,
					IsGuidesListOpen: state.IsGuidesListOpen,
					IsInHomeScreen: state.IsInHomeScreen,
					IsAnnouncementListOpen: state.IsAnnouncementListOpen,
					IsBannerlistOpen: state.IsBannerlistOpen,
					isGuideInfoScreen: state.isGuideInfoScreen,
					guideName: state.guideName,
					selectedTemplated: state.selectedTemplated,
					selectedTemplateTour: state.selectedTemplateTour,
					hasAnnouncementOpened: state.hasAnnouncementOpened,
					currentGuideId: state.currentGuideId,
				}),
			}
		)
	)
);

export default useUserSession;
