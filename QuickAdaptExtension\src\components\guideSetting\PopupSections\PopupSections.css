.qadpt-imgsec-popover .MuiPaper-elevation {
    height: 44px;
    width: auto;
    top: 64px !important;
    margin-left: auto;
    margin-right: auto;
    left: 433px !important;
    padding: 5px 10px;
}

.qadpt-imgsec-popover .qadpt-tool-btn{
    display: flex;
    /* justify-content: space-between; */
    align-items: center;
    height: 100%;
    padding: 0 12px;
    font-size: 12px;
}

.qadpt-imgsec-popover .qadpt-tool-items {
    display: flex;
    align-items: center;
    gap:8px; 
    font-size:12px;
    cursor: pointer;
}
.qadpt-container-box {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding: 0;
    margin: 0;
    overflow: auto
}
.qadpt-color-picker{
    width: 20px;
    height: 20px;
    padding: 0 !important;
    border-radius: 50%;
    border: 1px solid var(--border-color);
}

.qadpt-color-picker::-webkit-color-swatch-wrapper {
    padding: 0;
    border-radius: 50%;
}
.qadpt-color-picker::-webkit-color-swatch  {
    border-radius: 50%;
    border: none;
}
