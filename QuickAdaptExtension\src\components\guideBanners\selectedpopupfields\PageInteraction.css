.canvas-properties {
    border: 1px solid #ccc;
    padding: 10px;
    border-radius: 8px;
    width: 250px;
  }
  
  .field {
    margin-bottom: 15px;
  }
  
  label {
    display: block;
    font-size: 14px;
    margin-bottom: 5px;
  }
  
  .input-with-unit {
    display: flex;
    align-items: center;
  }
  
  .input-with-unit input {
    flex: 1;
    padding: 5px;
    border-radius: 4px;
    border: 1px solid #ccc;
  }
  
  .input-with-unit span {
    margin-left: 5px;
  }
  
  .position-buttons {
    display: flex;
    gap: 10px;
  }
  
  .position-buttons button {
    flex: 1;
    padding: 8px;
    border: 1px solid #ccc;
    background-color: #f0f0f0;
    cursor: pointer;
  }
  
  .position-buttons .active {
    background-color: #d0e1f9;
  }
  
  input[type='color'] {
    padding: 0;
    border: none;
    width: 100%;
    height: 35px;
    cursor: pointer;
  }
  