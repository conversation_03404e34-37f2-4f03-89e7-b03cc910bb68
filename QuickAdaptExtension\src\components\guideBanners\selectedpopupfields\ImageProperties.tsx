import React, { useState } from "react";
import { <PERSON>, <PERSON>po<PERSON>, <PERSON><PERSON>ield, Grid, IconButton, Button } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import "../../guideDesign/Canvas.module.scss";
// import Draggable from "react-draggable";
import ArrowBackIosNewOutlinedIcon from "@mui/icons-material/ArrowBackIosNewOutlined";
import "../guideBanner.css";
const ButtonSettings = () => {
	const [borderColor, setBorderColor] = useState("#000000");
	const [backgroundColor, setBackgroundColor] = useState("#FFFFFF");
	const [isOpen, setIsOpen] = useState(true);
	const [selectedPosition, setSelectedPosition] = useState("");
	const [url, setUrl] = useState("");
	const [action, setAction] = useState("close");
	const [openInNewTab, setOpenInNewTab] = useState(true);
	const [colors, setColors] = useState({
		fill: "#4CAF50",
		border: "#4CAF50",
		text: "#ffffff",
	});

	const handleClose = () => {
		setIsOpen(false);
	};

	if (!isOpen) return null;

	const handleColorChange = (type: any, color: any) => {
		setColors((prevColors) => ({ ...prevColors, [type]: color }));
	};

	return (
		//<Draggable>
		<div className="qadpt-designpopup qadpt-imgset">
			<div className="qadpt-content">
				<div className="qadpt-design-header">
					<IconButton
						aria-label="close"
						onClick={handleClose}
					>
						<ArrowBackIosNewOutlinedIcon />
					</IconButton>
					<div className="qadpt-title">Image Properties</div>
					<IconButton
						size="small"
						aria-label="close"
						onClick={handleClose}
					>
						<CloseIcon />
					</IconButton>
				</div>
				<div className="qadpt-prop-section">
					<Typography
						sx={{ marginBottom: "10px" }}
					>Image Actions</Typography>
					<select
						id="action"
						value={action}
						className="qadpt-actions"
						onChange={(e) => setAction(e.target.value)}
					>
						<option value="Open URL">None</option>
						<option value="Another Action">new</option>
					</select>
				</div>
				Image Formatting
				<div className="qadpt-prop-section">
					{/* <button
						className={`tab-btn ${openInNewTab ? "active" : ""}`}
						onClick={() => setOpenInNewTab(true)}
					>
						New Tab
					</button> */}
					<Button
						onClick={() => setOpenInNewTab(true)}
						style={{ border: "1px solid var(--Theme-accentColor)" }}
					>
						Fill
					</Button>
					<Button
						onClick={() => setOpenInNewTab(true)}
						style={{ border: "1px solid var(--Theme-accentColor)" }}
					>
						Fit
					</Button>
				</div>
				{/* <div className="properties-section">
					<Typography>Enter URL</Typography>
					<input
						type="text"
						id="url"
						placeholder="http://www.example.com"
						value={url}
						onChange={(e) => setUrl(e.target.value)}
					/>
				</div> */}
			</div>
		</div>
		//</Draggable>
	);
};

export default ButtonSettings;
