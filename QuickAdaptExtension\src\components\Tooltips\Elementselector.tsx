import React, { useEffect, useState } from 'react';

interface ElementSelectionProps {
  onElementSelected?: (element: HTMLElement | null) => void;
}

const ElementSelection: React.FC<ElementSelectionProps> = ({ onElementSelected }) => {
  const [highlightedElement, setHighlightedElement] = useState<HTMLElement | null>(null);

  useEffect(() => {
    const handleMouseOver = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      
      // Set the highlighted element
      if (highlightedElement !== target) {
        setHighlightedElement(target);
        highlightElement(target);
      }
    };

    const handleMouseOut = () => {
      // Remove the highlight when the mouse is out
      removeHighlight();
    };

    // Attach event listeners to the document
    document.addEventListener('mouseover', handleMouseOver);
    document.addEventListener('mouseout', handleMouseOut);

    // Cleanup event listeners on component unmount
    return () => {
      document.removeEventListener('mouseover', handleMouseOver);
      document.removeEventListener('mouseout', handleMouseOut);
    };
  }, [highlightedElement]);

  const highlightElement = (element: HTMLElement) => {
    // Add a red border or custom styling to highlight the element
    // element.style.outline = '2px solid red';
    element.style.transition = 'outline 0.2s ease-in-out';
    if (onElementSelected) onElementSelected(element);
  };

  const removeHighlight = () => {
    if (highlightedElement) {
      // Remove the highlight
      highlightedElement.style.outline = 'none';
    }
    setHighlightedElement(null);
    if (onElementSelected) onElementSelected(null);
  };

  return null;
};

export default ElementSelection;
