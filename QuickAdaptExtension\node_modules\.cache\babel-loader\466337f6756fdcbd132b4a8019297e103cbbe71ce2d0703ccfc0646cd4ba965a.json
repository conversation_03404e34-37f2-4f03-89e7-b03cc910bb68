{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideDesign\\\\CustomCss.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { But<PERSON>, TextField, IconButton } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\n// import Draggable from \"react-draggable\";\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\nimport './Canvas.module.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CustomCSS = () => {\n  _s();\n  const [cssCode, setCssCode] = useState(\"\"); // State to store the CSS code\n  const [isOpen, setIsOpen] = useState(true);\n  const handleClose = () => {\n    setIsOpen(false); // Close the popup when close button is clicked\n  };\n  const handleCodeChange = event => {\n    setCssCode(event.target.value); // Update the state with the input value\n  };\n  const handleSave = () => {\n    // Handle the save functionality, e.g., sending the cssCode to an API or saving locally\n  };\n  if (!isOpen) return null;\n  return (\n    /*#__PURE__*/\n    //<Draggable>\n    _jsxDEV(\"div\", {\n      className: \"qadpt-designpopup\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-design-header\",\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            \"aria-label\": \"close\",\n            onClick: handleClose,\n            children: /*#__PURE__*/_jsxDEV(ArrowBackIosNewOutlinedIcon, {\n              className: \"qadpt-design-back\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 7\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 4\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-title\",\n            children: \"Custom CSS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 6\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            \"aria-label\": \"close\",\n            onClick: handleClose,\n            children: /*#__PURE__*/_jsxDEV(CloseIcon, {\n              className: \"qadpt-design-close\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 7\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 4\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 4\n        }, this), /*#__PURE__*/_jsxDEV(TextField\n        //label=\"Paste or Write Custom CSS\"\n        , {\n          variant: \"outlined\",\n          fullWidth: true,\n          multiline: true,\n          minRows: 10 // Minimum rows to display, making it a large text area\n          ,\n          maxRows: 20 // Max rows to avoid excessive stretching\n          ,\n          value: cssCode // Bind the value to the state\n          ,\n          onChange: handleCodeChange // Handle changes in the text area\n          ,\n          className: \"qadpt-customfield\"\n          // sx={{\n          // \tbackgroundColor: \"#EAE2E2\",\n          // \tborderRadius: \"8px\",\n          // \tmarginBottom: \"16px\",\n          // \theight: \"340px\",\n          // \t\"& .MuiOutlinedInput-root\": {\n          // \t\theight: \"100%\",\n          // \t\t\"& fieldset\": {\n          // \t\t\tborderColor: \"#67a1a3\", // Custom border color\n          // \t\t},\n          // \t\t\"&:hover fieldset\": {\n          // \t\t\tborderColor: \"#495e58\",\n          // \t\t},\n          // \t},\n          // }}\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          fullWidth: true,\n          onClick: handleSave // Call handleSave when the button is clicked\n          ,\n          className: \"qadpt-btn\"\n          // sx={{\n          // \tbackgroundColor: \"#67a1a3\",\n          // \tcolor: \"#fff\",\n          // \tborderRadius: \"8px\",\n          // \ttextTransform: \"none\",\n          // \t\"&:hover\": {\n          // \t\tbackgroundColor: \"#568a8a\", // Darker color on hover\n          // \t},\n          // \tposition: \"relative\",\n          // \ttop: \"20px\",\n          // }}\n          ,\n          children: \"Save\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 4\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 3\n    }, this)\n    //</Draggable>\n  );\n};\n_s(CustomCSS, \"TEdRRvjGA72x9/JGmGNujpS6+Ow=\");\n_c = CustomCSS;\nexport default CustomCSS;\nvar _c;\n$RefreshReg$(_c, \"CustomCSS\");", "map": {"version": 3, "names": ["React", "useState", "<PERSON><PERSON>", "TextField", "IconButton", "CloseIcon", "ArrowBackIosNewOutlinedIcon", "jsxDEV", "_jsxDEV", "CustomCSS", "_s", "cssCode", "setCssCode", "isOpen", "setIsOpen", "handleClose", "handleCodeChange", "event", "target", "value", "handleSave", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "variant", "fullWidth", "multiline", "minRows", "maxRows", "onChange", "_c", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/guideDesign/CustomCss.tsx"], "sourcesContent": ["import React, { useState } from \"react\";\r\nimport { Box, Button, <PERSON>po<PERSON>, Text<PERSON>ield, IconButton } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\n// import Draggable from \"react-draggable\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport './Canvas.module.css';\r\nconst CustomCSS = () => {\r\n\tconst [cssCode, setCssCode] = useState(\"\"); // State to store the CSS code\r\n\tconst [isOpen, setIsOpen] = useState(true);\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetIsOpen(false); // Close the popup when close button is clicked\r\n\t};\r\n\tconst handleCodeChange = (event: any) => {\r\n\t\tsetCssCode(event.target.value); // Update the state with the input value\r\n\t};\r\n\r\n\tconst handleSave = () => {\r\n\t\t// Handle the save functionality, e.g., sending the cssCode to an API or saving locally\r\n\t\t\r\n\t};\r\n\tif (!isOpen) return null;\r\n\r\n\treturn (\r\n\t\t//<Draggable>\r\n\t\t<div className=\"qadpt-designpopup\">\r\n\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t<IconButton\r\n\t\t\t\taria-label=\"close\"\r\n\t\t\t\tonClick={handleClose}>\r\n\t\t\t\t\t\t<ArrowBackIosNewOutlinedIcon  className=\"qadpt-design-back\"/>\r\n\t\t\t\t\t\t{/* Header */}\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t<div\r\n\t\t\t\tclassName=\"qadpt-title\">\r\n\t\t\t\tCustom CSS\r\n\t\t\t</div>\r\n\t\t\t{/* Close Button */}\r\n\t\t\t<IconButton size=\"small\" aria-label=\"close\" onClick={handleClose}>\r\n\t\t\t\t\t\t<CloseIcon className=\"qadpt-design-close\"/>\r\n    </IconButton>\r\n\r\n\t\t</div>\r\n\r\n\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t//label=\"Paste or Write Custom CSS\"\r\n\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\tmultiline\r\n\t\t\t\t\t\tminRows={10} // Minimum rows to display, making it a large text area\r\n\t\t\t\t\t\tmaxRows={20} // Max rows to avoid excessive stretching\r\n\t\t\t\t\t\tvalue={cssCode} // Bind the value to the state\r\n\t\t\t\t\tonChange={handleCodeChange} // Handle changes in the text area\r\n\t\t\t\t\tclassName=\"qadpt-customfield\"\r\n\t\t\t\t\t\t// sx={{\r\n\t\t\t\t\t\t// \tbackgroundColor: \"#EAE2E2\",\r\n\t\t\t\t\t\t// \tborderRadius: \"8px\",\r\n\t\t\t\t\t\t// \tmarginBottom: \"16px\",\r\n\t\t\t\t\t\t// \theight: \"340px\",\r\n\t\t\t\t\t\t// \t\"& .MuiOutlinedInput-root\": {\r\n\t\t\t\t\t\t// \t\theight: \"100%\",\r\n\t\t\t\t\t\t// \t\t\"& fieldset\": {\r\n\t\t\t\t\t\t// \t\t\tborderColor: \"#67a1a3\", // Custom border color\r\n\t\t\t\t\t\t// \t\t},\r\n\t\t\t\t\t\t// \t\t\"&:hover fieldset\": {\r\n\t\t\t\t\t\t// \t\t\tborderColor: \"#495e58\",\r\n\t\t\t\t\t\t// \t\t},\r\n\t\t\t\t\t\t// \t},\r\n\t\t\t\t\t\t// }}\r\n\t\t\t\t\t/>\r\n\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\tonClick={handleSave} // Call handleSave when the button is clicked\r\n\t\t\t\t\tclassName=\"qadpt-btn\"\r\n\t\t\t\t\t\t// sx={{\r\n\t\t\t\t\t\t// \tbackgroundColor: \"#67a1a3\",\r\n\t\t\t\t\t\t// \tcolor: \"#fff\",\r\n\t\t\t\t\t\t// \tborderRadius: \"8px\",\r\n\t\t\t\t\t\t// \ttextTransform: \"none\",\r\n\t\t\t\t\t\t// \t\"&:hover\": {\r\n\t\t\t\t\t\t// \t\tbackgroundColor: \"#568a8a\", // Darker color on hover\r\n\t\t\t\t\t\t// \t},\r\n\t\t\t\t\t\t// \tposition: \"relative\",\r\n\t\t\t\t\t\t// \ttop: \"20px\",\r\n\t\t\t\t\t\t// }}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\tSave\r\n\t\t\t\t\t</Button>\r\n\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t//</Draggable>\r\n\t);\r\n};\r\n\r\nexport default CustomCSS;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAAcC,MAAM,EAAcC,SAAS,EAAEC,UAAU,QAAQ,eAAe;AAC9E,OAAOC,SAAS,MAAM,2BAA2B;AACjD;AACA,OAAOC,2BAA2B,MAAM,6CAA6C;AACrF,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAC7B,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAC5C,MAAM,CAACY,MAAM,EAAEC,SAAS,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAE1C,MAAMc,WAAW,GAAGA,CAAA,KAAM;IACzBD,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;EACnB,CAAC;EACD,MAAME,gBAAgB,GAAIC,KAAU,IAAK;IACxCL,UAAU,CAACK,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;EACjC,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACxB;EAAA,CAEA;EACD,IAAI,CAACP,MAAM,EAAE,OAAO,IAAI;EAExB;IAAA;IACC;IACAL,OAAA;MAAKa,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eACjCd,OAAA;QAAKa,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC9Bd,OAAA;UAAKa,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBACpCd,OAAA,CAACJ,UAAU;YACV,cAAW,OAAO;YAClBmB,OAAO,EAAER,WAAY;YAAAO,QAAA,eACnBd,OAAA,CAACF,2BAA2B;cAAEe,SAAS,EAAC;YAAmB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAElD,CAAC,eACbnB,OAAA;YACDa,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAEzB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAENnB,OAAA,CAACJ,UAAU;YAACwB,IAAI,EAAC,OAAO;YAAC,cAAW,OAAO;YAACL,OAAO,EAAER,WAAY;YAAAO,QAAA,eAC9Dd,OAAA,CAACH,SAAS;cAACgB,SAAS,EAAC;YAAoB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV,CAAC,eAEHnB,OAAA,CAACL;QACA;QAAA;UACA0B,OAAO,EAAC,UAAU;UAClBC,SAAS;UACTC,SAAS;UACTC,OAAO,EAAE,EAAG,CAAC;UAAA;UACbC,OAAO,EAAE,EAAG,CAAC;UAAA;UACbd,KAAK,EAAER,OAAQ,CAAC;UAAA;UACjBuB,QAAQ,EAAElB,gBAAiB,CAAC;UAAA;UAC5BK,SAAS,EAAC;UACT;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QAAA;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEFnB,OAAA,CAACN,MAAM;UACN2B,OAAO,EAAC,WAAW;UACnBC,SAAS;UACVP,OAAO,EAAEH,UAAW,CAAC;UAAA;UACrBC,SAAS,EAAC;UACT;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UAAA;UAAAC,QAAA,EACA;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;IACN;EAAA;AAEF,CAAC;AAACjB,EAAA,CAzFID,SAAS;AAAA0B,EAAA,GAAT1B,SAAS;AA2Ff,eAAeA,SAAS;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}