import React, { useState, useEffect } from "react";
import { Popover, Button, Typography, Box, LinearProgress, DialogActions, IconButton } from "@mui/material";
import { CustomIconButton } from "./Button";
import CloseIcon from "@mui/icons-material/Close";
import { PopoverOrigin } from "@mui/material";
import useDrawerStore, { DrawerState } from "../../store/drawerStore";
import { GuideData } from "../drawer/Drawer";

interface PopupProps {
	guideStep: any[];
	anchorEl: null | HTMLElement;
	onClose: () => void;
	onPrevious: () => void;
	onContinue: () => void;
	title: string;
	text: string;
	imageUrl?: string;
	videoUrl?: string;
	previousButtonLabel: string;
	continueButtonLabel: string;
	previousButtonStyles?: {
		backgroundColor?: string;
		textColor?: string;
		borderColor?: string;
	};
	continueButtonStyles?: {
		backgroundColor?: string;
		textColor?: string;
		borderColor?: string;
	};
	currentStep: number;
	totalSteps: number;
	onDontShowAgain: () => void;
	progress: number;
	textFieldProperties?: any;
	imageProperties?: any;
	customButton?: any;
	modalProperties?: {
		InteractionWithPopup?: boolean;
		IncludeRequisiteButtons?: boolean;
		DismissOption?: boolean;
		ModalPlacedOn?: string;
	};
	canvasProperties?: {
		Position?: string;
		Padding?: string;
		Radius?: string;
		BorderSize?: string;
		BorderColor?: string;
		BackgroundColor?: string;
		Width?: string;
	};
	htmlSnippet: string;
	OverlayValue: boolean;
	savedGuideData: GuideData | null;
	hotspotProperties: any;
}

const TooltipPreview: React.FC<PopupProps> = ({
	guideStep,
	anchorEl,
	onClose,
	onPrevious,
	onContinue,
	title,
	text,
	imageUrl,
	videoUrl,
	previousButtonLabel,
	continueButtonLabel,
	currentStep,
	totalSteps,
	onDontShowAgain,
	progress,
	textFieldProperties,
	imageProperties,
	customButton,
	modalProperties,
	canvasProperties,
	htmlSnippet,
	previousButtonStyles,
	continueButtonStyles,
	OverlayValue,
	savedGuideData,
	hotspotProperties,
}) => {
	const { setCurrentStep ,selectedTemplate} = useDrawerStore((state: DrawerState) => state);
	const [Overlayvalue, setOverlayValue] = useState(false);
	const [popupPosition, setPopupPosition] = useState<{ top: number; left: number } | null>(null);
	const handleContinue = () => {
		if (selectedTemplate !== "Tour") {
			if (currentStep < totalSteps) {
				setCurrentStep(currentStep + 1);
				onContinue();
				renderNextPopup(currentStep < totalSteps);
			}
		}
	};

	const renderNextPopup = (shouldRenderNextPopup: boolean) => {
		return shouldRenderNextPopup ? (
			<TooltipPreview
				savedGuideData={savedGuideData}
				guideStep={guideStep}
				anchorEl={anchorEl}
				onClose={onClose}
				onPrevious={handlePrevious}
				onContinue={handleContinue}
				title={title}
				text={text}
				imageUrl={imageUrl}
				previousButtonLabel={previousButtonLabel}
				continueButtonLabel={continueButtonLabel}
				currentStep={currentStep + 1}
				totalSteps={totalSteps}
				onDontShowAgain={onDontShowAgain}
				progress={progress}
				textFieldProperties={savedGuideData?.GuideStep?.[currentStep]?.TextFieldProperties}
				imageProperties={savedGuideData?.GuideStep?.[currentStep]?.ImageProperties}
				customButton={
					savedGuideData?.GuideStep?.[currentStep]?.ButtonSection?.map((section: any) =>
						section.CustomButtons.map((button: any) => ({
							...button,
							ContainerId: section.Id, // Attach the container ID for grouping
						}))
					)?.reduce((acc: string | any[], curr: any) => acc.concat(curr), []) || []
				}
				modalProperties={modalProperties}
				canvasProperties={canvasProperties}
				htmlSnippet={htmlSnippet}
				OverlayValue={OverlayValue}
				hotspotProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot || {}}
			/>
		) : null;
	};

	const handlePrevious = () => {
		if (currentStep > 1) {
			setCurrentStep(currentStep - 1);
			onPrevious();
		}
	};

	//const [popupPosition, setPopupPosition] = useState<{ top: number; left: number } | null>(null);

	const xpath = guideStep?.[currentStep - 1]?.ElementPath;

	const xOffset = guideStep?.[currentStep - 1]?.Position.XAxisOffset;
	const yOffset = guideStep?.[currentStep - 1]?.Position.YAxisOffset;

	const getElementByXPath = (xpath: string) => {
		const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
		return result.singleNodeValue as HTMLElement | null;
	};

	const getElementPosition = (xpath: string, xOffset: number, yOffset: number) => {
		const element = getElementByXPath(xpath);
		if (element) {
			const rect = element.getBoundingClientRect();
			return {
				top: rect.top, //+ window.scrollY + yOffset, // Adjust for vertical scroll
				left: rect.left, //+ window.scrollX + xOffset, // Adjust for horizontal scroll
			};
		}
		return null;
	};

	useEffect(() => {
		if (typeof window !== undefined) {
			const position = getElementPosition(xpath, xOffset, yOffset);
			if (position) {
				setPopupPosition(position);
			}
		}
	}, [xpath, xOffset, yOffset]);

	useEffect(() => {
		if (OverlayValue) {
			setOverlayValue(true);
		} else {
			setOverlayValue(false);
		}
	}, [OverlayValue]);
	const imageFit = imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.Fit || "contain";
	const getAnchorAndTransformOrigins = (
		position: string
	): { anchorOrigin: PopoverOrigin; transformOrigin: PopoverOrigin } => {
		switch (position) {
			case "top-left":
				return {
					anchorOrigin: { vertical: "top", horizontal: "left" },
					transformOrigin: { vertical: "bottom", horizontal: "right" },
				};
			case "top-right":
				return {
					anchorOrigin: { vertical: "top", horizontal: "right" },
					transformOrigin: { vertical: "bottom", horizontal: "left" },
				};
			case "bottom-left":
				return {
					anchorOrigin: { vertical: "bottom", horizontal: "left" },
					transformOrigin: { vertical: "top", horizontal: "right" },
				};
			case "bottom-right":
				return {
					anchorOrigin: { vertical: "bottom", horizontal: "right" },
					transformOrigin: { vertical: "center", horizontal: "left" },
				};
			case "center-center":
				return {
					anchorOrigin: { vertical: "center", horizontal: "center" },
					transformOrigin: { vertical: "center", horizontal: "center" },
				};
			case "top-center":
				return {
					anchorOrigin: { vertical: "top", horizontal: "center" },
					transformOrigin: { vertical: "bottom", horizontal: "center" },
				};
			case "left-center":
				return {
					anchorOrigin: { vertical: "center", horizontal: "left" },
					transformOrigin: { vertical: "center", horizontal: "right" },
				};
			case "bottom-center":
				return {
					anchorOrigin: { vertical: "bottom", horizontal: "center" },
					transformOrigin: { vertical: "center", horizontal: "center" },
				};
			case "right-center":
				return {
					anchorOrigin: { vertical: "center", horizontal: "right" },
					transformOrigin: { vertical: "center", horizontal: "left" },
				};
			default:
				return {
					anchorOrigin: { vertical: "center", horizontal: "center" },
					transformOrigin: { vertical: "center", horizontal: "center" },
				};
		}
	};

	const { anchorOrigin, transformOrigin } = getAnchorAndTransformOrigins(canvasProperties?.Position || "center center");

	const textStyle = {
		fontWeight: textFieldProperties?.TextProperties?.Bold ? "bold" : "normal",
		fontStyle: textFieldProperties?.TextProperties?.Italic ? "italic" : "normal",
		color: textFieldProperties?.TextProperties?.TextColor || "#000000",
		textAlign: textFieldProperties?.Alignment || "left",
	};

	const imageStyle = {
		maxHeight: imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.MaxImageHeight || "500px",
		textAlign: imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.Alignment || "center",
		objectFit: imageFit || "contain",
		width: "100%",
		height: `${imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.SectionHeight || 250}px`,
		background: imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.BackgroundColor || "#ffffff",
	};

	const renderHtmlSnippet = (snippet: string) => {
		// Return the raw HTML snippet for rendering
		return {
			__html: snippet.replace(/(<a\s+[^>]*href=")([^"]*)("[^>]*>)/g, (match, p1, p2, p3) => {
				return `${p1}${p2}" target="_blank"${p3}`;
			}),
		};
	};

	const groupedButtons = customButton.reduce((acc: any, button: any) => {
		const containerId = button.ContainerId || "default"; // Use a ContainerId or fallback
		if (!acc[containerId]) {
			acc[containerId] = [];
		}
		acc[containerId].push(button);
		return acc;
	}, {});

	const canvasStyle = {
		position: canvasProperties?.Position || "center-center",
		padding: `${canvasProperties?.Padding}px` || "4px",
		borderRadius: `${canvasProperties?.Radius}px` || "4px",
		borderWidth: canvasProperties?.BorderSize || "0px",
		borderColor: canvasProperties?.BorderColor || "black",
		borderStyle: "solid",
		backgroundColor: canvasProperties?.BackgroundColor || "white",
		width: canvasProperties?.Width ? `${canvasProperties?.Width}px` : "300px",
	};
	const dissmissIconColor = "red";
	const ActionButtonBackgroundcolor = "#f0f0f0";
	const overlay: boolean = Overlayvalue;
	const sectionHeight = imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.SectionHeight || "auto";
	const openInNewTab = true;
	const handleButtonAction = (action: any) => {
		if (action.Action === "open-url") {
			const targetUrl = action.TargetUrl;
			if (action.ActionValue === "same-tab") {
				// Open the URL in the same tab
				window.location.href = targetUrl;
			} else {
				// Open the URL in a new tab
				window.open(targetUrl, "_blank", "noopener noreferrer");
			}
		} else {
			if (action.Action == "Previous" || action.ActionValue == "Previous") {
				handlePrevious();
			} else if (action.Action == "Next" || action.ActionValue == "Next") {
				handleContinue();
			} else if (action.Action == "Restart" || action.ActionValue == "Restart") {
				// Reset to the first step
				setCurrentStep(1);
				// If there's a specific URL for the first step, navigate to it
				if (savedGuideData?.GuideStep?.[0]?.ElementPath) {
					const firstStepElement = getElementByXPath(savedGuideData.GuideStep[0].ElementPath);
					if (firstStepElement) {
						firstStepElement.scrollIntoView({ behavior: 'smooth' });
					}
				}
			}
		}
		setOverlayValue(false);
	};
	function getAlignment(alignment: string) {
		switch (alignment) {
			case "start":
				return "flex-start";
			case "end":
				return "flex-end";
			case "center":
			default:
				return "center";
		}
	}
	const getCanvasPosition = (position: string = "center-center") => {
		switch (position) {
			case "bottom-left":
				return { top: popupPosition ? `${popupPosition.top} !important` : "auto !important" };
			case "bottom-right":
				return { top: popupPosition ? `${popupPosition.top} !important` : "auto !important" };
			case "bottom-center":
				return { top: popupPosition ? `${popupPosition.top} !important` : "auto !important" };
			case "center-center":
				return { top: popupPosition ? `${popupPosition.top} !important` : "25% !important" };
			case "left-center":
				return { top: imageUrl === "" ? "40% !important" : "20% !important" };
			case "right-center":
				return { top: popupPosition ? `${popupPosition.top} !important` : "10% !important" };
			case "top-left":
				return { top: popupPosition ? `${popupPosition.top} !important` : "10% !important" };
			case "top-right":
				return { top: popupPosition ? `${popupPosition.top} !important` : "10% !important" };
			case "top-center":
				return { top: popupPosition ? `${popupPosition.top} !important` : "9% !important" };
			default:
				return { top: popupPosition ? `${popupPosition.top} !important` : "25% !important" };
		}
	};

	return (
		<div>
			{overlay && (
				<div
					style={{
						position: "fixed",
						top: 0,
						left: 0,
						right: 0,
						bottom: 0,
						backgroundColor: "rgba(0, 0, 0, 0.5)",
						zIndex: 999,
						//pointerEvents: "none",
					}}
				/>
			)}
			<Popover
				className="previewdata qadpt-index"
				open={true}
				//anchorEl={anchorEl}
				anchorPosition={popupPosition ? { top: popupPosition.top, left: popupPosition.left } : { top: 100, left: 200 }}
				onClose={undefined}
				anchorOrigin={anchorOrigin}
				transformOrigin={transformOrigin}
				anchorReference="anchorPosition"
				// anchorPosition={{
				// 	top: anchorEl?.getBoundingClientRect().top || 0,
				// 	left: anchorEl?.getBoundingClientRect().left || 0,
				// }}
				sx={{
					"pointer-events": anchorEl ? "auto" : "auto",
					"& .MuiPaper-root": {
						zIndex: 1000,
						// borderRadius: "1px",
						...canvasStyle,
						//...getAnchorAndTransformOrigins,
						//top: "16% !important",
						maxHeight: "400px",
						// top: canvasProperties?.Position === "bottom-left" ? "auto !important" :
						//     canvasProperties?.Position === "bottom-right" ? "auto !important" :
						//         canvasProperties?.Position === "bottom-center" ? "auto !important" :
						//             canvasProperties?.Position === "center-center" ? "30% !important" :
						//                 canvasProperties?.Position === "left-center" ? (imageUrl === "" ? "40% !important" : "20% !important") :
						//                     canvasProperties?.Position === "right-center" ? "20% !important" :
						//                         canvasProperties?.Position === "top-left" ? "10% !important" :
						//                         canvasProperties?.Position === "top-center" ? "9% !important" :
						//                         canvasProperties?.Position==="top-right"?"10% !important":    "",
						...getCanvasPosition(canvasProperties?.Position || "center-center"),
						bottom:
							canvasProperties?.Position === "bottom-left"
								? " 0 !important"
								: canvasProperties?.Position === "bottom-right"
								? "0 !important"
								: canvasProperties?.Position === "bottom-center"
								? "0 !important"
								: "",
						//top:canvasProperties?.Position==="bottom left"?"146px !important":canvasProperties?.Position==="bottom right"?"188px !important":canvasProperties?.Position==="bottom center"?"165px !important":canvasProperties?.Position==="left center"?"70px !important":canvasProperties?.Position==="top center"?"3px !important":canvasProperties?.Position==="center center"?"70px !important":canvasProperties?.Position==="right center"?"75px !important":""
						//top: canvasProperties?.Position === "bottom left" ? "auto !important" : canvasProperties?.Position === "bottom right" ? "auto !important" : canvasProperties?.Position === "bottom center" ? "auto !important" : canvasProperties?.Position === "center center" ? "33% !important" : canvasProperties?.Position === "left center" ? "10% !important" : canvasProperties?.Position === "right center" ? "10% !important" : "",
						//bottom: canvasProperties?.Position === "bottom left" ? " 0 !important" : canvasProperties?.Position === "bottom right" ? "0 !important" : canvasProperties?.Position === "bottom center" ? "0 !important" : "",
						//left: canvasProperties?.Position === "center center" ? "33% !important" : canvasProperties?.Position === "bottom center" ? "35% !important" : canvasProperties?.Position === "right center" ? "63% !important" : canvasProperties?.Position === "bottom right" ? "63% !important" : canvasProperties?.Position === "top center" ? "36% !important" : canvasProperties?.Position === "top right" ? "63% !important" : null
					},
				}}
				disableScrollLock={true}
			>
					<div style={{placeContent:"end",display:"flex"}}>
					{modalProperties?.DismissOption && (
					<IconButton sx={{
						position: "fixed",
					boxShadow: "rgba(0, 0, 0, 0.06) 0px 4px 8px",
					left: "auto",
					right: "-10px",
					margin: "-15px -3px",
					background: "#fff !important",
					border: "1px solid #ccc",
					zIndex:"999999",
						borderRadius: "50px",
					padding:"5px !important"

					}}>
						<CloseIcon sx={{ zoom: 0.6,color:"#000" }} />
					</IconButton>
					)}
					</div>

				<Box style={{ height: sectionHeight }}>
					<Box
						display="flex"
						flexDirection="column"
						flexWrap="wrap"
						justifyContent="center"
					>
						{imageProperties?.map((imageProp: any, propIndex: number) =>
							imageProp.CustomImage.map((customImg: any, imgIndex: number) => (
								<Box
									key={`${imageProp.Id}-${imgIndex}`}
									component="img"
									src={customImg.Url}
									alt={customImg.AltText || "Image"}
									sx={{
										maxHeight: imageProp.MaxImageHeight || customImg.MaxImageHeight || "500px",
										textAlign: imageProp.Alignment || "center",
										objectFit: customImg.Fit || "contain",
										//  width: "500px",
										height: `${customImg.SectionHeight || 250}px`,
										background: customImg.BackgroundColor || "#ffffff",
										margin: "10px 0",
									}}
									onClick={() => {
										if (imageProp.Hyperlink) {
											const targetUrl = imageProp.Hyperlink;
											window.open(targetUrl, "_blank", "noopener noreferrer");
										}
									}}
									style={{ cursor: imageProp.Hyperlink ? "pointer" : "default" }}
								/>
							))
						)}
					</Box>

					{textFieldProperties?.map(
						(textField: any, index: any) =>
							textField.Text && (
								<Typography
									key={textField.Id || index} // Use a unique key, either Id or index
									sx={{
										textAlign: textField.TextProperties?.TextFormat || textStyle.textAlign,
										marginTop: 1,
										color: textField.TextProperties?.TextColor || textStyle.color,

										padding: `${canvasProperties?.Padding}px` || "4px",
									}}
									dangerouslySetInnerHTML={renderHtmlSnippet(textField.Text)} // Render the raw HTML
								/>
							)
					)}

					{Object.keys(groupedButtons).map((containerId) => (
						<Box
							key={containerId}
							sx={{
								display: "flex",
								justifyContent: getAlignment(groupedButtons[containerId][0]?.Alignment),
								flexWrap: "wrap",
								margin: 0,
								backgroundColor: groupedButtons[containerId][0]?.BackgroundColor,
								padding: "10px",
							}}
						>
							{groupedButtons[containerId].map((button: any, index: number) => (
								<Button
									key={index}
									onClick={() => handleButtonAction(button.ButtonAction)}
									variant="contained"
									sx={{
										marginRight: "13px",
										margin: "0 5px",
										backgroundColor: button.ButtonProperties?.ButtonBackgroundColor || "#007bff",
										color: button.ButtonProperties?.ButtonTextColor || "#fff",
										border: button.ButtonProperties?.BorderColor || "transparent",
										fontSize: button.ButtonProperties?.FontSize || "15px",
										width: button.ButtonProperties?.Width || "auto",
										paddingTop: "3px",
										paddingRight: "16px",
										paddingBottom: "3x",
										paddingLeft: "16px",
										textTransform: "none",
										borderRadius: button.ButtonProperties?.BorderRadius || "15px",
									}}
								>
									{button.ButtonName}
								</Button>
							))}
						</Box>
					))}
				</Box>

				{/* Render Step Progress */}
				{totalSteps > 1 ? (
					<>
						<Box>
							<LinearProgress
								variant="determinate"
								value={progress}
							/>
						</Box>
						<Box
							display="flex"
							justifyContent="initial"
							mt={2}
						>
							<Typography variant="body2">
							Step {currentStep}/{totalSteps} Steps
							</Typography>
						</Box>
					</>
				) : null}
			</Popover>
		</div>
	);
};

export default TooltipPreview;
