const path = require('path');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const Dotenv = require('dotenv-webpack');
const webpack = require('webpack'); 
module.exports = {
  entry: {
    content: './public/content.js', // Entry point for content.js
  },
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: '[name].bundle.js',
  },
  module: {
    rules: [
      {
        test: /\.(js|jsx|ts|tsx)$/, // Add tsx and ts extensions
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader', // You can also use 'ts-loader'
          options: {
            presets: ['@babel/preset-env', '@babel/preset-react', '@babel/preset-typescript'],
          },
        },
      },
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader'],
      },
      {
        test: /\.scss$/, 
        use: [
          'style-loader',  
          'css-loader',    
          'sass-loader',   
        ],
      },      
    ],
  },
  resolve: {
    extensions: ['.js', '.jsx', '.ts', '.tsx'], // Add ts and tsx extensions
  },
  mode: 'development',
  devtool: 'inline-source-map',
  plugins: [
    new CopyWebpackPlugin({
      patterns: [
        {
          from: path.resolve(__dirname, 'dist'),
          to: path.resolve(__dirname, 'build/dist'), // Copy output from dist to build
        },
      ],
    }),
  
    new Dotenv({
      path: path.resolve(__dirname, '.env.dev'),
      systemvars:true,
    }),
      ],
};