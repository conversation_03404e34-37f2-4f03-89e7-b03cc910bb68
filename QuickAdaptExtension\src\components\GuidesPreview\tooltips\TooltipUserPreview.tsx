import React, { useState, useMemo, useEffect } from "react";
// import TooltipGuide from "../Tooltips/Tooltips";
// import { IReponse, useFetch } from "../../hooks/useFetch";
//import { useLocation } from "react-router-dom";
//import MenuBar from "../Tooltips/Menubarfortest";
import TooltipGuide from "../tooltips/tooltipguide";
import useDrawerStore, { DrawerState } from "../../../store/drawerStore";
import { GuideData } from "../../drawer/Drawer";

interface Guide {
	GuideId: string;
	DontShowAgain: boolean;
}

interface PopupProps {
	guideStep: any[];
	anchorEl: null | HTMLElement;
	onClose: () => void;
	onPrevious: () => void;
	onContinue: () => void;
	title: string;
	text: string;
	imageUrl?: string;
	videoUrl?: string;
	previousButtonLabel: string;
	continueButtonLabel: string;
	previousButtonStyles?: {
		backgroundColor?: string;
		textColor?: string;
		borderColor?: string;
	};
	continueButtonStyles?: {
		backgroundColor?: string;
		textColor?: string;
		borderColor?: string;
	};
	currentStep: number;
	totalSteps: number;
	onDontShowAgain: () => void;
	progress: number;
	textFieldProperties?: any;
	imageProperties?: any;
	customButton?: any;
	modalProperties?: {
		InteractionWithPopup?: boolean;
		IncludeRequisiteButtons?: boolean;
		DismissOption?: boolean;
		ModalPlacedOn?: string;
	};
	canvasProperties?: {
		Position?: string;
		Padding?: string;
		Radius?: string;
		BorderSize?: string;
		BorderColor?: string;
		BackgroundColor?: string;
		Width?: string;
	};
	htmlSnippet: string;
	OverlayValue: boolean;
	savedGuideData: GuideData | null;
	hotspotProperties: any;
}

const TooltipUserpreview: React.FC<PopupProps> = ({
	guideStep,
	anchorEl,
	onClose,
	onPrevious,
	onContinue,
	title,
	text,
	imageUrl,
	videoUrl,
	previousButtonLabel,
	continueButtonLabel,
	currentStep,
	totalSteps,
	onDontShowAgain,
	progress,
	textFieldProperties,
	imageProperties,
	customButton,
	modalProperties,
	canvasProperties,
	htmlSnippet,
	previousButtonStyles,
	continueButtonStyles,
	OverlayValue,
	savedGuideData,
	hotspotProperties,
}) => {
	//const location = useLocation();
	const [currentUrl, setCurrentUrl] = useState(window.location.href);
	const [showTooltip, setShowTooltip] = useState(true);
	const { setCurrentStep } = useDrawerStore((state: DrawerState) => state);
	// const [response, setResponse] = useState<IReponse>({
	// 	data: [],
	// 	loading: false,
	// 	error: {
	// 		message: "",
	// 		isError: false,
	// 	},
	// });
	// const [response] = useFetch({
	//   // url: "/Guide/GetGuideDetails?guideId=09102024-180802907-0097902d-ecb8-4333-a70a-ba151cc641a6",
	//   url: `/EndUserGuide/GetGuideListByTargetUrl?targetUrl=${currentUrl}`,
	// });
	// const [guideDetails] = useFetch({
	// 	url: `/EndUserGuide/GetGuideListByTargetUrl?targetUrl=${currentUrl}`,
	// });

	// useEffect(() => {
	// 	console.log(savedGuideData, "GuideDetails");
	// 	if (savedGuideData && savedGuideData && savedGuideData.length > 0) {
	// 		savedGuideData = savedGuideData.filter((x: any) => x.GuideType === "Tooltip" && x.TargetUrl === currentUrl);

	// 		// setResponse((prev) => ({
	// 		// 	...prev,
	// 		// 	data: savedGuideData.data,
	// 		// 	loading: savedGuideData.loading,
	// 		// 	error: savedGuideData.error,
	// 		// }));
	// 	}
	// }, [savedGuideData, currentUrl]);

	const data = savedGuideData;
	const xpathUrl = guideStep?.[currentStep - 1]?.ElementPath;
	const xOffset = guideStep?.[currentStep - 1]?.Position.XAxisOffset;
	const yOffset = guideStep?.[currentStep - 1]?.Position.YAxisOffset;
	const steps = (data?.GuideStep || []).map((step: any) => {
		const textFields = step.TextFieldProperties?.map((textField: any) => textField.Text) || [];

		const imageUrls = step.ImageProperties?.map((imageProperty: any) => imageProperty.CustomImage?.[0]?.Url) || [];
		const buttonData = step.ButtonSection?.map((buttonSection: any) => buttonSection.CustomButtons || []) || [];

		return {
			xpath: step.ElementPath,
			content: textFields,
			imageUrl: imageUrls,
			buttonData: buttonData,
			targetUrl: xpathUrl || "",
			overlay: step.Overlay,
			positionXAxisOffset: step.Position.XAxisOffset,
			positionYAxisOffset: step.Position.YAxisOffset,
		};
	});
	  

	//const tooltipConfig = data?.GuideStep?.[0]?.Tooltip || {};
	const tooltipConfig = savedGuideData?.GuideStep || {};

	const handleCloseTooltip = () => {
		setShowTooltip(false);
		const storedGuides = JSON.parse(localStorage.getItem("closedGuides_") || "[]");
		const updatedGuides = [
			...storedGuides,
			{
				GuideId: savedGuideData?.GuideId || null,
				DontShowAgain: true,
			},
		];
		localStorage.setItem("closedGuides_", JSON.stringify(updatedGuides));
	};
	useEffect(() => {
		const handleStorageChange = () => {
			const storedGuides: Guide[] = JSON.parse(localStorage.getItem("closedGuides_") || "[]");
			const isGuideClosed = storedGuides.some(
				(guide) => guide.GuideId === savedGuideData?.GuideId && guide.DontShowAgain === true
			);
			if (savedGuideData && savedGuideData?.GuideStep?.length > 0 && isGuideClosed) {
				setShowTooltip(false);
			} else if (data?.GuideStep && data.GuideStep.length > 0) {
				setShowTooltip(true);
			}
		};

		handleStorageChange();
		window.addEventListener("storage", handleStorageChange);
		return () => {
			window.removeEventListener("storage", handleStorageChange);
		};
	}, [savedGuideData, data?.GuideId, data?.GuideStep?.length]);

	return (
		<div>
			{/* <MenuBar /> */}
			{/* <div
                id="tooltip-target"
                style={{ margin: "50px", padding: "10px", border: "1px solid black" }}
            >
                Hover over me
            </div> */}
			{/* <div style={{ marginTop: "150px", textAlign: "center" }} id="tooltip-next">
                <h1>TooltipTest</h1>
            </div>
            <div style={{ marginTop: "163px", textAlign: "center" }} id="tooltip-test">
                <p>this is tooltip test</p>
            </div> */}

			{showTooltip && (
				<TooltipGuide
					steps={steps}
					currentUrl={currentUrl}
					onClose={handleCloseTooltip}
					tooltipConfig={tooltipConfig}
				/>
			)}
		</div>
	);
};

export default TooltipUserpreview;
