{"ast": null, "code": "var _jsxFileName = \"E:\\\\quixy\\\\newadopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\checklist\\\\ChecklistCanvasSettings.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Box, Typography, TextField, IconButton, Button } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\n// import Draggable from \"react-draggable\";\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\nimport useDrawerStore from \"../../store/drawerStore\";\nimport { warning } from \"../../assets/icons/icons\";\nimport { useTranslation } from 'react-i18next';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChecklistCanvasSettings = ({\n  zindeex,\n  setZindeex,\n  setShowChecklistCanvasSettings,\n  selectedTemplate\n}) => {\n  _s();\n  const {\n    t: translate\n  } = useTranslation();\n  const {\n    setCanvasSetting,\n    borderColor,\n    width,\n    setWidth,\n    backgroundColor,\n    setBorderColor,\n    setBackgroundColor,\n    borderRadius,\n    setBorderRadius,\n    Annpadding,\n    setAnnPadding,\n    AnnborderSize,\n    setAnnBorderSize,\n    Bposition,\n    setBposition,\n    checklistGuideMetaData,\n    updateChecklistCanvas,\n    setIsUnSavedChanges,\n    isUnSavedChanges,\n    setIsThemeChanges,\n    selectedTheme\n  } = useDrawerStore(state => state);\n  const [isOpen, setIsOpen] = useState(true);\n\n  // Error states for validation\n  const [heightError, setHeightError] = useState(false);\n  const [widthError, setWidthError] = useState(false);\n  const [cornerRadiusError, setCornerRadiusError] = useState(false);\n  const [borderWidthError, setBorderWidthError] = useState(false);\n  const [checklistCanvasProperties, setChecklistCanvasProperties] = useState(() => {\n    var _checklistGuideMetaDa;\n    const initialchecklistCanvasProperties = ((_checklistGuideMetaDa = checklistGuideMetaData[0]) === null || _checklistGuideMetaDa === void 0 ? void 0 : _checklistGuideMetaDa.canvas) || {\n      width: \"930\",\n      height: \"450\",\n      cornerRadius: \"12\",\n      primaryColor: \"var(--Theme-accentColor)\",\n      borderColor: \"\",\n      backgroundColor: \"#ffffff\",\n      openByDefault: false,\n      hideAfterCompletion: true,\n      borderWidth: \"0\"\n    };\n    return initialchecklistCanvasProperties;\n  });\n  // State for tracking changes and apply button\n  const [isDisabled, setIsDisabled] = useState(true);\n  const [hasChanges, setHasChanges] = useState(false);\n  const [initialState, setInitialState] = useState(checklistCanvasProperties);\n\n  // Function to check if the Apply button should be enabled\n  const updateApplyButtonState = (changed, hasErrors = false) => {\n    setIsDisabled(!changed || hasErrors);\n  };\n\n  // Effect to check for any changes compared to initial state\n  useEffect(() => {\n    // Compare current properties with initial state\n    const hasAnyChanges = JSON.stringify(checklistCanvasProperties) !== JSON.stringify(initialState);\n    setHasChanges(hasAnyChanges);\n\n    // Check for validation errors\n    const hasValidationErrors = heightError || widthError || cornerRadiusError || borderWidthError;\n    updateApplyButtonState(hasAnyChanges, hasValidationErrors);\n  }, [checklistCanvasProperties, initialState, heightError, widthError, cornerRadiusError, borderWidthError]);\n  const handleBorderColorChange = e => setBorderColor(e.target.value);\n  const handleBackgroundColorChange = e => setBackgroundColor(e.target.value);\n  const handleClose = () => {\n    setIsOpen(false);\n    setShowChecklistCanvasSettings(false);\n  };\n  const onPropertyChange = (key, value) => {\n    setChecklistCanvasProperties(prevState => {\n      const newState = {\n        ...prevState,\n        [key]: value\n      };\n      // Mark that changes have been made\n      setHasChanges(true);\n      return newState;\n    });\n  };\n  // useEffect(() => {\n\n  // \t\tconst canvas = selectedTheme?.ThemeStyles?.Canvas;\n  // \t\tconst buttons = selectedTheme?.ThemeStyles?.Buttons;\n\n  // \t\t\tconst checklistCanvasPropertie =  {\n  // \t\t\t\t\t\twidth: \"930\",\n  // \t\t\t\t\t\theight: \"450\",\n  // \t\t\t\t\t\tcornerRadius: canvas.canvasRadius,\n  // \t\t\t\t\t\tprimaryColor: buttons.primaryBtnBg,\n  // \t\t\t\t\t\t\t\tborderColor:  canvas.canvasBorderColor,\n  // \t\t\t\t\t\tbackgroundColor: canvas.canvasBgColor,\n  // \t\t\t\t\t\t\t\topenByDefault: false,\n  // \t\t\t\t\t\t\t\thideAfterCompletion: true,\n  // \t\t\t\t\t\tborderWidth:canvas.canvasBorderSize\n  // \t\t\t\t\t\t\t};\n  // \t\t\t\t\t\t\tupdateChecklistCanvas(checklistCanvasPropertie);\n  // \t\t\t\t\t\t\tsetChecklistCanvasProperties(checklistCanvasPropertie);\n\n  // \t  }, [ selectedTheme]);\n  const handleApplyChanges = () => {\n    // Apply the changes - updateChecklistCanvas will handle recording the change for undo/redo\n    updateChecklistCanvas(checklistCanvasProperties);\n    // Update the initial state to the current state after applying changes\n    setInitialState({\n      ...checklistCanvasProperties\n    });\n    // Reset the changes flag\n    setHasChanges(false);\n    // Disable the Apply button\n    setIsDisabled(true);\n    handleClose();\n    setIsUnSavedChanges(true);\n    setIsThemeChanges(true);\n  };\n  if (!isOpen) return null;\n  return (\n    /*#__PURE__*/\n    //<Draggable>\n    _jsxDEV(\"div\", {\n      id: \"qadpt-designpopup\",\n      className: \"qadpt-designpopup\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"qadpt-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-design-header\",\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            \"aria-label\": \"close\",\n            onClick: handleClose,\n            children: /*#__PURE__*/_jsxDEV(ArrowBackIosNewOutlinedIcon, {\n              className: \"qadpt-design-back\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 7\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 6\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-title\",\n            children: translate('Canvas')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 6\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            \"aria-label\": \"close\",\n            onClick: handleClose,\n            children: /*#__PURE__*/_jsxDEV(CloseIcon, {\n              className: \"qadpt-design-close\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 7\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 6\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 5\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-canblock\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-controls\",\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate('Height')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  variant: \"outlined\",\n                  value: checklistCanvasProperties.height,\n                  size: \"small\",\n                  autoFocus: true,\n                  className: \"qadpt-control-input\",\n                  onChange: e => {\n                    // Only allow numeric input\n                    const value = e.target.value;\n                    if (value === '') {\n                      onPropertyChange(\"height\", '0');\n                      setHeightError(false);\n                      return;\n                    }\n                    if (!/^-?\\d*$/.test(value)) {\n                      return;\n                    }\n                    const inputValue = parseInt(value) || 0;\n\n                    // Validate height between 100px and 1000px\n                    if (inputValue < 400 || inputValue > 600) {\n                      setHeightError(true);\n                    } else {\n                      setHeightError(false);\n                    }\n                    onPropertyChange(\"height\", value);\n                  },\n                  InputProps: {\n                    endAdornment: \"px\",\n                    sx: {\n                      \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"& fieldset\": {\n                        border: \"none\"\n                      }\n                    }\n                  },\n                  error: heightError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 7\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 6\n            }, this), heightError && /*#__PURE__*/_jsxDEV(Typography, {\n              style: {\n                fontSize: \"12px\",\n                color: \"#e9a971\",\n                textAlign: \"left\",\n                top: \"100%\",\n                left: 0,\n                marginBottom: \"5px\",\n                display: \"flex\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  display: \"flex\",\n                  fontSize: \"12px\",\n                  alignItems: \"center\",\n                  marginRight: \"4px\"\n                },\n                dangerouslySetInnerHTML: {\n                  __html: warning\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 8\n              }, this), translate('Value must be between 400px and 600px.')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 7\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate('Width')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  variant: \"outlined\",\n                  value: checklistCanvasProperties.width,\n                  size: \"small\",\n                  autoFocus: true,\n                  className: \"qadpt-control-input\",\n                  onChange: e => {\n                    // Only allow numeric input\n                    const value = e.target.value;\n                    if (value === '') {\n                      onPropertyChange(\"width\", '0');\n                      setWidthError(false);\n                      return;\n                    }\n                    if (!/^-?\\d*$/.test(value)) {\n                      return;\n                    }\n                    const inputValue = parseInt(value) || 0;\n\n                    // Validate width between 300px and 1200px\n                    if (inputValue < 300 || inputValue > 1200) {\n                      setWidthError(true);\n                    } else {\n                      setWidthError(false);\n                    }\n                    onPropertyChange(\"width\", value);\n                  },\n                  InputProps: {\n                    endAdornment: \"px\",\n                    sx: {\n                      \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"& fieldset\": {\n                        border: \"none\"\n                      }\n                    }\n                  },\n                  error: widthError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 7\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 25\n            }, this), widthError && /*#__PURE__*/_jsxDEV(Typography, {\n              style: {\n                fontSize: \"12px\",\n                color: \"#e9a971\",\n                textAlign: \"left\",\n                top: \"100%\",\n                left: 0,\n                marginBottom: \"5px\",\n                display: \"flex\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  display: \"flex\",\n                  fontSize: \"12px\",\n                  alignItems: \"center\",\n                  marginRight: \"4px\"\n                },\n                dangerouslySetInnerHTML: {\n                  __html: warning\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 8\n              }, this), translate('Value must be between 300px and 1200px.')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 7\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate('Corner Radius')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  variant: \"outlined\",\n                  value: checklistCanvasProperties.cornerRadius,\n                  fullWidth: true,\n                  size: \"small\",\n                  className: \"qadpt-control-input\",\n                  onChange: e => {\n                    // Only allow numeric input\n                    const value = e.target.value;\n                    if (value === '') {\n                      onPropertyChange(\"cornerRadius\", '0');\n                      setCornerRadiusError(false);\n                      return;\n                    }\n                    if (!/^-?\\d*$/.test(value)) {\n                      return;\n                    }\n                    const inputValue = parseInt(value) || 0;\n\n                    // Validate corner radius between 0px and 50px\n                    if (inputValue < 0 || inputValue > 50) {\n                      setCornerRadiusError(true);\n                    } else {\n                      setCornerRadiusError(false);\n                    }\n                    onPropertyChange(\"cornerRadius\", value);\n                  },\n                  InputProps: {\n                    endAdornment: \"px\",\n                    sx: {\n                      \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"& fieldset\": {\n                        border: \"none\"\n                      }\n                    }\n                  },\n                  error: cornerRadiusError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 7\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 6\n            }, this), cornerRadiusError && /*#__PURE__*/_jsxDEV(Typography, {\n              style: {\n                fontSize: \"12px\",\n                color: \"#e9a971\",\n                textAlign: \"left\",\n                top: \"100%\",\n                left: 0,\n                marginBottom: \"5px\",\n                display: \"flex\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  display: \"flex\",\n                  fontSize: \"12px\",\n                  alignItems: \"center\",\n                  marginRight: \"4px\"\n                },\n                dangerouslySetInnerHTML: {\n                  __html: warning\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 8\n              }, this), translate('Value must be between 0px and 50px.')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 352,\n              columnNumber: 7\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate('Border Width')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  variant: \"outlined\",\n                  value: checklistCanvasProperties.borderWidth,\n                  fullWidth: true,\n                  size: \"small\",\n                  className: \"qadpt-control-input\",\n                  onChange: e => {\n                    // Only allow numeric input\n                    const value = e.target.value;\n                    if (value === '') {\n                      onPropertyChange(\"borderWidth\", '0');\n                      setBorderWidthError(false);\n                      return;\n                    }\n                    if (!/^-?\\d*$/.test(value)) {\n                      return;\n                    }\n                    const inputValue = parseInt(value) || 0;\n\n                    // Validate border width between 0px and 20px\n                    if (inputValue < 0 || inputValue > 20) {\n                      setBorderWidthError(true);\n                    } else {\n                      setBorderWidthError(false);\n                    }\n                    onPropertyChange(\"borderWidth\", value);\n                  },\n                  InputProps: {\n                    endAdornment: \"px\",\n                    sx: {\n                      \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                        border: \"none\"\n                      },\n                      \"& fieldset\": {\n                        border: \"none\"\n                      }\n                    }\n                  },\n                  error: borderWidthError\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 7\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 7\n            }, this), borderWidthError && /*#__PURE__*/_jsxDEV(Typography, {\n              style: {\n                fontSize: \"12px\",\n                color: \"#e9a971\",\n                textAlign: \"left\",\n                top: \"100%\",\n                left: 0,\n                marginBottom: \"5px\",\n                display: \"flex\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  display: \"flex\",\n                  fontSize: \"12px\",\n                  alignItems: \"center\",\n                  marginRight: \"4px\"\n                },\n                dangerouslySetInnerHTML: {\n                  __html: warning\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 8\n              }, this), translate('Value must be between 0px and 20px.')]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 7\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate('Primary Color')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"color\",\n                  value: checklistCanvasProperties.primaryColor,\n                  onChange: e => onPropertyChange(\"primaryColor\", e.target.value),\n                  className: \"qadpt-color-input\",\n                  style: {\n                    backgroundColor: 'var(--Theme-accentColor)'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 7\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 438,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate('Background')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"color\",\n                  value: checklistCanvasProperties.backgroundColor,\n                  onChange: e => onPropertyChange(\"backgroundColor\", e.target.value),\n                  className: \"qadpt-color-input\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 7\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 6\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate('Border')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"color\",\n                  value: checklistCanvasProperties.borderColor,\n                  onChange: e => onPropertyChange(\"borderColor\", e.target.value),\n                  className: \"qadpt-color-input\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 466,\n                  columnNumber: 7\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 465,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 6\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate('Open by Default')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"toggle-switch\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: checklistCanvasProperties.openByDefault,\n                    onChange: e => onPropertyChange(\"openByDefault\", e.target.checked),\n                    name: \"showByDefault\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 479,\n                    columnNumber: 33\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"slider\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 485,\n                    columnNumber: 33\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 9\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 7\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              className: \"qadpt-control-box\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"qadpt-control-label\",\n                children: translate('Hide After Completion')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 8\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"toggle-switch\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    checked: checklistCanvasProperties.hideAfterCompletion,\n                    onChange: e => onPropertyChange(\"hideAfterCompletion\", e.target.checked),\n                    name: \"showByDefault\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 494,\n                    columnNumber: 33\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"slider\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 500,\n                    columnNumber: 33\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 493,\n                  columnNumber: 9\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 492,\n                columnNumber: 8\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 25\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 5\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 5\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-drawerFooter\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            onClick: handleApplyChanges,\n            className: `qadpt-btn ${isDisabled ? \"disabled\" : \"\"}`,\n            disabled: isDisabled,\n            children: translate('Apply')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 5\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 4\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 3\n    }, this)\n    //</Draggable>\n  );\n};\n_s(ChecklistCanvasSettings, \"i+jetkHS0tvigisp7icnavsNC8w=\", false, function () {\n  return [useTranslation, useDrawerStore];\n});\n_c = ChecklistCanvasSettings;\nexport default ChecklistCanvasSettings;\nvar _c;\n$RefreshReg$(_c, \"ChecklistCanvasSettings\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Box", "Typography", "TextField", "IconButton", "<PERSON><PERSON>", "CloseIcon", "ArrowBackIosNewOutlinedIcon", "useDrawerStore", "warning", "useTranslation", "jsxDEV", "_jsxDEV", "ChecklistCanvasSettings", "zindeex", "setZindeex", "setShowChecklistCanvasSettings", "selectedTemplate", "_s", "t", "translate", "setCanvasSetting", "borderColor", "width", "<PERSON><PERSON><PERSON><PERSON>", "backgroundColor", "setBorderColor", "setBackgroundColor", "borderRadius", "setBorderRadius", "Annpadding", "setAnnPadding", "AnnborderSize", "setAnnBorderSize", "Bposition", "setBposition", "checklistGuideMetaData", "updateChecklistCanvas", "setIsUnSavedChanges", "isUnSavedChanges", "setIsThemeChanges", "selectedTheme", "state", "isOpen", "setIsOpen", "heightError", "setHeightError", "widthError", "setWidthError", "cornerRadiusError", "setCornerRadiusError", "borderWidthError", "setBorderWidthError", "checklistCanvasProperties", "setChecklistCanvasProperties", "_checklistGuideMetaDa", "initialchecklistCanvasProperties", "canvas", "height", "cornerRadius", "primaryColor", "openByDefault", "hideAfterCompletion", "borderWidth", "isDisabled", "setIsDisabled", "has<PERSON><PERSON><PERSON>", "set<PERSON>as<PERSON><PERSON><PERSON>", "initialState", "setInitialState", "updateApplyButtonState", "changed", "hasErrors", "hasAnyChanges", "JSON", "stringify", "hasValidationErrors", "handleBorderColorChange", "e", "target", "value", "handleBackgroundColorChange", "handleClose", "onPropertyChange", "key", "prevState", "newState", "handleApplyChanges", "id", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "variant", "autoFocus", "onChange", "test", "inputValue", "parseInt", "InputProps", "endAdornment", "sx", "border", "error", "style", "fontSize", "color", "textAlign", "top", "left", "marginBottom", "display", "alignItems", "marginRight", "dangerouslySetInnerHTML", "__html", "fullWidth", "type", "checked", "name", "disabled", "_c", "$RefreshReg$"], "sources": ["E:/quixy/newadopt/quickadapt/QuickAdaptExtension/src/components/checklist/ChecklistCanvasSettings.tsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { Box, Typography, TextField, Grid, IconButton, Button, Tooltip } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport RadioButtonUncheckedIcon from \"@mui/icons-material/RadioButtonUnchecked\";\r\nimport RadioButtonCheckedIcon from \"@mui/icons-material/RadioButtonChecked\";\r\n// import Draggable from \"react-draggable\";\r\nimport ArrowBackIosNewOutlinedIcon from \"@mui/icons-material/ArrowBackIosNewOutlined\";\r\nimport useDrawerStore from \"../../store/drawerStore\";\r\nimport { defaultDots, topLeft, topCenter, topRight, middleLeft, middleCenter, middleRight, bottomLeft, bottomMiddle, bottomRight, topcenter, warning } from \"../../assets/icons/icons\";\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nconst ChecklistCanvasSettings = ({ zindeex, setZindeex, setShowChecklistCanvasSettings, selectedTemplate }: any) => {\r\n\tconst { t: translate } = useTranslation();\r\n\tconst {\r\n\t\tsetCanvasSetting,\r\n\t\tborderColor,\r\n\t\twidth,\r\n\t\tsetWidth,\r\n\t\tbackgroundColor,\r\n\t\tsetBorderColor,\r\n\t\tsetBackgroundColor,\r\n\t\tborderRadius,\r\n\t\tsetBorderRadius,\r\n\t\tAnnpadding,\r\n\t\tsetAnnPadding,\r\n\t\tAnnborderSize,\r\n\t\tsetAnnBorderSize,\r\n\t\tBposition,\r\n\t\tsetBposition,\r\n\t\tchecklistGuideMetaData,\r\n\t\tupdateChecklistCanvas,\r\n\t\tsetIsUnSavedChanges,\r\n\t\tisUnSavedChanges,\r\n\t\tsetIsThemeChanges,\r\n\t\tselectedTheme\r\n\t} = useDrawerStore((state: any) => state);\r\n\r\n\tconst [isOpen, setIsOpen] = useState(true);\r\n\r\n\t// Error states for validation\r\n\tconst [heightError, setHeightError] = useState(false);\r\n\tconst [widthError, setWidthError] = useState(false);\r\n\tconst [cornerRadiusError, setCornerRadiusError] = useState(false);\r\n\tconst [borderWidthError, setBorderWidthError] = useState(false);\r\n\r\n\r\n\tconst [checklistCanvasProperties, setChecklistCanvasProperties] = useState<any>(() => {\r\n\t\tconst initialchecklistCanvasProperties = checklistGuideMetaData[0]?.canvas || {\r\n\twidth: \"930\",\r\n\theight: \"450\",\r\n\tcornerRadius: \"12\",\r\n\tprimaryColor: \"var(--Theme-accentColor)\",\r\n\t\t\tborderColor: \"\",\r\n\tbackgroundColor: \"#ffffff\",\r\n\t\t\topenByDefault: false,\r\n\t\t\thideAfterCompletion: true,\r\n\tborderWidth:\"0\"\r\n\t\t};\r\n\t\treturn initialchecklistCanvasProperties;\r\n\t});\r\n\t// State for tracking changes and apply button\r\n\tconst [isDisabled, setIsDisabled] = useState(true);\r\n\tconst [hasChanges, setHasChanges] = useState(false);\r\n\tconst [initialState, setInitialState] = useState(checklistCanvasProperties);\r\n\r\n\t// Function to check if the Apply button should be enabled\r\n\tconst updateApplyButtonState = (changed: boolean, hasErrors: boolean = false) => {\r\n\t\tsetIsDisabled(!changed || hasErrors);\r\n\t};\r\n\r\n\t// Effect to check for any changes compared to initial state\r\n\tuseEffect(() => {\r\n\t\t// Compare current properties with initial state\r\n\t\tconst hasAnyChanges = JSON.stringify(checklistCanvasProperties) !== JSON.stringify(initialState);\r\n\t\tsetHasChanges(hasAnyChanges);\r\n\r\n\t\t// Check for validation errors\r\n\t\tconst hasValidationErrors = heightError || widthError || cornerRadiusError || borderWidthError;\r\n\r\n\t\tupdateApplyButtonState(hasAnyChanges, hasValidationErrors);\r\n\t}, [checklistCanvasProperties, initialState, heightError, widthError, cornerRadiusError, borderWidthError]);\r\n\r\n\tconst handleBorderColorChange = (e: any) => setBorderColor(e.target.value);\r\n\tconst handleBackgroundColorChange = (e: any) => setBackgroundColor(e.target.value);\r\n\r\n\tconst handleClose = () => {\r\n\t\tsetIsOpen(false);\r\n\t\tsetShowChecklistCanvasSettings(false);\r\n\r\n\t};\r\n\tconst onPropertyChange = (key: any, value: any) => {\r\n\t\tsetChecklistCanvasProperties((prevState: any) => {\r\n\t\t\tconst newState = {\r\n\t\t\t\t...prevState,\r\n\t\t\t\t[key]: value,\r\n\t\t\t};\r\n\t\t\t// Mark that changes have been made\r\n\t\t\tsetHasChanges(true);\r\n\t\t\treturn newState;\r\n\t\t});\r\n\t};\r\n// useEffect(() => {\r\n\t  \r\n// \t\tconst canvas = selectedTheme?.ThemeStyles?.Canvas;\r\n// \t\tconst buttons = selectedTheme?.ThemeStyles?.Buttons;\r\n\r\n// \t\t\tconst checklistCanvasPropertie =  {\r\n// \t\t\t\t\t\twidth: \"930\",\r\n// \t\t\t\t\t\theight: \"450\",\r\n// \t\t\t\t\t\tcornerRadius: canvas.canvasRadius,\r\n// \t\t\t\t\t\tprimaryColor: buttons.primaryBtnBg,\r\n// \t\t\t\t\t\t\t\tborderColor:  canvas.canvasBorderColor,\r\n// \t\t\t\t\t\tbackgroundColor: canvas.canvasBgColor,\r\n// \t\t\t\t\t\t\t\topenByDefault: false,\r\n// \t\t\t\t\t\t\t\thideAfterCompletion: true,\r\n// \t\t\t\t\t\tborderWidth:canvas.canvasBorderSize\r\n// \t\t\t\t\t\t\t};\r\n// \t\t\t\t\t\t\tupdateChecklistCanvas(checklistCanvasPropertie);\r\n// \t\t\t\t\t\t\tsetChecklistCanvasProperties(checklistCanvasPropertie);\r\n\t\t\r\n// \t  }, [ selectedTheme]);\r\n\tconst handleApplyChanges = () => {\r\n\t\t// Apply the changes - updateChecklistCanvas will handle recording the change for undo/redo\r\n\t\tupdateChecklistCanvas(checklistCanvasProperties);\r\n\t\t// Update the initial state to the current state after applying changes\r\n\t\tsetInitialState({ ...checklistCanvasProperties });\r\n\t\t// Reset the changes flag\r\n\t\tsetHasChanges(false);\r\n\t\t// Disable the Apply button\r\n\t\tsetIsDisabled(true);\r\n\t\thandleClose();\r\n\t\tsetIsUnSavedChanges(true);\r\n\t\tsetIsThemeChanges(true);\r\n\t};\r\n\r\n\tif (!isOpen) return null;\r\n\r\n\treturn (\r\n\t\t//<Draggable>\r\n\t\t<div\r\n\t\t\tid=\"qadpt-designpopup\"\r\n\t\t\tclassName=\"qadpt-designpopup\"\r\n\t\t>\r\n\t\t\t<div className=\"qadpt-content\">\r\n\t\t\t\t<div className=\"qadpt-design-header\">\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<ArrowBackIosNewOutlinedIcon  className=\"qadpt-design-back\"/>\r\n\t\t\t\t\t\t{/* Header */}\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t<div className=\"qadpt-title\">{translate('Canvas')}</div>\r\n\t\t\t\t\t{/* Close Button */}\r\n\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\taria-label=\"close\"\r\n\t\t\t\t\t\tonClick={handleClose}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<CloseIcon className=\"qadpt-design-close\"/>\r\n\t\t\t\t\t</IconButton>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t{/* Position Grid */}\r\n\t\t\t\t<div className=\"qadpt-canblock\">\r\n\t\t\t\t<div className=\"qadpt-controls\">\r\n\r\n\r\n\t\t\t\t\t{/* Height Control */}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Height')}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={checklistCanvasProperties.height}\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tautoFocus\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t// Only allow numeric input\r\n\t\t\t\t\t\t\t\tconst value = e.target.value;\r\n\t\t\t\t\t\t\t\tif (value === '') {\r\n\t\t\t\t\t\t\t\t\tonPropertyChange(\"height\", '0');\r\n\t\t\t\t\t\t\t\t\tsetHeightError(false);\r\n\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tif (!/^-?\\d*$/.test(value)) {\r\n\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tconst inputValue = parseInt(value) || 0;\r\n\r\n\t\t\t\t\t\t\t\t// Validate height between 100px and 1000px\r\n\t\t\t\t\t\t\t\tif (inputValue < 400 || inputValue > 600) {\r\n\t\t\t\t\t\t\t\t\tsetHeightError(true);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tsetHeightError(false);\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tonPropertyChange(\"height\", value);\r\n\t\t\t\t\t\t\t}}\r\n\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\terror={heightError}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n                        </Box>\r\n\t\t\t\t\t{heightError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate('Value must be between 400px and 600px.')}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t)}\r\n                        <Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Width')}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={checklistCanvasProperties.width}\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tautoFocus\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t// Only allow numeric input\r\n\t\t\t\t\t\t\t\tconst value = e.target.value;\r\n\t\t\t\t\t\t\t\tif (value === '') {\r\n\t\t\t\t\t\t\t\t\tonPropertyChange(\"width\", '0');\r\n\t\t\t\t\t\t\t\t\tsetWidthError(false);\r\n\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tif (!/^-?\\d*$/.test(value)) {\r\n\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tconst inputValue = parseInt(value) || 0;\r\n\r\n\t\t\t\t\t\t\t\t// Validate width between 300px and 1200px\r\n\t\t\t\t\t\t\t\tif (inputValue < 300 || inputValue > 1200) {\r\n\t\t\t\t\t\t\t\t\tsetWidthError(true);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tsetWidthError(false);\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tonPropertyChange(\"width\", value);\r\n\t\t\t\t\t\t\t}}\r\n\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\terror={widthError}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t{widthError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate('Value must be between 300px and 1200px.')}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t)}\r\n\t\t\t\t\t{/* Corner Radius Control */}\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Corner Radius')}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={checklistCanvasProperties.cornerRadius}\r\n\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t// Only allow numeric input\r\n\t\t\t\t\t\t\t\tconst value = e.target.value;\r\n\t\t\t\t\t\t\t\tif (value === '') {\r\n\t\t\t\t\t\t\t\t\tonPropertyChange(\"cornerRadius\", '0');\r\n\t\t\t\t\t\t\t\t\tsetCornerRadiusError(false);\r\n\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tif (!/^-?\\d*$/.test(value)) {\r\n\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tconst inputValue = parseInt(value) || 0;\r\n\r\n\t\t\t\t\t\t\t\t// Validate corner radius between 0px and 50px\r\n\t\t\t\t\t\t\t\tif (inputValue < 0 || inputValue > 50) {\r\n\t\t\t\t\t\t\t\t\tsetCornerRadiusError(true);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tsetCornerRadiusError(false);\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tonPropertyChange(\"cornerRadius\", value);\r\n\t\t\t\t\t\t\t}}\r\n\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\terror={cornerRadiusError}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t{cornerRadiusError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate('Value must be between 0px and 50px.')}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Border Width')}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\tvalue={checklistCanvasProperties.borderWidth}\r\n\t\t\t\t\t\t\tfullWidth\r\n\t\t\t\t\t\t\tsize=\"small\"\r\n\t\t\t\t\t\t\tclassName=\"qadpt-control-input\"\r\n\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t// Only allow numeric input\r\n\t\t\t\t\t\t\t\tconst value = e.target.value;\r\n\t\t\t\t\t\t\t\tif (value === '') {\r\n\t\t\t\t\t\t\t\t\tonPropertyChange(\"borderWidth\", '0');\r\n\t\t\t\t\t\t\t\t\tsetBorderWidthError(false);\r\n\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tif (!/^-?\\d*$/.test(value)) {\r\n\t\t\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tconst inputValue = parseInt(value) || 0;\r\n\r\n\t\t\t\t\t\t\t\t// Validate border width between 0px and 20px\r\n\t\t\t\t\t\t\t\tif (inputValue < 0 || inputValue > 20) {\r\n\t\t\t\t\t\t\t\t\tsetBorderWidthError(true);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tsetBorderWidthError(false);\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tonPropertyChange(\"borderWidth\", value);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\tendAdornment: \"px\",\r\n\t\t\t\t\t\t\t\tsx: {\r\n\r\n\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"none\" },\r\n\t\t\t\t\t\t\t\t\t\"& fieldset\":{border:\"none\"},\r\n\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\terror={borderWidthError}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Box>\r\n\t\t\t\t\t{borderWidthError && (\r\n\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tfontSize: \"12px\",\r\n\t\t\t\t\t\t\t\tcolor: \"#e9a971\",\r\n\t\t\t\t\t\t\t\ttextAlign: \"left\",\r\n\t\t\t\t\t\t\t\ttop: \"100%\",\r\n\t\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\t\tmarginBottom: \"5px\",\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight:\"4px\" }}\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: warning }}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t{translate('Value must be between 0px and 20px.')}\r\n\t\t\t\t\t\t</Typography>\r\n\t\t\t\t\t)}\r\n\r\n                    <Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Primary Color')}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\tvalue={checklistCanvasProperties.primaryColor}\r\n\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"primaryColor\", e.target.value)}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t\tstyle={{backgroundColor:'var(--Theme-accentColor)'}}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n                        </Box>\r\n\r\n\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Background')}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\tvalue={checklistCanvasProperties.backgroundColor}\r\n\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"backgroundColor\", e.target.value)}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n                        </Box>\r\n\r\n\r\n\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Border')}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\ttype=\"color\"\r\n\t\t\t\t\t\t\tvalue={checklistCanvasProperties.borderColor}\r\n\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"borderColor\", e.target.value)}\r\n\t\t\t\t\t\t\tclassName=\"qadpt-color-input\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t<Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Open by Default')}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<label className=\"toggle-switch\">\r\n                                <input\r\n                                    type=\"checkbox\"\r\n                                    checked={checklistCanvasProperties.openByDefault}\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"openByDefault\", e.target.checked)}\r\n                                    name=\"showByDefault\"\r\n                                />\r\n                                <span className=\"slider\"></span>\r\n\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\r\n                        <Box className=\"qadpt-control-box\">\r\n\t\t\t\t\t\t\t<div className=\"qadpt-control-label\">{translate('Hide After Completion')}</div>\r\n\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t<label className=\"toggle-switch\">\r\n                                <input\r\n                                    type=\"checkbox\"\r\n                                    checked={checklistCanvasProperties.hideAfterCompletion}\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => onPropertyChange(\"hideAfterCompletion\", e.target.checked)}\r\n                                    name=\"showByDefault\"\r\n                                />\r\n                                <span className=\"slider\"></span>\r\n\t\t\t\t\t\t\t\t</label>\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</Box>\r\n\r\n\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t<div className=\"qadpt-drawerFooter\">\r\n\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\tonClick={handleApplyChanges}\r\n\t\t\t\t\t\t\tclassName={`qadpt-btn ${isDisabled ? \"disabled\" : \"\"}`}\r\n\t\t\t\t\t\t\tdisabled={isDisabled}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t{translate('Apply')}\r\n\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t</div>\r\n\t\t\t</div>\r\n\r\n\t\t</div>\r\n\t\t//</Draggable>\r\n\t);\r\n};\r\n\r\nexport default ChecklistCanvasSettings;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,UAAU,EAAEC,SAAS,EAAQC,UAAU,EAAEC,MAAM,QAAiB,eAAe;AAC7F,OAAOC,SAAS,MAAM,2BAA2B;AAGjD;AACA,OAAOC,2BAA2B,MAAM,6CAA6C;AACrF,OAAOC,cAAc,MAAM,yBAAyB;AACpD,SAA6IC,OAAO,QAAQ,0BAA0B;AACtL,SAASC,cAAc,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,uBAAuB,GAAGA,CAAC;EAAEC,OAAO;EAAEC,UAAU;EAAEC,8BAA8B;EAAEC;AAAsB,CAAC,KAAK;EAAAC,EAAA;EACnH,MAAM;IAAEC,CAAC,EAAEC;EAAU,CAAC,GAAGV,cAAc,CAAC,CAAC;EACzC,MAAM;IACLW,gBAAgB;IAChBC,WAAW;IACXC,KAAK;IACLC,QAAQ;IACRC,eAAe;IACfC,cAAc;IACdC,kBAAkB;IAClBC,YAAY;IACZC,eAAe;IACfC,UAAU;IACVC,aAAa;IACbC,aAAa;IACbC,gBAAgB;IAChBC,SAAS;IACTC,YAAY;IACZC,sBAAsB;IACtBC,qBAAqB;IACrBC,mBAAmB;IACnBC,gBAAgB;IAChBC,iBAAiB;IACjBC;EACD,CAAC,GAAGjC,cAAc,CAAEkC,KAAU,IAAKA,KAAK,CAAC;EAEzC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;;EAE1C;EACA,MAAM,CAAC6C,WAAW,EAAEC,cAAc,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC+C,UAAU,EAAEC,aAAa,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACiD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACmD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EAG/D,MAAM,CAACqD,yBAAyB,EAAEC,4BAA4B,CAAC,GAAGtD,QAAQ,CAAM,MAAM;IAAA,IAAAuD,qBAAA;IACrF,MAAMC,gCAAgC,GAAG,EAAAD,qBAAA,GAAAnB,sBAAsB,CAAC,CAAC,CAAC,cAAAmB,qBAAA,uBAAzBA,qBAAA,CAA2BE,MAAM,KAAI;MAC/ElC,KAAK,EAAE,KAAK;MACZmC,MAAM,EAAE,KAAK;MACbC,YAAY,EAAE,IAAI;MAClBC,YAAY,EAAE,0BAA0B;MACtCtC,WAAW,EAAE,EAAE;MACjBG,eAAe,EAAE,SAAS;MACxBoC,aAAa,EAAE,KAAK;MACpBC,mBAAmB,EAAE,IAAI;MAC3BC,WAAW,EAAC;IACX,CAAC;IACD,OAAOP,gCAAgC;EACxC,CAAC,CAAC;EACF;EACA,MAAM,CAACQ,UAAU,EAAEC,aAAa,CAAC,GAAGjE,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACkE,UAAU,EAAEC,aAAa,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACoE,YAAY,EAAEC,eAAe,CAAC,GAAGrE,QAAQ,CAACqD,yBAAyB,CAAC;;EAE3E;EACA,MAAMiB,sBAAsB,GAAGA,CAACC,OAAgB,EAAEC,SAAkB,GAAG,KAAK,KAAK;IAChFP,aAAa,CAAC,CAACM,OAAO,IAAIC,SAAS,CAAC;EACrC,CAAC;;EAED;EACAzE,SAAS,CAAC,MAAM;IACf;IACA,MAAM0E,aAAa,GAAGC,IAAI,CAACC,SAAS,CAACtB,yBAAyB,CAAC,KAAKqB,IAAI,CAACC,SAAS,CAACP,YAAY,CAAC;IAChGD,aAAa,CAACM,aAAa,CAAC;;IAE5B;IACA,MAAMG,mBAAmB,GAAG/B,WAAW,IAAIE,UAAU,IAAIE,iBAAiB,IAAIE,gBAAgB;IAE9FmB,sBAAsB,CAACG,aAAa,EAAEG,mBAAmB,CAAC;EAC3D,CAAC,EAAE,CAACvB,yBAAyB,EAAEe,YAAY,EAAEvB,WAAW,EAAEE,UAAU,EAAEE,iBAAiB,EAAEE,gBAAgB,CAAC,CAAC;EAE3G,MAAM0B,uBAAuB,GAAIC,CAAM,IAAKpD,cAAc,CAACoD,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAC1E,MAAMC,2BAA2B,GAAIH,CAAM,IAAKnD,kBAAkB,CAACmD,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC;EAElF,MAAME,WAAW,GAAGA,CAAA,KAAM;IACzBtC,SAAS,CAAC,KAAK,CAAC;IAChB5B,8BAA8B,CAAC,KAAK,CAAC;EAEtC,CAAC;EACD,MAAMmE,gBAAgB,GAAGA,CAACC,GAAQ,EAAEJ,KAAU,KAAK;IAClD1B,4BAA4B,CAAE+B,SAAc,IAAK;MAChD,MAAMC,QAAQ,GAAG;QAChB,GAAGD,SAAS;QACZ,CAACD,GAAG,GAAGJ;MACR,CAAC;MACD;MACAb,aAAa,CAAC,IAAI,CAAC;MACnB,OAAOmB,QAAQ;IAChB,CAAC,CAAC;EACH,CAAC;EACF;;EAEA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACC,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAChC;IACAlD,qBAAqB,CAACgB,yBAAyB,CAAC;IAChD;IACAgB,eAAe,CAAC;MAAE,GAAGhB;IAA0B,CAAC,CAAC;IACjD;IACAc,aAAa,CAAC,KAAK,CAAC;IACpB;IACAF,aAAa,CAAC,IAAI,CAAC;IACnBiB,WAAW,CAAC,CAAC;IACb5C,mBAAmB,CAAC,IAAI,CAAC;IACzBE,iBAAiB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,IAAI,CAACG,MAAM,EAAE,OAAO,IAAI;EAExB;IAAA;IACC;IACA/B,OAAA;MACC4E,EAAE,EAAC,mBAAmB;MACtBC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAE7B9E,OAAA;QAAK6E,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC7B9E,OAAA;UAAK6E,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBACnC9E,OAAA,CAACR,UAAU;YACV,cAAW,OAAO;YAClBuF,OAAO,EAAET,WAAY;YAAAQ,QAAA,eAErB9E,OAAA,CAACL,2BAA2B;cAAEkF,SAAS,EAAC;YAAmB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAElD,CAAC,eACbnF,OAAA;YAAK6E,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAEtE,SAAS,CAAC,QAAQ;UAAC;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAExDnF,OAAA,CAACR,UAAU;YACV4F,IAAI,EAAC,OAAO;YACZ,cAAW,OAAO;YAClBL,OAAO,EAAET,WAAY;YAAAQ,QAAA,eAErB9E,OAAA,CAACN,SAAS;cAACmF,SAAS,EAAC;YAAoB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAGNnF,OAAA;UAAK6E,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC/B9E,OAAA;YAAK6E,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAI9B9E,OAAA,CAACX,GAAG;cAACwF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC9E,OAAA;gBAAK6E,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAEtE,SAAS,CAAC,QAAQ;cAAC;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChEnF,OAAA;gBAAA8E,QAAA,eACD9E,OAAA,CAACT,SAAS;kBACT8F,OAAO,EAAC,UAAU;kBAClBjB,KAAK,EAAE3B,yBAAyB,CAACK,MAAO;kBACxCsC,IAAI,EAAC,OAAO;kBACZE,SAAS;kBACTT,SAAS,EAAC,qBAAqB;kBAC/BU,QAAQ,EAAGrB,CAAC,IAAK;oBAChB;oBACA,MAAME,KAAK,GAAGF,CAAC,CAACC,MAAM,CAACC,KAAK;oBAC5B,IAAIA,KAAK,KAAK,EAAE,EAAE;sBACjBG,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC;sBAC/BrC,cAAc,CAAC,KAAK,CAAC;sBACrB;oBACD;oBAEA,IAAI,CAAC,SAAS,CAACsD,IAAI,CAACpB,KAAK,CAAC,EAAE;sBAC3B;oBACD;oBAEA,MAAMqB,UAAU,GAAGC,QAAQ,CAACtB,KAAK,CAAC,IAAI,CAAC;;oBAEvC;oBACA,IAAIqB,UAAU,GAAG,GAAG,IAAIA,UAAU,GAAG,GAAG,EAAE;sBACzCvD,cAAc,CAAC,IAAI,CAAC;oBACrB,CAAC,MAAM;sBACNA,cAAc,CAAC,KAAK,CAAC;oBACtB;oBAEAqC,gBAAgB,CAAC,QAAQ,EAAEH,KAAK,CAAC;kBAClC,CAAE;kBAEFuB,UAAU,EAAE;oBACXC,YAAY,EAAE,IAAI;oBAClBC,EAAE,EAAE;sBAEH,0CAA0C,EAAE;wBAAEC,MAAM,EAAE;sBAAO,CAAC;sBAC9D,gDAAgD,EAAE;wBAAEA,MAAM,EAAE;sBAAO,CAAC;sBACpE,YAAY,EAAC;wBAACA,MAAM,EAAC;sBAAM;oBAE5B;kBACD,CAAE;kBACFC,KAAK,EAAE9D;gBAAY;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACe,CAAC,EACxBlD,WAAW,iBACXjC,OAAA,CAACV,UAAU;cACV0G,KAAK,EAAE;gBACNC,QAAQ,EAAE,MAAM;gBAChBC,KAAK,EAAE,SAAS;gBAChBC,SAAS,EAAE,MAAM;gBACjBC,GAAG,EAAE,MAAM;gBACXC,IAAI,EAAE,CAAC;gBACPC,YAAY,EAAE,KAAK;gBACnBC,OAAO,EAAE;cACV,CAAE;cAAAzB,QAAA,gBAEF9E,OAAA;gBAAMgG,KAAK,EAAE;kBAAEO,OAAO,EAAE,MAAM;kBAAEN,QAAQ,EAAE,MAAM;kBAAEO,UAAU,EAAE,QAAQ;kBAAEC,WAAW,EAAC;gBAAM,CAAE;gBAC3FC,uBAAuB,EAAE;kBAAEC,MAAM,EAAE9G;gBAAQ;cAAE;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,EACA3E,SAAS,CAAC,wCAAwC,CAAC;YAAA;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CACZ,eACkBnF,OAAA,CAACX,GAAG;cAACwF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBACnD9E,OAAA;gBAAK6E,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAEtE,SAAS,CAAC,OAAO;cAAC;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/DnF,OAAA;gBAAA8E,QAAA,eACD9E,OAAA,CAACT,SAAS;kBACT8F,OAAO,EAAC,UAAU;kBAClBjB,KAAK,EAAE3B,yBAAyB,CAAC9B,KAAM;kBACvCyE,IAAI,EAAC,OAAO;kBACZE,SAAS;kBACTT,SAAS,EAAC,qBAAqB;kBAC/BU,QAAQ,EAAGrB,CAAC,IAAK;oBAChB;oBACA,MAAME,KAAK,GAAGF,CAAC,CAACC,MAAM,CAACC,KAAK;oBAC5B,IAAIA,KAAK,KAAK,EAAE,EAAE;sBACjBG,gBAAgB,CAAC,OAAO,EAAE,GAAG,CAAC;sBAC9BnC,aAAa,CAAC,KAAK,CAAC;sBACpB;oBACD;oBAEA,IAAI,CAAC,SAAS,CAACoD,IAAI,CAACpB,KAAK,CAAC,EAAE;sBAC3B;oBACD;oBAEA,MAAMqB,UAAU,GAAGC,QAAQ,CAACtB,KAAK,CAAC,IAAI,CAAC;;oBAEvC;oBACA,IAAIqB,UAAU,GAAG,GAAG,IAAIA,UAAU,GAAG,IAAI,EAAE;sBAC1CrD,aAAa,CAAC,IAAI,CAAC;oBACpB,CAAC,MAAM;sBACNA,aAAa,CAAC,KAAK,CAAC;oBACrB;oBAEAmC,gBAAgB,CAAC,OAAO,EAAEH,KAAK,CAAC;kBACjC,CAAE;kBAEFuB,UAAU,EAAE;oBACXC,YAAY,EAAE,IAAI;oBAClBC,EAAE,EAAE;sBAEH,0CAA0C,EAAE;wBAAEC,MAAM,EAAE;sBAAO,CAAC;sBAC9D,gDAAgD,EAAE;wBAAEA,MAAM,EAAE;sBAAO,CAAC;sBACpE,YAAY,EAAC;wBAACA,MAAM,EAAC;sBAAM;oBAE5B;kBACD,CAAE;kBACFC,KAAK,EAAE5D;gBAAW;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EACLhD,UAAU,iBACVnC,OAAA,CAACV,UAAU;cACV0G,KAAK,EAAE;gBACNC,QAAQ,EAAE,MAAM;gBAChBC,KAAK,EAAE,SAAS;gBAChBC,SAAS,EAAE,MAAM;gBACjBC,GAAG,EAAE,MAAM;gBACXC,IAAI,EAAE,CAAC;gBACPC,YAAY,EAAE,KAAK;gBACnBC,OAAO,EAAE;cACV,CAAE;cAAAzB,QAAA,gBAEF9E,OAAA;gBAAMgG,KAAK,EAAE;kBAAEO,OAAO,EAAE,MAAM;kBAAEN,QAAQ,EAAE,MAAM;kBAAEO,UAAU,EAAE,QAAQ;kBAAEC,WAAW,EAAC;gBAAM,CAAE;gBAC3FC,uBAAuB,EAAE;kBAAEC,MAAM,EAAE9G;gBAAQ;cAAE;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,EACA3E,SAAS,CAAC,yCAAyC,CAAC;YAAA;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CACZ,eAEDnF,OAAA,CAACX,GAAG;cAACwF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC9E,OAAA;gBAAK6E,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAEtE,SAAS,CAAC,eAAe;cAAC;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvEnF,OAAA;gBAAA8E,QAAA,eACD9E,OAAA,CAACT,SAAS;kBACT8F,OAAO,EAAC,UAAU;kBAClBjB,KAAK,EAAE3B,yBAAyB,CAACM,YAAa;kBAC9C6D,SAAS;kBACTxB,IAAI,EAAC,OAAO;kBACZP,SAAS,EAAC,qBAAqB;kBAC/BU,QAAQ,EAAGrB,CAAC,IAAK;oBAChB;oBACA,MAAME,KAAK,GAAGF,CAAC,CAACC,MAAM,CAACC,KAAK;oBAC5B,IAAIA,KAAK,KAAK,EAAE,EAAE;sBACjBG,gBAAgB,CAAC,cAAc,EAAE,GAAG,CAAC;sBACrCjC,oBAAoB,CAAC,KAAK,CAAC;sBAC3B;oBACD;oBAEA,IAAI,CAAC,SAAS,CAACkD,IAAI,CAACpB,KAAK,CAAC,EAAE;sBAC3B;oBACD;oBAEA,MAAMqB,UAAU,GAAGC,QAAQ,CAACtB,KAAK,CAAC,IAAI,CAAC;;oBAEvC;oBACA,IAAIqB,UAAU,GAAG,CAAC,IAAIA,UAAU,GAAG,EAAE,EAAE;sBACtCnD,oBAAoB,CAAC,IAAI,CAAC;oBAC3B,CAAC,MAAM;sBACNA,oBAAoB,CAAC,KAAK,CAAC;oBAC5B;oBAEAiC,gBAAgB,CAAC,cAAc,EAAEH,KAAK,CAAC;kBACxC,CAAE;kBAEFuB,UAAU,EAAE;oBACXC,YAAY,EAAE,IAAI;oBAClBC,EAAE,EAAE;sBAEH,0CAA0C,EAAE;wBAAEC,MAAM,EAAE;sBAAO,CAAC;sBAC9D,gDAAgD,EAAE;wBAAEA,MAAM,EAAE;sBAAO,CAAC;sBACpE,YAAY,EAAC;wBAACA,MAAM,EAAC;sBAAM;oBAE5B;kBACD,CAAE;kBACFC,KAAK,EAAE1D;gBAAkB;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EACN9C,iBAAiB,iBACjBrC,OAAA,CAACV,UAAU;cACV0G,KAAK,EAAE;gBACNC,QAAQ,EAAE,MAAM;gBAChBC,KAAK,EAAE,SAAS;gBAChBC,SAAS,EAAE,MAAM;gBACjBC,GAAG,EAAE,MAAM;gBACXC,IAAI,EAAE,CAAC;gBACPC,YAAY,EAAE,KAAK;gBACnBC,OAAO,EAAE;cACV,CAAE;cAAAzB,QAAA,gBAEF9E,OAAA;gBAAMgG,KAAK,EAAE;kBAAEO,OAAO,EAAE,MAAM;kBAAEN,QAAQ,EAAE,MAAM;kBAAEO,UAAU,EAAE,QAAQ;kBAAEC,WAAW,EAAC;gBAAM,CAAE;gBAC3FC,uBAAuB,EAAE;kBAAEC,MAAM,EAAE9G;gBAAQ;cAAE;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,EACA3E,SAAS,CAAC,qCAAqC,CAAC;YAAA;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CACZ,eAEAnF,OAAA,CAACX,GAAG;cAACwF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBACjC9E,OAAA;gBAAK6E,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAEtE,SAAS,CAAC,cAAc;cAAC;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtEnF,OAAA;gBAAA8E,QAAA,eACD9E,OAAA,CAACT,SAAS;kBACT8F,OAAO,EAAC,UAAU;kBAClBjB,KAAK,EAAE3B,yBAAyB,CAACU,WAAY;kBAC7CyD,SAAS;kBACTxB,IAAI,EAAC,OAAO;kBACZP,SAAS,EAAC,qBAAqB;kBAC/BU,QAAQ,EAAGrB,CAAC,IAAK;oBAChB;oBACA,MAAME,KAAK,GAAGF,CAAC,CAACC,MAAM,CAACC,KAAK;oBAC5B,IAAIA,KAAK,KAAK,EAAE,EAAE;sBACjBG,gBAAgB,CAAC,aAAa,EAAE,GAAG,CAAC;sBACpC/B,mBAAmB,CAAC,KAAK,CAAC;sBAC1B;oBACD;oBAEA,IAAI,CAAC,SAAS,CAACgD,IAAI,CAACpB,KAAK,CAAC,EAAE;sBAC3B;oBACD;oBAEA,MAAMqB,UAAU,GAAGC,QAAQ,CAACtB,KAAK,CAAC,IAAI,CAAC;;oBAEvC;oBACA,IAAIqB,UAAU,GAAG,CAAC,IAAIA,UAAU,GAAG,EAAE,EAAE;sBACtCjD,mBAAmB,CAAC,IAAI,CAAC;oBAC1B,CAAC,MAAM;sBACNA,mBAAmB,CAAC,KAAK,CAAC;oBAC3B;oBAEA+B,gBAAgB,CAAC,aAAa,EAAEH,KAAK,CAAC;kBACvC,CAAE;kBACFuB,UAAU,EAAE;oBACXC,YAAY,EAAE,IAAI;oBAClBC,EAAE,EAAE;sBAEH,0CAA0C,EAAE;wBAAEC,MAAM,EAAE;sBAAO,CAAC;sBAC9D,gDAAgD,EAAE;wBAAEA,MAAM,EAAE;sBAAO,CAAC;sBACpE,YAAY,EAAC;wBAACA,MAAM,EAAC;sBAAM;oBAE5B;kBACD,CAAE;kBACFC,KAAK,EAAExD;gBAAiB;kBAAAyC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EACL5C,gBAAgB,iBAChBvC,OAAA,CAACV,UAAU;cACV0G,KAAK,EAAE;gBACNC,QAAQ,EAAE,MAAM;gBAChBC,KAAK,EAAE,SAAS;gBAChBC,SAAS,EAAE,MAAM;gBACjBC,GAAG,EAAE,MAAM;gBACXC,IAAI,EAAE,CAAC;gBACPC,YAAY,EAAE,KAAK;gBACnBC,OAAO,EAAE;cACV,CAAE;cAAAzB,QAAA,gBAEF9E,OAAA;gBAAMgG,KAAK,EAAE;kBAAEO,OAAO,EAAE,MAAM;kBAAEN,QAAQ,EAAE,MAAM;kBAAEO,UAAU,EAAE,QAAQ;kBAAEC,WAAW,EAAC;gBAAM,CAAE;gBAC3FC,uBAAuB,EAAE;kBAAEC,MAAM,EAAE9G;gBAAQ;cAAE;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,EACA3E,SAAS,CAAC,qCAAqC,CAAC;YAAA;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CACZ,eAEcnF,OAAA,CAACX,GAAG;cAACwF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC/C9E,OAAA;gBAAK6E,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAEtE,SAAS,CAAC,eAAe;cAAC;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvEnF,OAAA;gBAAA8E,QAAA,eACD9E,OAAA;kBACC6G,IAAI,EAAC,OAAO;kBACZzC,KAAK,EAAE3B,yBAAyB,CAACO,YAAa;kBAC9CuC,QAAQ,EAAGrB,CAAC,IAAKK,gBAAgB,CAAC,cAAc,EAAEL,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;kBAChES,SAAS,EAAC,mBAAmB;kBAC7BmB,KAAK,EAAE;oBAACnF,eAAe,EAAC;kBAA0B;gBAAE;kBAAAmE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACe,CAAC,eAGzBnF,OAAA,CAACX,GAAG;cAACwF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC9E,OAAA;gBAAK6E,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAEtE,SAAS,CAAC,YAAY;cAAC;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACpEnF,OAAA;gBAAA8E,QAAA,eACD9E,OAAA;kBACC6G,IAAI,EAAC,OAAO;kBACZzC,KAAK,EAAE3B,yBAAyB,CAAC5B,eAAgB;kBACjD0E,QAAQ,EAAGrB,CAAC,IAAKK,gBAAgB,CAAC,iBAAiB,EAAEL,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;kBACrES,SAAS,EAAC;gBAAmB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACe,CAAC,eAGzBnF,OAAA,CAACX,GAAG;cAACwF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChC9E,OAAA;gBAAK6E,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAEtE,SAAS,CAAC,QAAQ;cAAC;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChEnF,OAAA;gBAAA8E,QAAA,eACD9E,OAAA;kBACC6G,IAAI,EAAC,OAAO;kBACZzC,KAAK,EAAE3B,yBAAyB,CAAC/B,WAAY;kBAC7C6E,QAAQ,EAAGrB,CAAC,IAAKK,gBAAgB,CAAC,aAAa,EAAEL,CAAC,CAACC,MAAM,CAACC,KAAK,CAAE;kBACjES,SAAS,EAAC;gBAAmB;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAELnF,OAAA,CAACX,GAAG;cAACwF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBACjC9E,OAAA;gBAAK6E,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAEtE,SAAS,CAAC,iBAAiB;cAAC;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzEnF,OAAA;gBAAA8E,QAAA,eACC9E,OAAA;kBAAO6E,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBACR9E,OAAA;oBACI6G,IAAI,EAAC,UAAU;oBACfC,OAAO,EAAErE,yBAAyB,CAACQ,aAAc;oBAC5EsC,QAAQ,EAAGrB,CAAC,IAAKK,gBAAgB,CAAC,eAAe,EAAEL,CAAC,CAACC,MAAM,CAAC2C,OAAO,CAAE;oBAC1CC,IAAI,EAAC;kBAAe;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC,eACFnF,OAAA;oBAAM6E,SAAS,EAAC;kBAAQ;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEYnF,OAAA,CAACX,GAAG;cAACwF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBACnD9E,OAAA;gBAAK6E,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,EAAEtE,SAAS,CAAC,uBAAuB;cAAC;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC/EnF,OAAA;gBAAA8E,QAAA,eACC9E,OAAA;kBAAO6E,SAAS,EAAC,eAAe;kBAAAC,QAAA,gBACR9E,OAAA;oBACI6G,IAAI,EAAC,UAAU;oBACfC,OAAO,EAAErE,yBAAyB,CAACS,mBAAoB;oBAClFqC,QAAQ,EAAGrB,CAAC,IAAKK,gBAAgB,CAAC,qBAAqB,EAAEL,CAAC,CAACC,MAAM,CAAC2C,OAAO,CAAE;oBAChDC,IAAI,EAAC;kBAAe;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC,eACFnF,OAAA;oBAAM6E,SAAS,EAAC;kBAAQ;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACPnF,OAAA;UAAK6E,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjC9E,OAAA,CAACP,MAAM;YACN4F,OAAO,EAAC,WAAW;YACnBN,OAAO,EAAEJ,kBAAmB;YAC5BE,SAAS,EAAE,aAAazB,UAAU,GAAG,UAAU,GAAG,EAAE,EAAG;YACvD4D,QAAQ,EAAE5D,UAAW;YAAA0B,QAAA,EAErBtE,SAAS,CAAC,OAAO;UAAC;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEF;IACL;EAAA;AAEF,CAAC;AAAC7E,EAAA,CA/fIL,uBAAuB;EAAA,QACHH,cAAc,EAuBnCF,cAAc;AAAA;AAAAqH,EAAA,GAxBbhH,uBAAuB;AAigB7B,eAAeA,uBAAuB;AAAC,IAAAgH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}