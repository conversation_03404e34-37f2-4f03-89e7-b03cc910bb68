@font-face {
  font-family: 'qadapt-icons';
  src:  url('fonts/qadapt-icons.eot?qmcsfb');
  src:  url('fonts/qadapt-icons.eot?qmcsfb#iefix') format('embedded-opentype'),
    url('fonts/qadapt-icons.ttf?qmcsfb') format('truetype'),
    url('fonts/qadapt-icons.woff?qmcsfb') format('woff'),
    url('fonts/qadapt-icons.svg?qmcsfb#qadapt-icons') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

i {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'qadapt-icons' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.fa-analysis:before {
  content: "\a003";
}
.fa-attendance-machine:before {
  content: "\a004";
}
.fa-bike-insurance:before {
  content: "\a005";
}
.fa-bill-receipt:before {
  content: "\a006";
}
.fa-business-communication:before {
  content: "\a007";
}
.fa-business-investment:before {
  content: "\a008";
}
.fa-business-management:before {
  content: "\a009";
}
.fa-businessman-with-briefcase:before {
  content: "\a010";
}
.fa-business-presentation:before {
  content: "\a011";
}
.fa-business-professional:before {
  content: "\a012";
}
.fa-business-profit:before {
  content: "\a013";
}
.fa-business-relationship:before {
  content: "\a014";
}
.fa-buyer:before {
  content: "\a015";
}
.fa-career:before {
  content: "\a016";
}
.fa-car-insurance:before {
  content: "\a017";
}
.fa-car-repair-mechanic:before {
  content: "\a018";
}
.fa-cashier:before {
  content: "\a019";
}
.fa-ceo:before {
  content: "\a020";
}
.fa-client:before {
  content: "\a021";
}
.fa-clients:before {
  content: "\a022";
}
.fa-closed:before {
  content: "\a023";
}
.fa-contract:before {
  content: "\a024";
}
.fa-core-values:before {
  content: "\a025";
}
.fa-corporate:before {
  content: "\a026";
}
.fa-credit-card-swipe:before {
  content: "\a027";
}
.fa-crm-browser:before {
  content: "\a028";
}
.fa-customer-experience:before {
  content: "\a029";
}
.fa-customer-journey:before {
  content: "\a030";
}
.fa-data-analytics:before {
  content: "\a031";
}
.fa-data-science:before {
  content: "\a032";
}
.fa-document-application:before {
  content: "\a033";
}
.fa-document-application-woman:before {
  content: "\a034";
}
.fa-erp:before {
  content: "\a035";
}
.fa-factory-pollution:before {
  content: "\a036";
}
.fa-family-insurance:before {
  content: "\a037";
}
.fa-female-reporter-journalist:before {
  content: "\a038";
}
.fa-fire-insurance:before {
  content: "\a039";
}
.fa-food-industry:before {
  content: "\a040";
}
.fa-general-insurance:before {
  content: "\a041";
}
.fa-growing-market-analysis:before {
  content: "\a042";
}
.fa-gst:before {
  content: "\a043";
}
.fa-headquarter:before {
  content: "\a044";
}
.fa-health-insurance:before {
  content: "\a045";
}
.fa-hierarchy-management:before {
  content: "\a046";
}
.fa-hierarchy-management-task:before {
  content: "\a047";
}
.fa-home-insurance:before {
  content: "\a048";
}
.fa-import-product:before {
  content: "\a049";
}
.fa-improvement-performance:before {
  content: "\a050";
}
.fa-income-taxes:before {
  content: "\a051";
}
.fa-influencer:before {
  content: "\a052";
}
.fa-insight:before {
  content: "\a053";
}
.fa-inspection:before {
  content: "\a054";
}
.fa-insurance-protection:before {
  content: "\a055";
}
.fa-integration:before {
  content: "\a056";
}
.fa-interview:before {
  content: "\a057";
}
.fa-investor:before {
  content: "\a058";
}
.fa-invoice:before {
  content: "\a059";
}
.fa-job:before {
  content: "\a060";
}
.fa-job-search:before {
  content: "\a061";
}
.fa-male-reporter-journalist:before {
  content: "\a062";
}
.fa-management:before {
  content: "\a063";
}
.fa-manufacturing-production:before {
  content: "\a064";
}
.fa-market-research:before {
  content: "\a065";
}
.fa-market-share:before {
  content: "\a066";
}
.fa-mechanic:before {
  content: "\a067";
}
.fa-meeting:before {
  content: "\a068";
}
.fa-meeting-table:before {
  content: "\a069";
}
.fa-mind-map:before {
  content: "\a070";
}
.fa-money-transfer:before {
  content: "\a071";
}
.fa-new-product:before {
  content: "\a072";
}
.fa-newspaper:before {
  content: "\a073";
}
.fa-newspaper-jobs:before {
  content: "\a074";
}
.fa-fa-office:before {
  content: "\a075";
}
.fa-fa-online-survey:before {
  content: "\a076";
}
.fa-fa-online-work:before {
  content: "\a077";
}
.fa-fa-pending-work:before {
  content: "\a078";
}
.fa-fa-person-insurance:before {
  content: "\a079";
}
.fa-fa-pilot:before {
  content: "\a080";
}
.fa-fa-planning:before {
  content: "\a081";
}
.fa-fa-plumbing:before {
  content: "\a082";
}
.fa-fa-power-plant:before {
  content: "\a083";
}
.fa-fa-product-development:before {
  content: "\a084";
}
.fa-productivity:before {
  content: "\a085";
}
.fa-product-launch-release:before {
  content: "\a086";
}
.fa-project:before {
  content: "\a087";
}
.fa-project-management:before {
  content: "\a088";
}
.fa-project-management-timeline:before {
  content: "\a089";
}
.fa-fa-project-manager:before {
  content: "\a090";
}
.fa-fa-project-work:before {
  content: "\a091";
}
.fa-fa-quality-control:before {
  content: "\a092";
}
.fa-receipt:before {
  content: "\a093";
}
.fa-fa-remote-work:before {
  content: "\a094";
}
.fa-fa-repairing:before {
  content: "\a095";
}
.fa-fa-retail-shop:before {
  content: "\a096";
}
.fa-fa-satisfaction:before {
  content: "\a097";
}
.fa-fa-seller:before {
  content: "\a098";
}
.fa-fa-service-desk:before {
  content: "\a099";
}
.fa-fa-services:before {
  content: "\a100";
}
.fa-fa-solution:before {
  content: "\a101";
}
.fa-fa-strategist:before {
  content: "\a102";
}
.fa-fa-successful-businessman:before {
  content: "\a103";
}
.fa-fa-supervisor:before {
  content: "\a104";
}
.fa-fa-supply-chain:before {
  content: "\a105";
}
.fa-fa-tax-calculator:before {
  content: "\a106";
}
.fa-fa-tax-cut:before {
  content: "\a107";
}
.fa-fa-tax-return:before {
  content: "\a108";
}
.fa-fa-team:before {
  content: "\a109";
}
.fa-fa-team-meeting:before {
  content: "\a110";
}
.fa-fa-technician:before {
  content: "\a111";
}
.fa-fa-trade:before {
  content: "\a112";
}
.fa-fa-user-network:before {
  content: "\a113";
}
.fa-fa-value:before {
  content: "\a114";
}
.fa-fa-vat:before {
  content: "\a115";
}
.fa-fa-video-conference:before {
  content: "\a116";
}
.fa-fa-virtual-meeting:before {
  content: "\a117";
}
.fa-fa-meeting-room:before {
  content: "\a118";
}
.fa-fa-workflow:before {
  content: "\a119";
}
.fa-fa-working-hours:before {
  content: "\a120";
}
.fa-fa-working-on-office:before {
  content: "\a121";
}
.fa-fa-working-time:before {
  content: "\a122";
}
.fa-fa-workplace:before {
  content: "\a123";
}
.fa-fa-workshop:before {
  content: "\a124";
}
.fa-fa-waiting-room-area:before {
  content: "\a125";
}
.fa-fa-user-tie-solid:before {
  content: "\a127";
}
.fa-fa-caret-up-solid:before {
  content: "\a128";
}
.fa-fa-check-circle-solid:before {
  content: "\a129";
}
.fa-fa-times-circle-solid:before {
  content: "\a130";
}
.fa-fa-password-reset:before {
  content: "\a131";
}
.fa-fa-password:before {
  content: "\a132";
}
.fa-fa-reset-password:before {
  content: "\a133";
}
.fa-fa-men-gear-circle:before {
  content: "\a134";
}
.fa-fa-men-gear:before {
  content: "\a135";
}
.fa-fa-light-ceilinglight:before {
  content: "\a147";
}
.fa-fa-exclamation-circle-solid:before {
  content: "\a148";
}
.fa-fa-add-multi-dtrecords:before {
  content: "\a149";
}
.fa-fa-branch:before {
  content: "\a150";
}
.fa-fa-deploy-log-download:before {
  content: "\a151";
}
.fa-fa-deployment-log:before {
  content: "\a152";
}
.fa-fa-deploy-rollback:before {
  content: "\a153";
}
.fa-fa-dt-download:before {
  content: "\a154";
}
.fa-fa-ds-filter:before {
  content: "\a155";
}
.fa-fa-dt-functions:before {
  content: "\a156";
}
.fa-fa-import-dtrecords:before {
  content: "\a157";
}
.fa-fa-dt-index:before {
  content: "\a158";
}
.fa-fa-ds-join:before {
  content: "\a159";
}
.fa-fa-manage-stages:before {
  content: "\a160";
}
.fa-fa-records:before {
  content: "\a161";
}
.fa-fa-regular-view:before {
  content: "\a162";
}
.fa-fa-dt-sync:before {
  content: "\a163";
}
.fa-fa-timeline-view:before {
  content: "\a164";
}
.fa-fa-manage-apps:before {
  content: "\a165";
}
.fa-fa-box-key:before {
  content: "\a166";
}
.fa-fa-deploy-branch:before {
  content: "\a167";
}
.fa-fa-version-manage:before {
  content: "\a168";
}
.fa-fa-add-ds:before {
  content: "\a169";
}
.fa-fa-add-dttable:before {
  content: "\a170";
}
.fa-fa-admenu:before {
  content: "\a171";
}
.fa-fa-apps:before {
  content: "\a172";
}
.fa-fa-appstore:before {
  content: "\a173";
}
.fa-fa-crreport:before {
  content: "\a174";
}
.fa-fa-crview:before {
  content: "\a175";
}
.fa-fa-datasource:before {
  content: "\a176";
}
.fa-fa-dsref:before {
  content: "\a177";
}
.fa-fa-dttable:before {
  content: "\a178";
}
.fa-fa-imp-user:before {
  content: "\a179";
}
.fa-fa-ip-white:before {
  content: "\a180";
}
.fa-fa-more:before {
  content: "\a181";
}
.fa-fa-multi-user:before {
  content: "\a182";
}
.fa-fa-pref:before {
  content: "\a183";
}
.fa-fa-config-sms:before {
  content: "\a184";
}
.fa-fa-ham-menu:before {
  content: "\a185";
}
.fa-fa-myroles:before {
  content: "\a186";
}
.fa-fa-dots:before {
  content: "\a187";
}
.fa-fa-add-field:before {
  content: "\a188";
}
.fa-fa-add-plus:before {
  content: "\a189";
}
.fa-fa-avatar:before {
  content: "\a190";
}
.fa-fa-back-arrow:before {
  content: "\a191";
}
.fa-fa-close:before {
  content: "\a192";
}
.fa-fa-close-1:before {
  content: "\a193";
}
.fa-fa-copy-icon:before {
  content: "\a194";
}
.fa-fa-drag:before {
  content: "\a195";
}
.fa-fa-editprofile:before {
  content: "\a196";
}
.fa-fa-group-by:before {
  content: "\a197";
}
.fa-fa-integrations:before {
  content: "\a198";
}
.fa-fa-logout:before {
  content: "\a199";
}
.fa-fa-caret-down-solid:before {
  content: "\a200";
}
.fa-fa-sort-down-solid:before {
  content: "\a201";
}
.fa-fa-mpin:before {
  content: "\a202";
}
.fa-fa-no-notifications:before {
  content: "\a203";
}
.fa-fa-notask:before {
  content: "\a204";
}
.fa-fa-password-lock:before {
  content: "\a205";
}
.fa-fa-preferences:before {
  content: "\a206";
}
.fa-fa-process:before {
  content: "\a207";
}
.fa-fa-profile-notifications:before {
  content: "\a208";
}
.fa-fa-profile-user:before {
  content: "\a209";
}
.fa-fa-reassign:before {
  content: "\a210";
}
.fa-fa-reportproblem:before {
  content: "\a211";
}
.fa-fa-right-arrow:before {
  content: "\a212";
}
.fa-fa-sort:before {
  content: "\a213";
}
.fa-fa-validation:before {
  content: "\a214";
}
.fa-fa-add-record:before {
  content: "\a215";
}
.fa-fa-dafts:before {
  content: "\a216";
}
.fa-fa-dashboard:before {
  content: "\a217";
}
.fa-fa-initiated:before {
  content: "\a218";
}
.fa-fa-manage-app:before {
  content: "\a219";
}
.fa-fa-menu:before {
  content: "\a220";
}
.fa-fa-participated:before {
  content: "\a221";
}
.fa-fa-reports:before {
  content: "\a222";
}
.fa-fa-requests:before {
  content: "\a223";
}
.fa-fa-tasks-circle:before {
  content: "\a224";
}
.fa-fa-tasks_old:before {
  content: "\a225";
}
.fa-fa-solutionview:before {
  content: "\a226";
}
.fa-fa-meeting-room-light:before {
  content: "\a227";
}
.fa-fa-external-rdbms:before {
  content: "\a228";
}
.fa-fa-pin-inclined:before {
  content: "\a229";
}
.fa-fa-generate-data:before {
  content: "\a230";
}
.fa-fa-dt-filter:before {
  content: "\a231";
}
.fa-fa-export-settings:before {
  content: "\a400";
}
.fa-fa-caravan-alt:before {
  content: "\e000";
}
.fa-fa-cat-space:before {
  content: "\e001";
}
.fa-fa-coffee-pot:before {
  content: "\e002";
}
.fa-fa-comet:before {
  content: "\e003";
}
.fa-fa-fan-table:before {
  content: "\e004";
}
.fa-fa-faucet:before {
  content: "\e005";
}
.fa-fa-faucet-drip:before {
  content: "\e006";
}
.fa-fa-galaxy:before {
  content: "\e008";
}
.fa-fa-garage:before {
  content: "\e009";
}
.fa-fa-garage-car:before {
  content: "\e00a";
}
.fa-fa-garage-open:before {
  content: "\e00b";
}
.fa-fa-heat:before {
  content: "\e00c";
}
.fa-fa-house-day:before {
  content: "\e00e";
}
.fa-fa-house-leave:before {
  content: "\e00f";
}
.fa-fa-house-night:before {
  content: "\e010";
}
.fa-fa-house-return:before {
  content: "\e011";
}
.fa-fa-house-signal:before {
  content: "\e012";
}
.fa-fa-lamp-desk:before {
  content: "\e014";
}
.fa-fa-lamp-floor:before {
  content: "\e015";
}
.fa-fa-light-ceiling:before {
  content: "\e016";
}
.fa-fa-light-switch:before {
  content: "\e017";
}
.fa-fa-light-switch-off:before {
  content: "\e018";
}
.fa-fa-microwave:before {
  content: "\e01b";
}
.fa-fa-raygun:before {
  content: "\e025";
}
.fa-fa-rocket-launch:before {
  content: "\e027";
}
.fa-fa-coffin-cross:before {
  content: "\e051";
}
.fa-fa-folder-download:before {
  content: "\e053";
}
.fa-fa-folder-upload:before {
  content: "\e054";
}
.fa-fa-bacteria:before {
  content: "\e059";
}
.fa-fa-bacterium:before {
  content: "\e05a";
}
.fa-fa-box-tissue:before {
  content: "\e05b";
}
.fa-fa-hand-holding-medical:before {
  content: "\e05c";
}
.fa-fa-hand-sparkles:before {
  content: "\e05d";
}
.fa-fa-hands-wash:before {
  content: "\e05e";
}
.fa-fa-handshake-alt-slash:before {
  content: "\e05f";
}
.fa-fa-handshake-slash:before {
  content: "\e060";
}
.fa-fa-head-side-cough:before {
  content: "\e061";
}
.fa-fa-head-side-cough-slash:before {
  content: "\e062";
}
.fa-fa-head-side-mask:before {
  content: "\e063";
}
.fa-fa-head-side-virus:before {
  content: "\e064";
}
.fa-fa-house-user:before {
  content: "\e065";
}
.fa-fa-laptop-house:before {
  content: "\e066";
}
.fa-fa-lungs-virus:before {
  content: "\e067";
}
.fa-fa-angle-double-up:before {
  content: "\e92a";
}
.fa-credit-card-blank:before {
  content: "\e942";
}
.fa-credit-card-front:before {
  content: "\e943";
}
.fa-drum-light:before {
  content: "\e963";
}
.fa-fa-file-signature:before {
  content: "\e98a";
}
.fa-fa-gingerbread-man:before {
  content: "\e9b6";
}
.fa-horse-head-light:before {
  content: "\ea12";
}
.fa-image-light:before {
  content: "\ea28";
}
.fa-fa-info:before {
  content: "\ea2d";
}
.fa-inventory-light:before {
  content: "\ea2f";
}
.fa-line-columns-light:before {
  content: "\ea52";
}
.fa-location-arrow-light:before {
  content: "\ea56";
}
.fa-fa-location-circle:before {
  content: "\ea57";
}
.fa-mailbox-light:before {
  content: "\ea63";
}
.fa-map-marker-light:before {
  content: "\ea6a";
}
.fa-fa-mug-tea:before {
  content: "\ea94";
}
.fa-music-alt-slash-light:before {
  content: "\ea95";
}
.fa-network-wired-light:before {
  content: "\ea96";
}
.fa-neuter-light:before {
  content: "\ea97";
}
.fa-notes-medical-light:before {
  content: "\ea98";
}
.fa-object-ungroup-light:before {
  content: "\ea99";
}
.fa-oil-temp-light:before {
  content: "\ea9a";
}
.fa-otter-light:before {
  content: "\ea9b";
}
.fa-outdent-light:before {
  content: "\ea9c";
}
.fa-outlet-light:before {
  content: "\ea9d";
}
.fa-oven-light:before {
  content: "\ea9e";
}
.fa-overline-light:before {
  content: "\ea9f";
}
.fa-page-break-light:before {
  content: "\eaa0";
}
.fa-chevron-left-light:before {
  content: "\eaa1";
}
.fa-mobile-android-light:before {
  content: "\eaa2";
}
.fa-comments-alt-dollar-light:before {
  content: "\eaa3";
}
.fa-fa-bus-alt:before {
  content: "\eaa4";
}
.fa-bars-light---f0c9:before {
  content: "\eaa5";
}
.fa-fa-bath:before {
  content: "\eaa6";
}
.fa-fa-user-tag:before {
  content: "\eaa7";
}
.fa-fa-trophy-alt:before {
  content: "\eaa8";
}
.fa-file-light---f15b:before {
  content: "\eaa9";
}
.fa-grip-horizontal-light---f58d:before {
  content: "\eaaa";
}
.fa-fa-blinds-open:before {
  content: "\eaab";
}
.fa-mailbox-light---f813:before {
  content: "\eaac";
}
.fa-fa-glass-martini:before {
  content: "\f000";
}
.fa-fa-music:before {
  content: "\f001";
}
.fa-fa-search:before {
  content: "\f002";
}
.fa-fa-heart:before {
  content: "\f004";
}
.fa-fa-star:before {
  content: "\f005";
}
.fa-fa-user:before {
  content: "\f007";
}
.fa-fa-film:before {
  content: "\f008";
}
.fa-fa-th:before {
  content: "\f00a";
}
.fa-fa-check:before {
  content: "\f00c";
}
.fa-fa-times:before {
  content: "\f00d";
}
.fa-fa-search-plus:before {
  content: "\f00e";
}
.fa-fa-search-minus:before {
  content: "\f010";
}
.fa-fa-power-off:before {
  content: "\f011";
}
.fa-fa-signal:before {
  content: "\f012";
}
.fa-fa-cog:before {
  content: "\f013";
}
.fa-fa-home:before {
  content: "\f015";
}
.fa-fa-clock:before {
  content: "\f017";
}
.fa-fa-road:before {
  content: "\f018";
}
.fa-fa-download:before {
  content: "\f019";
}
.fa-fa-inbox:before {
  content: "\f01c";
}
.fa-fa-redo:before {
  content: "\f01e";
}
.fa-fa-sync:before {
  content: "\f021";
}
.fa-fa-list-alt:before {
  content: "\f022";
}
.fa-fa-lock:before {
  content: "\f023";
}
.fa-fa-flag:before {
  content: "\f024";
}
.fa-fa-headphones:before {
  content: "\f025";
}
.fa-fa-volume-up:before {
  content: "\f028";
}
.fa-fa-qrcode:before {
  content: "\f029";
}
.fa-fa-barcode:before {
  content: "\f02a";
}
.fa-fa-tag:before {
  content: "\f02b";
}
.fa-fa-book:before {
  content: "\f02d";
}
.fa-fa-bookmark:before {
  content: "\f02e";
}
.fa-fa-print:before {
  content: "\f02f";
}
.fa-fa-camera:before {
  content: "\f030";
}
.fa-fa-font:before {
  content: "\f031";
}
.fa-fa-bold:before {
  content: "\f032";
}
.fa-fa-italic:before {
  content: "\f033";
}
.fa-fa-text-width:before {
  content: "\f035";
}
.fa-fa-align-left:before {
  content: "\f036";
}
.fa-fa-align-center:before {
  content: "\f037";
}
.fa-fa-align-right:before {
  content: "\f038";
}
.fa-fa-align-justify:before {
  content: "\f039";
}
.fa-fa-list:before {
  content: "\f03a";
}
.fa-fa-indent:before {
  content: "\f03c";
}
.fa-fa-video:before {
  content: "\f03d";
}
.fa-fa-image:before {
  content: "\f03e";
}
.fa-fa-pencil:before {
  content: "\f040";
}
.fa-fa-map-marker:before {
  content: "\f041";
}
.fa-fa-adjust:before {
  content: "\f042";
}
.fa-fa-tint:before {
  content: "\f043";
}
.fa-fa-edit:before {
  content: "\f044";
}
.fa-fa-arrows:before {
  content: "\f047";
}
.fa-fa-fast-backward:before {
  content: "\f049";
}
.fa-fa-backward:before {
  content: "\f04a";
}
.fa-fa-stop:before {
  content: "\f04d";
}
.fa-fa-forward:before {
  content: "\f04e";
}
.fa-fa-fast-forward:before {
  content: "\f050";
}
.fa-fa-eject:before {
  content: "\f052";
}
.fa-fa-chevron-left:before {
  content: "\f053";
}
.fa-fa-chevron-right:before {
  content: "\f054";
}
.fa-fa-plus-circle:before {
  content: "\f055";
}
.fa-fa-minus-circle:before {
  content: "\f056";
}
.fa-fa-times-circle:before {
  content: "\f057";
}
.fa-fa-check-circle:before {
  content: "\f058";
}
.fa-fa-question-circle:before {
  content: "\f059";
}
.fa-fa-info-circle:before {
  content: "\f05a";
}
.fa-fa-crosshairs:before {
  content: "\f05b";
}
.fa-fa-ban:before {
  content: "\f05e";
}
.fa-fa-arrow-left:before {
  content: "\f060";
}
.fa-fa-arrow-right:before {
  content: "\f061";
}
.fa-fa-arrow-up:before {
  content: "\f062";
}
.fa-fa-arrow-down:before {
  content: "\f063";
}
.fa-fa-share:before {
  content: "\f064";
}
.fa-fa-expand:before {
  content: "\f065";
}
.fa-fa-compress:before {
  content: "\f066";
}
.fa-fa-plus:before {
  content: "\f067";
}
.fa-fa-minus:before {
  content: "\f068";
}
.fa-fa-asterisk:before {
  content: "\f069";
}
.fa-fa-exclamation-circle:before {
  content: "\f06a";
}
.fa-fa-gift:before {
  content: "\f06b";
}
.fa-fa-leaf:before {
  content: "\f06c";
}
.fa-fa-fire:before {
  content: "\f06d";
}
.fa-fa-eye:before {
  content: "\f06e";
}
.fa-fa-eye-slash:before {
  content: "\f070";
}
.fa-fa-exclamation-triangle:before {
  content: "\f071";
}
.fa-fa-plane:before {
  content: "\f072";
}
.fa-fa-calendar-alt:before {
  content: "\f073";
}
.fa-fa-random:before {
  content: "\f074";
}
.fa-fa-comment:before {
  content: "\f075";
}
.fa-fa-magnet:before {
  content: "\f076";
}
.fa-fa-chevron-up:before {
  content: "\f077";
}
.fa-fa-chevron-down:before {
  content: "\f078";
}
.fa-fa-shopping-cart:before {
  content: "\f07a";
}
.fa-fa-folder:before {
  content: "\f07b";
}
.fa-fa-folder-open:before {
  content: "\f07c";
}
.fa-fa-arrows-v:before {
  content: "\f07d";
}
.fa-fa-arrows-h:before {
  content: "\f07e";
}
.fa-fa-chart-bar:before {
  content: "\f080";
}
.fa-fa-camera-retro:before {
  content: "\f083";
}
.fa-fa-key:before {
  content: "\f084";
}
.fa-fa-cogs:before {
  content: "\f085";
}
.fa-fa-comments:before {
  content: "\f086";
}
.fa-fa-sign-out:before {
  content: "\f08b";
}
.fa-fa-thumbtack:before {
  content: "\f08d";
}
.fa-fa-external-link:before {
  content: "\f08e";
}
.fa-fa-upload:before {
  content: "\f093";
}
.fa-fa-lemon:before {
  content: "\f094";
}
.fa-fa-phone-square:before {
  content: "\f098";
}
.fa-fa-credit-card:before {
  content: "\f09d";
}
.fa-fa-rss:before {
  content: "\f09e";
}
.fa-fa-hdd:before {
  content: "\f0a0";
}
.fa-fa-bullhorn:before {
  content: "\f0a1";
}
.fa-fa-certificate:before {
  content: "\f0a3";
}
.fa-fa-hand-point-right:before {
  content: "\f0a4";
}
.fa-fa-hand-point-left:before {
  content: "\f0a5";
}
.fa-fa-hand-point-up:before {
  content: "\f0a6";
}
.fa-fa-hand-point-down:before {
  content: "\f0a7";
}
.fa-fa-arrow-circle-left:before {
  content: "\f0a8";
}
.fa-fa-arrow-circle-right:before {
  content: "\f0a9";
}
.fa-fa-arrow-circle-up:before {
  content: "\f0aa";
}
.fa-fa-arrow-circle-down:before {
  content: "\f0ab";
}
.fa-fa-globe:before {
  content: "\f0ac";
}
.fa-fa-wrench:before {
  content: "\f0ad";
}
.fa-fa-tasks:before {
  content: "\f0ae";
}
.fa-fa-filter:before {
  content: "\f0b0";
}
.fa-fa-briefcase:before {
  content: "\f0b1";
}
.fa-fa-arrows-alt:before {
  content: "\f0b2";
}
.fa-fa-users:before {
  content: "\f0c0";
}
.fa-fa-link:before {
  content: "\f0c1";
}
.fa-fa-cloud:before {
  content: "\f0c2";
}
.fa-fa-flask:before {
  content: "\f0c3";
}
.fa-fa-cut:before {
  content: "\f0c4";
}
.fa-fa-copy:before {
  content: "\f0c5";
}
.fa-fa-paperclip:before {
  content: "\f0c6";
}
.fa-fa-save:before {
  content: "\f0c7";
}
.fa-fa-square:before {
  content: "\f0c8";
}
.fa-fa-bars:before {
  content: "\f0c9";
}
.fa-fa-list-ul:before {
  content: "\f0ca";
}
.fa-fa-list-ol:before {
  content: "\f0cb";
}
.fa-fa-table:before {
  content: "\f0ce";
}
.fa-fa-magic:before {
  content: "\f0d0";
}
.fa-fa-truck:before {
  content: "\f0d1";
}
.fa-fa-money-bill:before {
  content: "\f0d6";
}
.fa-fa-caret-down:before {
  content: "\f0d7";
}
.fa-fa-caret-up:before {
  content: "\f0d8";
}
.fa-fa-caret-left:before {
  content: "\f0d9";
}
.fa-fa-caret-right:before {
  content: "\f0da";
}
.fa-fa-columns:before {
  content: "\f0db";
}
.fa-fa-sort-down:before {
  content: "\f0dd";
}
.fa-fa-envelope:before {
  content: "\f0e0";
}
.fa-fa-undo:before {
  content: "\f0e2";
}
.fa-fa-gavel:before {
  content: "\f0e3";
}
.fa-fa-tachometer:before {
  content: "\f0e4";
}
.fa-fa-bolt:before {
  content: "\f0e7";
}
.fa-fa-sitemap:before {
  content: "\f0e8";
}
.fa-fa-umbrella:before {
  content: "\f0e9";
}
.fa-fa-paste:before {
  content: "\f0ea";
}
.fa-fa-lightbulb:before {
  content: "\f0eb";
}
.fa-fa-exchange:before {
  content: "\f0ec";
}
.fa-fa-cloud-download:before {
  content: "\f0ed";
}
.fa-fa-cloud-upload:before {
  content: "\f0ee";
}
.fa-fa-user-md:before {
  content: "\f0f0";
}
.fa-fa-stethoscope:before {
  content: "\f0f1";
}
.fa-fa-bell:before {
  content: "\f0f3";
}
.fa-fa-coffee:before {
  content: "\f0f4";
}
.fa-fa-hospital:before {
  content: "\f0f8";
}
.fa-fa-ambulance:before {
  content: "\f0f9";
}
.fa-fa-medkit:before {
  content: "\f0fa";
}
.fa-fa-fighter-jet:before {
  content: "\f0fb";
}
.fa-fa-beer:before {
  content: "\f0fc";
}
.fa-fa-h-square:before {
  content: "\f0fd";
}
.fa-fa-plus-square:before {
  content: "\f0fe";
}
.fa-fa-angle-double-left:before {
  content: "\f100";
}
.fa-fa-angle-double-right:before {
  content: "\f101";
}
.fa-fa-angle-double-down:before {
  content: "\f103";
}
.fa-fa-angle-left:before {
  content: "\f104";
}
.fa-fa-angle-right:before {
  content: "\f105";
}
.fa-fa-angle-up:before {
  content: "\f106";
}
.fa-fa-angle-down:before {
  content: "\f107";
}
.fa-fa-desktop:before {
  content: "\f108";
}
.fa-fa-laptop:before {
  content: "\f109";
}
.fa-fa-mobile:before {
  content: "\f10b";
}
.fa-fa-quote-left:before {
  content: "\f10d";
}
.fa-fa-quote-right:before {
  content: "\f10e";
}
.fa-fa-spinner:before {
  content: "\f110";
}
.fa-fa-circle:before {
  content: "\f111";
}
.fa-fa-smile:before {
  content: "\f118";
}
.fa-fa-frown:before {
  content: "\f119";
}
.fa-fa-meh:before {
  content: "\f11a";
}
.fa-fa-gamepad:before {
  content: "\f11b";
}
.fa-fa-keyboard:before {
  content: "\f11c";
}
.fa-fa-flag-checkered:before {
  content: "\f11e";
}
.fa-fa-terminal:before {
  content: "\f120";
}
.fa-fa-code:before {
  content: "\f121";
}
.fa-fa-location-arrow:before {
  content: "\f124";
}
.fa-fa-crop:before {
  content: "\f125";
}
.fa-fa-code-branch:before {
  content: "\f126";
}
.fa-info:before {
  content: "\f129";
}
.fa-fa-exclamation:before {
  content: "\f12a";
}
.fa-fa-eraser:before {
  content: "\f12d";
}
.fa-fa-puzzle-piece:before {
  content: "\f12e";
}
.fa-fa-microphone:before {
  content: "\f130";
}
.fa-fa-microphone-slash:before {
  content: "\f131";
}
.fa-fa-shield:before {
  content: "\f132";
}
.fa-fa-calendar:before {
  content: "\f133";
}
.fa-fa-fire-extinguisher:before {
  content: "\f134";
}
.fa-fa-rocket:before {
  content: "\f135";
}
.fa-fa-chevron-circle-left:before {
  content: "\f137";
}
.fa-fa-chevron-circle-right:before {
  content: "\f138";
}
.fa-fa-chevron-circle-up:before {
  content: "\f139";
}
.fa-fa-chevron-circle-down:before {
  content: "\f13a";
}
.fa-fa-css3:before {
  content: "\f13c";
}
.fa-fa-anchor:before {
  content: "\f13d";
}
.fa-fa-unlock-alt:before {
  content: "\f13e";
}
.fa-fa-bullseye:before {
  content: "\f140";
}
.fa-fa-ellipsis-h:before {
  content: "\f141";
}
.fa-fa-ellipsis-v:before {
  content: "\f142";
}
.fa-fa-play-circle:before {
  content: "\f144";
}
.fa-fa-ticket:before {
  content: "\f145";
}
.fa-fa-minus-square:before {
  content: "\f146";
}
.fa-fa-level-up:before {
  content: "\f148";
}
.fa-fa-level-down:before {
  content: "\f149";
}
.fa-fa-check-square:before {
  content: "\f14a";
}
.fa-fa-external-link-square:before {
  content: "\f14c";
}
.fa-fa-share-square:before {
  content: "\f14d";
}
.fa-fa-compass:before {
  content: "\f14e";
}
.fa-fa-caret-square-down:before {
  content: "\f150";
}
.fa-fa-caret-square-up:before {
  content: "\f151";
}
.fa-fa-caret-square-right:before {
  content: "\f152";
}
.fa-fa-euro-sign:before {
  content: "\f153";
}
.fa-fa-pound-sign:before {
  content: "\f154";
}
.fa-fa-dollar-sign:before {
  content: "\f155";
}
.fa-fa-rupee-sign:before {
  content: "\f156";
}
.fa-fa-yen-sign:before {
  content: "\f157";
}
.fa-fa-ruble-sign:before {
  content: "\f158";
}
.fa-fa-file:before {
  content: "\f15b";
}
.fa-fa-file-alt:before {
  content: "\f15c";
}
.fa-fa-sort-numeric-down:before {
  content: "\f162";
}
.fa-fa-thumbs-up:before {
  content: "\f164";
}
.fa-fa-thumbs-down:before {
  content: "\f165";
}
.fa-fa-adn:before {
  content: "\f170";
}
.fa-fa-bitbucket:before {
  content: "\f171";
}
.fa-fa-long-arrow-down:before {
  content: "\f175";
}
.fa-fa-long-arrow-up:before {
  content: "\f176";
}
.fa-fa-long-arrow-left:before {
  content: "\f177";
}
.fa-fa-long-arrow-right:before {
  content: "\f178";
}
.fa-fa-android:before {
  content: "\f17b";
}
.fa-fa-female:before {
  content: "\f182";
}
.fa-fa-male:before {
  content: "\f183";
}
.fa-fa-sun:before {
  content: "\f185";
}
.fa-fa-moon:before {
  content: "\f186";
}
.fa-fa-archive:before {
  content: "\f187";
}
.fa-fa-bug:before {
  content: "\f188";
}
.fa-fa-pagelines:before {
  content: "\f18c";
}
.fa-fa-caret-square-left:before {
  content: "\f191";
}
.fa-fa-dot-circle:before {
  content: "\f192";
}
.fa-fa-wheelchair:before {
  content: "\f193";
}
.fa-fa-lira-sign:before {
  content: "\f195";
}
.fa-fa-space-shuttle:before {
  content: "\f197";
}
.fa-fa-envelope-square:before {
  content: "\f199";
}
.fa-fa-openid:before {
  content: "\f19b";
}
.fa-fa-university:before {
  content: "\f19c";
}
.fa-fa-graduation-cap:before {
  content: "\f19d";
}
.fa-fa-google:before {
  content: "\f1a0";
}
.fa-fa-stumbleupon:before {
  content: "\f1a4";
}
.fa-fa-drupal:before {
  content: "\f1a9";
}
.fa-fa-language:before {
  content: "\f1ab";
}
.fa-fa-fax:before {
  content: "\f1ac";
}
.fa-fa-building:before {
  content: "\f1ad";
}
.fa-fa-child:before {
  content: "\f1ae";
}
.fa-fa-paw:before {
  content: "\f1b0";
}
.fa-fa-cube:before {
  content: "\f1b2";
}
.fa-fa-cubes:before {
  content: "\f1b3";
}
.fa-fa-behance:before {
  content: "\f1b4";
}
.fa-fa-behance-square:before {
  content: "\f1b5";
}
.fa-fa-recycle:before {
  content: "\f1b8";
}
.fa-fa-car:before {
  content: "\f1b9";
}
.fa-fa-taxi:before {
  content: "\f1ba";
}
.fa-fa-tree:before {
  content: "\f1bb";
}
.fa-fa-deviantart:before {
  content: "\f1bd";
}
.fa-fa-database:before {
  content: "\f1c0";
}
.fa-fa-file-pdf:before {
  content: "\f1c1";
}
.fa-fa-file-word:before {
  content: "\f1c2";
}
.fa-fa-file-excel:before {
  content: "\f1c3";
}
.fa-fa-file-powerpoint:before {
  content: "\f1c4";
}
.fa-fa-file-image:before {
  content: "\f1c5";
}
.fa-fa-file-archive:before {
  content: "\f1c6";
}
.fa-fa-file-audio:before {
  content: "\f1c7";
}
.fa-fa-file-video:before {
  content: "\f1c8";
}
.fa-fa-file-code:before {
  content: "\f1c9";
}
.fa-fa-vine:before {
  content: "\f1ca";
}
.fa-fa-codepen:before {
  content: "\f1cb";
}
.fa-fa-life-ring:before {
  content: "\f1cd";
}
.fa-fa-circle-notch:before {
  content: "\f1ce";
}
.fa-fa-rebel:before {
  content: "\f1d0";
}
.fa-fa-qq:before {
  content: "\f1d6";
}
.fa-fa-paper-plane:before {
  content: "\f1d8";
}
.fa-fa-history:before {
  content: "\f1da";
}
.fa-fa-heading:before {
  content: "\f1dc";
}
.fa-fa-paragraph:before {
  content: "\f1dd";
}
.fa-fa-share-alt:before {
  content: "\f1e0";
}
.fa-fa-bomb:before {
  content: "\f1e2";
}
.fa-fa-futbol:before {
  content: "\f1e3";
}
.fa-fa-binoculars:before {
  content: "\f1e5";
}
.fa-fa-plug:before {
  content: "\f1e6";
}
.fa-fa-newspaper:before {
  content: "\f1ea";
}
.fa-fa-wifi:before {
  content: "\f1eb";
}
.fa-fa-calculator:before {
  content: "\f1ec";
}
.fa-fa-cc-visa:before {
  content: "\f1f0";
}
.fa-fa-cc-mastercard:before {
  content: "\f1f1";
}
.fa-fa-cc-discover:before {
  content: "\f1f2";
}
.fa-fa-cc-amex:before {
  content: "\f1f3";
}
.fa-fa-cc-paypal:before {
  content: "\f1f4";
}
.fa-fa-cc-stripe:before {
  content: "\f1f5";
}
.fa-fa-bell-slash:before {
  content: "\f1f6";
}
.fa-fa-trash:before {
  content: "\f1f8";
}
.fa-fa-copyright:before {
  content: "\f1f9";
}
.fa-fa-at:before {
  content: "\f1fa";
}
.fa-fa-eye-dropper:before {
  content: "\f1fb";
}
.fa-fa-paint-brush:before {
  content: "\f1fc";
}
.fa-fa-birthday-cake:before {
  content: "\f1fd";
}
.fa-fa-chart-area:before {
  content: "\f1fe";
}
.fa-fa-chart-pie:before {
  content: "\f200";
}
.fa-fa-chart-line:before {
  content: "\f201";
}
.fa-fa-toggle-off:before {
  content: "\f204";
}
.fa-fa-toggle-on:before {
  content: "\f205";
}
.fa-fa-bicycle:before {
  content: "\f206";
}
.fa-fa-bus:before {
  content: "\f207";
}
.fa-fa-angellist:before {
  content: "\f209";
}
.fa-fa-closed-captioning:before {
  content: "\f20a";
}
.fa-fa-buysellads:before {
  content: "\f20d";
}
.fa-fa-connectdevelop:before {
  content: "\f20e";
}
.fa-fa-dashcube:before {
  content: "\f210";
}
.fa-fa-cart-plus:before {
  content: "\f217";
}
.fa-fa-cart-arrow-down:before {
  content: "\f218";
}
.fa-fa-diamond:before {
  content: "\f219";
}
.fa-fa-ship:before {
  content: "\f21a";
}
.fa-fa-motorcycle:before {
  content: "\f21c";
}
.fa-fa-heartbeat:before {
  content: "\f21e";
}
.fa-fa-mars:before {
  content: "\f222";
}
.fa-fa-mercury:before {
  content: "\f223";
}
.fa-fa-mars-double:before {
  content: "\f227";
}
.fa-fa-mars-stroke:before {
  content: "\f229";
}
.fa-fa-mars-stroke-v:before {
  content: "\f22a";
}
.fa-fa-mars-stroke-h:before {
  content: "\f22b";
}
.fa-fa-genderless:before {
  content: "\f22d";
}
.fa-fa-whatsapp:before {
  content: "\f232";
}
.fa-fa-server:before {
  content: "\f233";
}
.fa-fa-user-plus:before {
  content: "\f234";
}
.fa-fa-user-times:before {
  content: "\f235";
}
.fa-fa-bed:before {
  content: "\f236";
}
.fa-fa-train:before {
  content: "\f238";
}
.fa-fa-battery-full:before {
  content: "\f240";
}
.fa-fa-battery-three-quarters:before {
  content: "\f241";
}
.fa-fa-battery-half:before {
  content: "\f242";
}
.fa-fa-battery-quarter:before {
  content: "\f243";
}
.fa-fa-battery-empty:before {
  content: "\f244";
}
.fa-fa-mouse-pointer:before {
  content: "\f245";
}
.fa-fa-i-cursor:before {
  content: "\f246";
}
.fa-fa-object-group:before {
  content: "\f247";
}
.fa-fa-sticky-note:before {
  content: "\f249";
}
.fa-fa-cc-jcb:before {
  content: "\f24b";
}
.fa-fa-cc-diners-club:before {
  content: "\f24c";
}
.fa-fa-clone:before {
  content: "\f24d";
}
.fa-fa-balance-scale:before {
  content: "\f24e";
}
.fa-fa-hourglass-start:before {
  content: "\f251";
}
.fa-fa-hourglass-half:before {
  content: "\f252";
}
.fa-fa-hourglass-end:before {
  content: "\f253";
}
.fa-fa-hourglass:before {
  content: "\f254";
}
.fa-fa-hand-rock:before {
  content: "\f255";
}
.fa-fa-hand-paper:before {
  content: "\f256";
}
.fa-fa-hand-scissors:before {
  content: "\f257";
}
.fa-fa-hand-lizard:before {
  content: "\f258";
}
.fa-fa-hand-spock:before {
  content: "\f259";
}
.fa-fa-hand-pointer:before {
  content: "\f25a";
}
.fa-fa-hand-peace:before {
  content: "\f25b";
}
.fa-fa-trademark:before {
  content: "\f25c";
}
.fa-fa-registered:before {
  content: "\f25d";
}
.fa-fa-creative-commons:before {
  content: "\f25e";
}
.fa-fa-gg-circle:before {
  content: "\f261";
}
.fa-fa-chrome:before {
  content: "\f268";
}
.fa-fa-tv:before {
  content: "\f26c";
}
.fa-fa-contao:before {
  content: "\f26d";
}
.fa-fa-500px:before {
  content: "\f26e";
}
.fa-fa-amazon:before {
  content: "\f270";
}
.fa-fa-calendar-plus:before {
  content: "\f271";
}
.fa-fa-calendar-minus:before {
  content: "\f272";
}
.fa-fa-calendar-times:before {
  content: "\f273";
}
.fa-fa-calendar-check:before {
  content: "\f274";
}
.fa-fa-industry:before {
  content: "\f275";
}
.fa-fa-map-pin:before {
  content: "\f276";
}
.fa-fa-map-signs:before {
  content: "\f277";
}
.fa-fa-comment-alt:before {
  content: "\f27a";
}
.fa-fa-black-tie:before {
  content: "\f27e";
}
.fa-fa-codiepie:before {
  content: "\f284";
}
.fa-fa-pause-circle:before {
  content: "\f28b";
}
.fa-fa-stop-circle:before {
  content: "\f28d";
}
.fa-fa-hashtag:before {
  content: "\f292";
}
.fa-fa-bluetooth:before {
  content: "\f293";
}
.fa-fa-bluetooth-b:before {
  content: "\f294";
}
.fa-fa-universal-access:before {
  content: "\f29a";
}
.fa-fa-blind:before {
  content: "\f29d";
}
.fa-fa-audio-description:before {
  content: "\f29e";
}
.fa-fa-braille:before {
  content: "\f2a1";
}
.fa-fa-assistive-listening-systems:before {
  content: "\f2a2";
}
.fa-fa-american-sign-language-interpreting:before {
  content: "\f2a3";
}
.fa-fa-deaf:before {
  content: "\f2a4";
}
.fa-fa-low-vision:before {
  content: "\f2a8";
}
.fa-fa-handshake:before {
  content: "\f2b5";
}
.fa-fa-envelope-open:before {
  content: "\f2b6";
}
.fa-fa-address-book:before {
  content: "\f2b9";
}
.fa-fa-address-card:before {
  content: "\f2bb";
}
.fa-fa-user-circle:before {
  content: "\f2bd";
}
.fa-fa-id-badge:before {
  content: "\f2c1";
}
.fa-fa-id-card:before {
  content: "\f2c2";
}
.fa-fa-thermometer-full:before {
  content: "\f2c7";
}
.fa-fa-shower:before {
  content: "\f2cc";
}
.fa-fa-podcast:before {
  content: "\f2ce";
}
.fa-fa-window-restore:before {
  content: "\f2d2";
}
.fa-fa-microchip:before {
  content: "\f2db";
}
.fa-fa-snowflake:before {
  content: "\f2dc";
}
.fa-fa-watch:before {
  content: "\f2e1";
}
.fa-fa-utensils-alt:before {
  content: "\f2e6";
}
.fa-fa-trophy:before {
  content: "\f2eb";
}
.fa-fa-triangle:before {
  content: "\f2ec";
}
.fa-fa-trash-alt:before {
  content: "\f2ed";
}
.fa-fa-sync-alt:before {
  content: "\f2f1";
}
.fa-fa-stopwatch:before {
  content: "\f2f2";
}
.fa-fa-spade:before {
  content: "\f2f4";
}
.fa-fa-sign-out-alt:before {
  content: "\f2f5";
}
.fa-fa-sign-in-alt:before {
  content: "\f2f6";
}
.fa-uniF2F7:before {
  content: "\f2f7";
}
.fa-uniF2F8:before {
  content: "\f2f8";
}
.fa-fa-rectangle-landscape:before {
  content: "\f2fa";
}
.fa-fa-rectangle-portrait:before {
  content: "\f2fb";
}
.fa-fa-poo:before {
  content: "\f2fe";
}
.fa-fa-images:before {
  content: "\f302";
}
.fa-pencil-alt-light:before {
  content: "\f303";
}
.fa-fa-octagon:before {
  content: "\f306";
}
.fa-fa-minus-hexagon:before {
  content: "\f307";
}
.fa-fa-minus-octagon:before {
  content: "\f308";
}
.fa-fa-long-arrow-alt-down:before {
  content: "\f309";
}
.fa-fa-long-arrow-alt-left:before {
  content: "\f30a";
}
.fa-fa-long-arrow-alt-right:before {
  content: "\f30b";
}
.fa-fa-long-arrow-alt-up:before {
  content: "\f30c";
}
.fa-fa-lock-alt:before {
  content: "\f30d";
}
.fa-fa-jack-o-lantern:before {
  content: "\f30e";
}
.fa-fa-info-square:before {
  content: "\f30f";
}
.fa-fa-inbox-in:before {
  content: "\f310";
}
.fa-fa-inbox-out:before {
  content: "\f311";
}
.fa-fa-hexagon:before {
  content: "\f312";
}
.fa-fa-h1:before {
  content: "\f313";
}
.fa-fa-h2:before {
  content: "\f314";
}
.fa-fa-h3:before {
  content: "\f315";
}
.fa-fa-file-check:before {
  content: "\f316";
}
.fa-fa-file-times:before {
  content: "\f317";
}
.fa-fa-file-minus:before {
  content: "\f318";
}
.fa-fa-file-plus:before {
  content: "\f319";
}
.fa-fa-file-exclamation:before {
  content: "\f31a";
}
.fa-fa-file-edit:before {
  content: "\f31c";
}
.fa-fa-expand-arrows:before {
  content: "\f31d";
}
.fa-fa-expand-arrows-alt:before {
  content: "\f31e";
}
.fa-fa-expand-wide:before {
  content: "\f320";
}
.fa-fa-exclamation-square:before {
  content: "\f321";
}
.fa-fa-chevron-double-down:before {
  content: "\f322";
}
.fa-fa-chevron-double-left:before {
  content: "\f323";
}
.fa-fa-chevron-double-right:before {
  content: "\f324";
}
.fa-fa-chevron-double-up:before {
  content: "\f325";
}
.fa-fa-compress-wide:before {
  content: "\f326";
}
.fa-fa-club:before {
  content: "\f327";
}
.fa-fa-clipboard:before {
  content: "\f328";
}
.fa-fa-chevron-square-down:before {
  content: "\f329";
}
.fa-fa-chevron-square-left:before {
  content: "\f32a";
}
.fa-fa-chevron-square-right:before {
  content: "\f32b";
}
.fa-fa-chevron-square-up:before {
  content: "\f32c";
}
.fa-fa-caret-circle-down:before {
  content: "\f32d";
}
.fa-fa-caret-circle-left:before {
  content: "\f32e";
}
.fa-fa-caret-circle-right:before {
  content: "\f330";
}
.fa-fa-caret-circle-up:before {
  content: "\f331";
}
.fa-fa-camera-alt:before {
  content: "\f332";
}
.fa-fa-calendar-exclamation:before {
  content: "\f334";
}
.fa-fa-badge:before {
  content: "\f335";
}
.fa-fa-badge-check:before {
  content: "\f336";
}
.fa-fa-arrows-alt-h:before {
  content: "\f337";
}
.fa-fa-arrows-alt-v:before {
  content: "\f338";
}
.fa-fa-arrow-square-down:before {
  content: "\f339";
}
.fa-fa-arrow-square-left:before {
  content: "\f33a";
}
.fa-fa-arrow-square-right:before {
  content: "\f33b";
}
.fa-fa-arrow-square-up:before {
  content: "\f33c";
}
.fa-fa-arrow-to-bottom:before {
  content: "\f33d";
}
.fa-fa-arrow-to-left:before {
  content: "\f33e";
}
.fa-fa-arrow-to-right:before {
  content: "\f340";
}
.fa-fa-arrow-to-top:before {
  content: "\f341";
}
.fa-fa-arrow-from-bottom:before {
  content: "\f342";
}
.fa-fa-arrow-from-left:before {
  content: "\f343";
}
.fa-fa-arrow-from-right:before {
  content: "\f344";
}
.fa-fa-arrow-from-top:before {
  content: "\f345";
}
.fa-fa-arrow-alt-from-bottom:before {
  content: "\f346";
}
.fa-fa-arrow-alt-from-left:before {
  content: "\f347";
}
.fa-fa-arrow-alt-from-right:before {
  content: "\f348";
}
.fa-fa-arrow-alt-from-top:before {
  content: "\f349";
}
.fa-fa-arrow-alt-to-bottom:before {
  content: "\f34a";
}
.fa-fa-arrow-alt-to-left:before {
  content: "\f34b";
}
.fa-fa-arrow-alt-to-right:before {
  content: "\f34c";
}
.fa-fa-arrow-alt-to-top:before {
  content: "\f34d";
}
.fa-fa-alarm-clock:before {
  content: "\f34e";
}
.fa-fa-arrow-alt-square-down:before {
  content: "\f350";
}
.fa-fa-arrow-alt-square-left:before {
  content: "\f351";
}
.fa-fa-arrow-alt-square-right:before {
  content: "\f352";
}
.fa-fa-arrow-alt-square-up-:before {
  content: "\f353";
}
.fa-fa-arrow-alt-down:before {
  content: "\f354";
}
.fa-fa-arrow-alt-left:before {
  content: "\f355";
}
.fa-fa-arrow-alt-right:before {
  content: "\f356";
}
.fa-fa-arrow-alt-up:before {
  content: "\f357";
}
.fa-fa-arrow-alt-circle-down:before {
  content: "\f358";
}
.fa-fa-arrow-alt-circle-left:before {
  content: "\f359";
}
.fa-fa-arrow-alt-circle-right:before {
  content: "\f35a";
}
.fa-fa-arrow-alt-circle-up:before {
  content: "\f35b";
}
.fa-fa-external-link-alt:before {
  content: "\f35d";
}
.fa-fa-external-link-square-alt:before {
  content: "\f360";
}
.fa-fa-exchange-alt:before {
  content: "\f362";
}
.fa-fa-repeat:before {
  content: "\f363";
}
.fa-fa-accessible-icon:before {
  content: "\f368";
}
.fa-fa-accusoft:before {
  content: "\f369";
}
.fa-fa-adversalbrands:before {
  content: "\f36a";
}
.fa-fa-affiliatetheme:before {
  content: "\f36b";
}
.fa-fa-algolia:before {
  content: "\f36c";
}
.fa-fa-amilia:before {
  content: "\f36d";
}
.fa-fa-app-store:before {
  content: "\f36f";
}
.fa-fa-app-store-ios:before {
  content: "\f370";
}
.fa-fa-asymmetrik:before {
  content: "\f372";
}
.fa-fa-avianex:before {
  content: "\f374";
}
.fa-fa-aws:before {
  content: "\f375";
}
.fa-fa-battery-bolt:before {
  content: "\f376";
}
.fa-fa-battery-slash:before {
  content: "\f377";
}
.fa-fa-bitcoin:before {
  content: "\f379";
}
.fa-fa-bity:before {
  content: "\f37a";
}
.fa-fa-blackberry:before {
  content: "\f37b";
}
.fa-fa-blogger:before {
  content: "\f37c";
}
.fa-fa-blogger-b:before {
  content: "\f37d";
}
.fa-fa-browser:before {
  content: "\f37e";
}
.fa-fa-buromobelexperte:before {
  content: "\f37f";
}
.fa-fa-centercode:before {
  content: "\f380";
}
.fa-fa-cloud-download-alt:before {
  content: "\f381";
}
.fa-fa-cloud-upload-alt:before {
  content: "\f382";
}
.fa-fa-cloudscale:before {
  content: "\f383";
}
.fa-fa-cloudsmith:before {
  content: "\f384";
}
.fa-fa-cloudversify:before {
  content: "\f385";
}
.fa-fa-code-commit:before {
  content: "\f386";
}
.fa-fa-code-merge:before {
  content: "\f387";
}
.fa-fa-cpanel:before {
  content: "\f388";
}
.fa-fa-credit-card-blank:before {
  content: "\f389";
}
.fa-fa-credit-card-front:before {
  content: "\f38a";
}
.fa-fa-css3-alt:before {
  content: "\f38b";
}
.fa-fa-cuttlefish:before {
  content: "\f38c";
}
.fa-fa-d-and-d:before {
  content: "\f38d";
}
.fa-fa-deskpro:before {
  content: "\f38f";
}
.fa-fa-desktop-alt:before {
  content: "\f390";
}
.fa-fa-ellipsis-v-alt:before {
  content: "\f39c";
}
.fa-fa-film-alt:before {
  content: "\f3a0";
}
.fa-fa-gem:before {
  content: "\f3a5";
}
.fa-fa-industry-alt:before {
  content: "\f3b3";
}
.fa-fa-level-down-alt:before {
  content: "\f3be";
}
.fa-fa-level-up-alt:before {
  content: "\f3bf";
}
.fa-fa-lock-open:before {
  content: "\f3c1";
}
.fa-fa-lock-open-alt:before {
  content: "\f3c2";
}
.fa-fa-map-marker-alt:before {
  content: "\f3c5";
}
.fa-fa-microphone-alt:before {
  content: "\f3c9";
}
.fa-fa-mobile-alt:before {
  content: "\f3cd";
}
.fa-fa-mobile-android:before {
  content: "\f3ce";
}
.fa-fa-mobile-android-alt:before {
  content: "\f3cf";
}
.fa-fa-money-bill-alt:before {
  content: "\f3d1";
}
.fa-fa-portrait:before {
  content: "\f3e0";
}
.fa-fa-reply:before {
  content: "\f3e5";
}
.fa-fa-sliders-v:before {
  content: "\f3f1";
}
.fa-fa-sliders-v-square:before {
  content: "\f3f2";
}
.fa-fa-user-alt:before {
  content: "\f406";
}
.fa-fa-window-alt:before {
  content: "\f40f";
}
.fa-fa-apple-pay:before {
  content: "\f415";
}
.fa-fa-cc-apple-pay:before {
  content: "\f416";
}
.fa-fa-autoprefixer:before {
  content: "\f41c";
}
.fa-fa-angular:before {
  content: "\f420";
}
.fa-fa-compress-alt:before {
  content: "\f422";
}
.fa-fa-expand-alt:before {
  content: "\f424";
}
.fa-fa-amazon-pay:before {
  content: "\f42c";
}
.fa-fa-cc-amazon-pay:before {
  content: "\f42d";
}
.fa-fa-baseball:before {
  content: "\f432";
}
.fa-fa-baseball-ball:before {
  content: "\f433";
}
.fa-fa-basketball-ball:before {
  content: "\f434";
}
.fa-fa-basketball-hoop:before {
  content: "\f435";
}
.fa-fa-bowling-ball:before {
  content: "\f436";
}
.fa-fa-bowling-pins:before {
  content: "\f437";
}
.fa-fa-boxing-glove:before {
  content: "\f438";
}
.fa-fa-chess:before {
  content: "\f439";
}
.fa-fa-chess-bishop:before {
  content: "\f43a";
}
.fa-fa-chess-bishop-alt:before {
  content: "\f43b";
}
.fa-fa-chess-board:before {
  content: "\f43c";
}
.fa-fa-chess-clock:before {
  content: "\f43d";
}
.fa-fa-chess-clock-alt:before {
  content: "\f43e";
}
.fa-fa-chess-king:before {
  content: "\f43f";
}
.fa-fa-chess-king-alt:before {
  content: "\f440";
}
.fa-fa-chess-knight:before {
  content: "\f441";
}
.fa-fa-chess-knight-alt:before {
  content: "\f442";
}
.fa-fa-chess-pawn:before {
  content: "\f443";
}
.fa-fa-chess-pawn-alt:before {
  content: "\f444";
}
.fa-fa-chess-queen:before {
  content: "\f445";
}
.fa-fa-chess-queen-alt:before {
  content: "\f446";
}
.fa-fa-chess-rook:before {
  content: "\f447";
}
.fa-fa-chess-rook-alt:before {
  content: "\f448";
}
.fa-fa-cricket:before {
  content: "\f449";
}
.fa-fa-curling:before {
  content: "\f44a";
}
.fa-fa-dumbbell:before {
  content: "\f44b";
}
.fa-fa-field-hockey:before {
  content: "\f44c";
}
.fa-fa-football-ball:before {
  content: "\f44e";
}
.fa-fa-football-helmet:before {
  content: "\f44f";
}
.fa-fa-golf-ball:before {
  content: "\f450";
}
.fa-fa-golf-club:before {
  content: "\f451";
}
.fa-fa-hockey-puck:before {
  content: "\f453";
}
.fa-fa-hockey-sticks:before {
  content: "\f454";
}
.fa-fa-luchador:before {
  content: "\f455";
}
.fa-fa-racquet:before {
  content: "\f45a";
}
.fa-fa-shuttlecock:before {
  content: "\f45b";
}
.fa-fa-square-full:before {
  content: "\f45c";
}
.fa-fa-table-tennis:before {
  content: "\f45d";
}
.fa-fa-tennis-ball:before {
  content: "\f45e";
}
.fa-fa-whistle:before {
  content: "\f460";
}
.fa-fa-allergies:before {
  content: "\f461";
}
.fa-fa-band-aid:before {
  content: "\f462";
}
.fa-fa-barcode-alt:before {
  content: "\f463";
}
.fa-fa-barcode-read:before {
  content: "\f464";
}
.fa-fa-barcode-scan:before {
  content: "\f465";
}
.fa-fa-box:before {
  content: "\f466";
}
.fa-fa-box-check:before {
  content: "\f467";
}
.fa-fa-boxes:before {
  content: "\f468";
}
.fa-fa-briefcase-medical:before {
  content: "\f469";
}
.fa-fa-burn:before {
  content: "\f46a";
}
.fa-fa-capsules:before {
  content: "\f46b";
}
.fa-fa-clipboard-check:before {
  content: "\f46c";
}
.fa-fa-clipboard-list:before {
  content: "\f46d";
}
.fa-fa-conveyor-belt:before {
  content: "\f46e";
}
.fa-fa-conveyor-belt-alt:before {
  content: "\f46f";
}
.fa-fa-diagnoses:before {
  content: "\f470";
}
.fa-fa-dna:before {
  content: "\f471";
}
.fa-fa-dolly:before {
  content: "\f472";
}
.fa-fa-dolly-empty:before {
  content: "\f473";
}
.fa-fa-dolly-flatbed:before {
  content: "\f474";
}
.fa-fa-dolly-flatbed-alt:before {
  content: "\f475";
}
.fa-fa-dolly-flatbed-empty:before {
  content: "\f476";
}
.fa-fa-file-medical:before {
  content: "\f477";
}
.fa-fa-file-medical-alt:before {
  content: "\f478";
}
.fa-fa-first-aid:before {
  content: "\f479";
}
.fa-fa-forklift:before {
  content: "\f47a";
}
.fa-fa-hand-holding-box:before {
  content: "\f47b";
}
.fa-fa-hand-receiving:before {
  content: "\f47c";
}
.fa-fa-hospital-alt:before {
  content: "\f47d";
}
.fa-fa-hospital-symbol:before {
  content: "\f47e";
}
.fa-fa-id-card-alt:before {
  content: "\f47f";
}
.fa-fa-inventory:before {
  content: "\f480";
}
.fa-fa-pills:before {
  content: "\f484";
}
.fa-fa-smoking:before {
  content: "\f48d";
}
.fa-fa-syringe:before {
  content: "\f48e";
}
.fa-fa-tablets:before {
  content: "\f490";
}
.fa-fa-warehouse:before {
  content: "\f494";
}
.fa-fa-weight:before {
  content: "\f496";
}
.fa-fa-x-ray:before {
  content: "\f497";
}
.fa-fa-blanket:before {
  content: "\f498";
}
.fa-fa-book-heart:before {
  content: "\f499";
}
.fa-fa-box-alt:before {
  content: "\f49a";
}
.fa-box-fragile:before {
  content: "\f49b";
}
.fa-fa-box-full:before {
  content: "\f49c";
}
.fa-fa-box-heart:before {
  content: "\f49d";
}
.fa-fa-box-open:before {
  content: "\f49e";
}
.fa-fa-box-up:before {
  content: "\f49f";
}
.fa-fa-box-usd:before {
  content: "\f4a0";
}
.fa-fa-boxes-alt:before {
  content: "\f4a1";
}
.fa-fa-comment-alt-check:before {
  content: "\f4a2";
}
.fa-fa-comment-alt-dots:before {
  content: "\f4a3";
}
.fa-fa-comment-alt-edit:before {
  content: "\f4a4";
}
.fa-fa-comment-alt-exclamation:before {
  content: "\f4a5";
}
.fa-fa-comment-alt-lines:before {
  content: "\f4a6";
}
.fa-fa-comment-alt-minus:before {
  content: "\f4a7";
}
.fa-fa-comment-alt-plus:before {
  content: "\f4a8";
}
.fa-fa-comment-alt-smile:before {
  content: "\f4aa";
}
.fa-fa-comment-alt-times:before {
  content: "\f4ab";
}
.fa-fa-comment-check:before {
  content: "\f4ac";
}
.fa-fa-comment-dots:before {
  content: "\f4ad";
}
.fa-fa-comment-edit:before {
  content: "\f4ae";
}
.fa-fa-comment-exclamation:before {
  content: "\f4af";
}
.fa-fa-comment-lines:before {
  content: "\f4b0";
}
.fa-fa-comment-minus:before {
  content: "\f4b1";
}
.fa-fa-comment-plus:before {
  content: "\f4b2";
}
.fa-fa-comment-slash:before {
  content: "\f4b3";
}
.fa-fa-comment-smile:before {
  content: "\f4b4";
}
.fa-fa-comment-times:before {
  content: "\f4b5";
}
.fa-fa-comments-alt:before {
  content: "\f4b6";
}
.fa-fa-container-storage:before {
  content: "\f4b7";
}
.fa-fa-couch:before {
  content: "\f4b8";
}
.fa-fa-donate:before {
  content: "\f4b9";
}
.fa-fa-dove:before {
  content: "\f4ba";
}
.fa-fa-fragile:before {
  content: "\f4bb";
}
.fa-fa-hand-heart:before {
  content: "\f4bc";
}
.fa-fa-hand-holding:before {
  content: "\f4bd";
}
.fa-fa-hand-holding-heart:before {
  content: "\f4be";
}
.fa-fa-hand-holding-seedling:before {
  content: "\f4bf";
}
.fa-fa-hand-holding-usd:before {
  content: "\f4c0";
}
.fa-fa-hand-holding-water:before {
  content: "\f4c1";
}
.fa-fa-hands:before {
  content: "\f4c2";
}
.fa-fa-hands-heart:before {
  content: "\f4c3";
}
.fa-fa-hands-helping:before {
  content: "\f4c4";
}
.fa-fa-hands-usd:before {
  content: "\f4c5";
}
.fa-fa-handshake-alt:before {
  content: "\f4c6";
}
.fa-fa-heart-circle:before {
  content: "\f4c7";
}
.fa-fa-heart-square:before {
  content: "\f4c8";
}
.fa-fa-home-heart:before {
  content: "\f4c9";
}
.fa-fa-lamp:before {
  content: "\f4ca";
}
.fa-fa-leaf-heart:before {
  content: "\f4cb";
}
.fa-fa-parachute-box:before {
  content: "\f4cd";
}
.fa-fa-piggy-bank:before {
  content: "\f4d3";
}
.fa-fa-ribbon:before {
  content: "\f4d6";
}
.fa-fa-route:before {
  content: "\f4d7";
}
.fa-fa-seedling:before {
  content: "\f4d8";
}
.fa-fa-creative-commons-by:before {
  content: "\f4e7";
}
.fa-fa-creative-commons-nc:before {
  content: "\f4e8";
}
.fa-fa-creative-commons-nc-jp:before {
  content: "\f4ea";
}
.fa-fa-creative-commons-nd:before {
  content: "\f4eb";
}
.fa-fa-creative-commons-pd:before {
  content: "\f4ec";
}
.fa-fa-creative-commons-pd-alt:before {
  content: "\f4ed";
}
.fa-fa-creative-commons-remix:before {
  content: "\f4ee";
}
.fa-fa-creative-commons-sampling:before {
  content: "\f4f0";
}
.fa-fa-creative-commons-sampling-plus:before {
  content: "\f4f1";
}
.fa-fa-creative-commons-share:before {
  content: "\f4f2";
}
.fa-fa-user-cog:before {
  content: "\f4fe";
}
.fa-fa-user-friends:before {
  content: "\f500";
}
.fa-fa-user-slash:before {
  content: "\f506";
}
.fa-fa-user-tie:before {
  content: "\f508";
}
.fa-fa-balance-scale-left:before {
  content: "\f515";
}
.fa-fa-balance-scale-right:before {
  content: "\f516";
}
.fa-fa-blender:before {
  content: "\f517";
}
.fa-fa-book-open:before {
  content: "\f518";
}
.fa-fa-broadcast-tower:before {
  content: "\f519";
}
.fa-fa-broom:before {
  content: "\f51a";
}
.fa-fa-chalkboard:before {
  content: "\f51b";
}
.fa-fa-chalkboard-teacher:before {
  content: "\f51c";
}
.fa-fa-church:before {
  content: "\f51d";
}
.fa-fa-coins:before {
  content: "\f51e";
}
.fa-fa-compact-disc:before {
  content: "\f51f";
}
.fa-fa-crow:before {
  content: "\f520";
}
.fa-fa-crown:before {
  content: "\f521";
}
.fa-fa-dice:before {
  content: "\f522";
}
.fa-fa-dice-five:before {
  content: "\f523";
}
.fa-fa-dice-four:before {
  content: "\f524";
}
.fa-fa-dice-one:before {
  content: "\f525";
}
.fa-fa-dice-six:before {
  content: "\f526";
}
.fa-fa-dice-three:before {
  content: "\f527";
}
.fa-fa-dice-two:before {
  content: "\f528";
}
.fa-fa-divide:before {
  content: "\f529";
}
.fa-fa-door-closed:before {
  content: "\f52a";
}
.fa-fa-door-open:before {
  content: "\f52b";
}
.fa-fa-equals:before {
  content: "\f52c";
}
.fa-fa-feather:before {
  content: "\f52d";
}
.fa-fa-frog:before {
  content: "\f52e";
}
.fa-fa-gas-pump:before {
  content: "\f52f";
}
.fa-fa-glasses:before {
  content: "\f530";
}
.fa-fa-greater-than:before {
  content: "\f531";
}
.fa-fa-greater-than-equal:before {
  content: "\f532";
}
.fa-fa-helicopter:before {
  content: "\f533";
}
.fa-fa-infinity:before {
  content: "\f534";
}
.fa-fa-kiwi-bird:before {
  content: "\f535";
}
.fa-fa-less-than:before {
  content: "\f536";
}
.fa-fa-less-than-equal:before {
  content: "\f537";
}
.fa-fa-memory:before {
  content: "\f538";
}
.fa-fa-microphone-alt-slash:before {
  content: "\f539";
}
.fa-fa-money-bill-wave:before {
  content: "\f53a";
}
.fa-fa-money-bill-wave-alt:before {
  content: "\f53b";
}
.fa-fa-money-check:before {
  content: "\f53c";
}
.fa-fa-money-check-alt:before {
  content: "\f53d";
}
.fa-fa-not-equal:before {
  content: "\f53e";
}
.fa-fa-palette:before {
  content: "\f53f";
}
.fa-fa-percentage:before {
  content: "\f541";
}
.fa-fa-project-diagram:before {
  content: "\f542";
}
.fa-fa-receipt:before {
  content: "\f543";
}
.fa-fa-robot:before {
  content: "\f544";
}
.fa-fa-ruler:before {
  content: "\f545";
}
.fa-fa-school:before {
  content: "\f549";
}
.fa-fa-screwdriver:before {
  content: "\f54a";
}
.fa-faa-shoe-prints:before {
  content: "\f54b";
}
.fa-fa-skull:before {
  content: "\f54c";
}
.fa-fa-store:before {
  content: "\f54e";
}
.fa-fa-toolbox:before {
  content: "\f552";
}
.fa-fa-tshirt:before {
  content: "\f553";
}
.fa-fa-wallet:before {
  content: "\f555";
}
.fa-fa-angry:before {
  content: "\f556";
}
.fa-fa-archway:before {
  content: "\f557";
}
.fa-fa-atlas:before {
  content: "\f558";
}
.fa-fa-award:before {
  content: "\f559";
}
.fa-fa-backspace:before {
  content: "\f55a";
}
.fa-fa-bezier-curve:before {
  content: "\f55b";
}
.fa-fa-bong:before {
  content: "\f55c";
}
.fa-fa-brush:before {
  content: "\f55d";
}
.fa-fa-cannabis:before {
  content: "\f55e";
}
.fa-fa-check-double:before {
  content: "\f560";
}
.fa-fa-cocktail:before {
  content: "\f561";
}
.fa-fa-concierge-bell:before {
  content: "\f562";
}
.fa-fa-cookie:before {
  content: "\f563";
}
.fa-fa-cookie-bite:before {
  content: "\f564";
}
.fa-fa-crop-alt:before {
  content: "\f565";
}
.fa-fa-digital-tachograph:before {
  content: "\f566";
}
.fa-fa-dizzy:before {
  content: "\f567";
}
.fa-fa-drafting-compass:before {
  content: "\f568";
}
.fa-fa-drum:before {
  content: "\f569";
}
.fa-fa-drum-steelpan:before {
  content: "\f56a";
}
.fa-fa-feather-alt:before {
  content: "\f56b";
}
.fa-fa-file-contract:before {
  content: "\f56c";
}
.fa-fa-file-download:before {
  content: "\f56d";
}
.fa-fa-file-export:before {
  content: "\f56e";
}
.fa-fa-file-import:before {
  content: "\f56f";
}
.fa-fa-file-invoice:before {
  content: "\f570";
}
.fa-fa-file-invoice-dollar:before {
  content: "\f571";
}
.fa-fa-file-prescription:before {
  content: "\f572";
}
.fa-fa-file-certificate:before {
  content: "\f573";
}
.fa-fa-file-upload:before {
  content: "\f574";
}
.fa-fa-fill:before {
  content: "\f575";
}
.fa-fa-fill-drip:before {
  content: "\f576";
}
.fa-fa-fingerprint:before {
  content: "\f577";
}
.fa-fa-fish:before {
  content: "\f578";
}
.fa-fa-flushed:before {
  content: "\f579";
}
.fa-fa-frown-open:before {
  content: "\f57a";
}
.fa-fa-glass-martini-alt:before {
  content: "\f57b";
}
.fa-fa-globe-africa:before {
  content: "\f57c";
}
.fa-fa-globe-americas:before {
  content: "\f57d";
}
.fa-fa-globe-asia:before {
  content: "\f57e";
}
.fa-fa-grimace:before {
  content: "\f57f";
}
.fa-fa-grin:before {
  content: "\f580";
}
.fa-fa-grin-alt:before {
  content: "\f581";
}
.fa-fa-grin-beam:before {
  content: "\f582";
}
.fa-fa-grin-beam-sweat:before {
  content: "\f583";
}
.fa-fa-grin-hearts:before {
  content: "\f584";
}
.fa-fa-grin-squint:before {
  content: "\f585";
}
.fa-fa-grin-squint-tears:before {
  content: "\f586";
}
.fa-fa-grin-stars:before {
  content: "\f587";
}
.fa-fa-grin-tears:before {
  content: "\f588";
}
.fa-fa-grin-tongue:before {
  content: "\f589";
}
.fa-fa-grin-tongue-squint:before {
  content: "\f58a";
}
.fa-fa-grin-tongue-wink:before {
  content: "\f58b";
}
.fa-fa-grin-wink:before {
  content: "\f58c";
}
.fa-fa-grip-horizontal:before {
  content: "\f58d";
}
.fa-fa-grip-vertical:before {
  content: "\f58e";
}
.fa-fa-headphones-alt:before {
  content: "\f58f";
}
.fa-fa-highlighter:before {
  content: "\f591";
}
.fa-fa-hot-tub:before {
  content: "\f593";
}
.fa-fa-hotel:before {
  content: "\f594";
}
.fa-fa-joint:before {
  content: "\f595";
}
.fa-fa-kiss:before {
  content: "\f596";
}
.fa-fa-kiss-beam:before {
  content: "\f597";
}
.fa-fa-kiss-wink-heart:before {
  content: "\f598";
}
.fa-fa-laugh:before {
  content: "\f599";
}
.fa-fa-laugh-beam:before {
  content: "\f59a";
}
.fa-fa-laugh-squint:before {
  content: "\f59b";
}
.fa-fa-laugh-wink:before {
  content: "\f59c";
}
.fa-fa-luggage-cart:before {
  content: "\f59d";
}
.fa-fa-map-marked:before {
  content: "\f59f";
}
.fa-fa-map-marked-alt:before {
  content: "\f5a0";
}
.fa-fa-marker:before {
  content: "\f5a1";
}
.fa-fa-medal:before {
  content: "\f5a2";
}
.fa-fa-meh-blank:before {
  content: "\f5a4";
}
.fa-fa-meh-rolling-eyes:before {
  content: "\f5a5";
}
.fa-fa-monument:before {
  content: "\f5a6";
}
.fa-fa-mortar-pestle:before {
  content: "\f5a7";
}
.fa-fa-paint-roller:before {
  content: "\f5aa";
}
.fa-fa-passport:before {
  content: "\f5ab";
}
.fa-fa-prescription:before {
  content: "\f5b1";
}
.fa-fa-shuttle-van:before {
  content: "\f5b6";
}
.fa-fa-signature:before {
  content: "\f5b7";
}
.fa-fa-solar-panel:before {
  content: "\f5ba";
}
.fa-fa-spray-can:before {
  content: "\f5bd";
}
.fa-fa-stamp:before {
  content: "\f5bf";
}
.fa-fa-swimmer:before {
  content: "\f5c4";
}
.fa-fa-tooth:before {
  content: "\f5c9";
}
.fa-fa-weight-hanging:before {
  content: "\f5cd";
}
.fa-fa-air-freshener:before {
  content: "\f5d0";
}
.fa-fa-apple-alt:before {
  content: "\f5d1";
}
.fa-fa-atom:before {
  content: "\f5d2";
}
.fa-fa-atom-alt:before {
  content: "\f5d3";
}
.fa-fa-backpack:before {
  content: "\f5d4";
}
.fa-fa-bell-school:before {
  content: "\f5d5";
}
.fa-fa-bell-school-slash:before {
  content: "\f5d6";
}
.fa-fa-bone:before {
  content: "\f5d7";
}
.fa-fa-bone-break:before {
  content: "\f5d8";
}
.fa-fa-book-alt:before {
  content: "\f5d9";
}
.fa-fa-book-reader:before {
  content: "\f5da";
}
.fa-fa-books:before {
  content: "\f5db";
}
.fa-fa-brain:before {
  content: "\f5dc";
}
.fa-fa-bus-school:before {
  content: "\f5dd";
}
.fa-fa-car-alt:before {
  content: "\f5de";
}
.fa-fa-car-battery:before {
  content: "\f5df";
}
.fa-fa-car-bump:before {
  content: "\f5e0";
}
.fa-fa-car-crash:before {
  content: "\f5e1";
}
.fa-fa-car-garage:before {
  content: "\f5e2";
}
.fa-fa-car-mechanic:before {
  content: "\f5e3";
}
.fa-fa-car-side:before {
  content: "\f5e4";
}
.fa-fa-car-tilt:before {
  content: "\f5e5";
}
.fa-fa-car-wash:before {
  content: "\f5e6";
}
.fa-fa-charging-station:before {
  content: "\f5e7";
}
.fa-fa-clipboard-prescription:before {
  content: "\f5e8";
}
.fa-fa-compass-slash:before {
  content: "\f5e9";
}
.fa-fa-diploma:before {
  content: "\f5ea";
}
.fa-fa-directions:before {
  content: "\f5eb";
}
.fa-fa-do-not-enter:before {
  content: "\f5ec";
}
.fa-fa-draw-circle:before {
  content: "\f5ed";
}
.fa-fa-draw-polygon:before {
  content: "\f5ee";
}
.fa-fa-draw-square:before {
  content: "\f5ef";
}
.fa-fa-ear:before {
  content: "\f5f0";
}
.fa-fa-engine-warning:before {
  content: "\f5f2";
}
.fa-fa-gas-pump-slash:before {
  content: "\f5f4";
}
.fa-fa-glasses-alt:before {
  content: "\f5f5";
}
.fa-fa-globe-stand:before {
  content: "\f5f6";
}
.fa-fa-heart-rate:before {
  content: "\f5f8";
}
.fa-fa-inhaler:before {
  content: "\f5f9";
}
.fa-fa-kidneys:before {
  content: "\f5fb";
}
.fa-fa-laptop-code:before {
  content: "\f5fc";
}
.fa-fa-layer-group:before {
  content: "\f5fd";
}
.fa-fa-layer-minus:before {
  content: "\f5fe";
}
.fa-fa-layer-plus:before {
  content: "\f5ff";
}
.fa-fa-lips:before {
  content: "\f600";
}
.fa-fa-location:before {
  content: "\f601";
}
.fa-fa-location-slash:before {
  content: "\f603";
}
.fa-fa-lungs:before {
  content: "\f604";
}
.fa-fa-map-marker-alt-slash:before {
  content: "\f605";
}
.fa-fa-map-marker-check:before {
  content: "\f606";
}
.fa-fa-map-marker-edit:before {
  content: "\f607";
}
.fa-fa-map-marker-exclamation:before {
  content: "\f608";
}
.fa-fa-map-marker-minus:before {
  content: "\f609";
}
.fa-fa-map-marker-question:before {
  content: "\f60b";
}
.fa-fa-map-marker-slash:before {
  content: "\f60c";
}
.fa-fa-map-marker-smile:before {
  content: "\f60d";
}
.fa-fa-map-marker-times:before {
  content: "\f60e";
}
.fa-fa-microscope:before {
  content: "\f610";
}
.fa-fa-monitor-heart-rate:before {
  content: "\f611";
}
.fa-fa-oil-can:before {
  content: "\f613";
}
.fa-fa-parking-circle:before {
  content: "\f615";
}
.fa-fa-route-highway:before {
  content: "\f61a";
}
.fa-fa-shapes:before {
  content: "\f61f";
}
.fa-fa-steering-wheel:before {
  content: "\f622";
}
.fa-fa-stomach:before {
  content: "\f623";
}
.fa-fa-teeth-open:before {
  content: "\f62f";
}
.fa-fa-tire:before {
  content: "\f631";
}
.fa-fa-traffic-cone:before {
  content: "\f636";
}
.fa-fa-traffic-light:before {
  content: "\f637";
}
.fa-fa-users-class:before {
  content: "\f63d";
}
.fa-fa-abacus:before {
  content: "\f640";
}
.fa-fa-ad:before {
  content: "\f641";
}
.fa-fa-alipay:before {
  content: "\f642";
}
.fa-fa-analytics:before {
  content: "\f643";
}
.fa-fa-ankh:before {
  content: "\f644";
}
.fa-fa-badge-dollar:before {
  content: "\f645";
}
.fa-fa-badge-percent:before {
  content: "\f646";
}
.fa-fa-bible:before {
  content: "\f647";
}
.fa-fa-bullseye-arrow:before {
  content: "\f648";
}
.fa-fa-bullseye-pointer:before {
  content: "\f649";
}
.fa-fa-business-time:before {
  content: "\f64a";
}
.fa-fa-cabinet-filing:before {
  content: "\f64b";
}
.fa-fa-calculator-alt:before {
  content: "\f64c";
}
.fa-fa-chart-line-down:before {
  content: "\f64d";
}
.fa-fa-chart-pie-alt:before {
  content: "\f64e";
}
.fa-fa-city:before {
  content: "\f64f";
}
.fa-fa-comment-dollar:before {
  content: "\f651";
}
.fa-fa-comments-alt-dollar:before {
  content: "\f652";
}
.fa-fa-comments-dollar:before {
  content: "\f653";
}
.fa-fa-cross:before {
  content: "\f654";
}
.fa-fa-dharmachakra:before {
  content: "\f655";
}
.fa-fa-empty-set:before {
  content: "\f656";
}
.fa-fa-envelope-open-dollar:before {
  content: "\f657";
}
.fa-fa-envelope-open-text:before {
  content: "\f658";
}
.fa-fa-file-chart-line:before {
  content: "\f659";
}
.fa-fa-file-chart-pie:before {
  content: "\f65a";
}
.fa-fa-file-spreadsheet:before {
  content: "\f65b";
}
.fa-fa-file-user:before {
  content: "\f65c";
}
.fa-folder-minus:before {
  content: "\f65d";
}
.fa-fa-folder-plus:before {
  content: "\f65e";
}
.fa-fa-folder-times:before {
  content: "\f65f";
}
.fa-fa-folders:before {
  content: "\f660";
}
.fa-fa-function:before {
  content: "\f661";
}
.fa-fa-funnel-dollar:before {
  content: "\f662";
}
.fa-fa-gift-card:before {
  content: "\f663";
}
.fa-fa-gopuram:before {
  content: "\f664";
}
.fa-fa-hamsa:before {
  content: "\f665";
}
.fa-fa-bahai:before {
  content: "\f666";
}
.fa-fa-integral:before {
  content: "\f667";
}
.fa-fa-intersection:before {
  content: "\f668";
}
.fa-fa-jedi:before {
  content: "\f669";
}
.fa-fa-journal-whills:before {
  content: "\f66a";
}
.fa-fa-kaaba:before {
  content: "\f66b";
}
.fa-fa-keynote:before {
  content: "\f66c";
}
.fa-fa-khanda:before {
  content: "\f66d";
}
.fa-fa-lambda:before {
  content: "\f66e";
}
.fa-fa-landmark:before {
  content: "\f66f";
}
.fa-fa-lightbulb-dollar:before {
  content: "\f670";
}
.fa-fa-lightbulb-exclamation:before {
  content: "\f671";
}
.fa-fa-lightbulb-on:before {
  content: "\f672";
}
.fa-fa-lightbulb-slash:before {
  content: "\f673";
}
.fa-fa-megaphone:before {
  content: "\f675";
}
.fa-fa-menorah:before {
  content: "\f676";
}
.fa-fa-mind-share:before {
  content: "\f677";
}
.fa-fa-mosque:before {
  content: "\f678";
}
.fa-fa-om:before {
  content: "\f679";
}
.fa-fa-omega:before {
  content: "\f67a";
}
.fa-fa-pastafarianism:before {
  content: "\f67b";
}
.fa-fa-peace:before {
  content: "\f67c";
}
.fa-fa-pi:before {
  content: "\f67e";
}
.fa-fa-praying-hands:before {
  content: "\f684";
}
.fa-fa-presentation:before {
  content: "\f685";
}
.fa-fa-quran:before {
  content: "\f687";
}
.fa-fa-sigma:before {
  content: "\f68b";
}
.fa-fa-signal-alt-2:before {
  content: "\f692";
}
.fa-fa-socks:before {
  content: "\f696";
}
.fa-fa-square-root:before {
  content: "\f697";
}
.fa-fa-user-chart:before {
  content: "\f6a3";
}
.fa-fa-volume:before {
  content: "\f6a8";
}
.fa-fa-wifi-slash:before {
  content: "\f6ac";
}
.fa-fa-yin-yang:before {
  content: "\f6ad";
}
.fa-fa-acorn:before {
  content: "\f6ae";
}
.fa-fa-acquisitions-incorporated:before {
  content: "\f6af";
}
.fa-fa-alicorn:before {
  content: "\f6b0";
}
.fa-fa-apple-crate:before {
  content: "\f6b1";
}
.fa-fa-axe:before {
  content: "\f6b2";
}
.fa-fa-axe-battle:before {
  content: "\f6b3";
}
.fa-fa-badger-honey:before {
  content: "\f6b4";
}
.fa-fa-bat:before {
  content: "\f6b5";
}
.fa-fa-blender-phone:before {
  content: "\f6b6";
}
.fa-fa-book-dead:before {
  content: "\f6b7";
}
.fa-fa-book-spells:before {
  content: "\f6b8";
}
.fa-fa-bow-arrow:before {
  content: "\f6b9";
}
.fa-fa-campfire:before {
  content: "\f6ba";
}
.fa-fa-campground:before {
  content: "\f6bb";
}
.fa-fa-candle-holder:before {
  content: "\f6bc";
}
.fa-fa-candy-corn:before {
  content: "\f6bd";
}
.fa-fa-cat:before {
  content: "\f6be";
}
.fa-fa-cauldron:before {
  content: "\f6bf";
}
.fa-fa-chair:before {
  content: "\f6c0";
}
.fa-fa-chair-office:before {
  content: "\f6c1";
}
.fa-fa-claw-marks:before {
  content: "\f6c2";
}
.fa-fa-cloud-moon:before {
  content: "\f6c3";
}
.fa-fa-cloud-sun:before {
  content: "\f6c4";
}
.fa-fa-coffee-togo:before {
  content: "\f6c5";
}
.fa-fa-coffin:before {
  content: "\f6c6";
}
.fa-fa-corn:before {
  content: "\f6c7";
}
.fa-fa-cow:before {
  content: "\f6c8";
}
.fa-fa-critical-role:before {
  content: "\f6c9";
}
.fa-fa-d-and-d-beyond:before {
  content: "\f6ca";
}
.fa-fa-dagger:before {
  content: "\f6cb";
}
.fa-fa-dice-d10:before {
  content: "\f6cd";
}
.fa-fa-dice-d12:before {
  content: "\f6ce";
}
.fa-fa-dice-d20:before {
  content: "\f6cf";
}
.fa-fa-dice-d4:before {
  content: "\f6d0";
}
.fa-fa-dice-d6:before {
  content: "\f6d1";
}
.fa-fa-dice-d8:before {
  content: "\f6d2";
}
.fa-fa-dog:before {
  content: "\f6d3";
}
.fa-fa-dog-leashed:before {
  content: "\f6d4";
}
.fa-fa-dragon:before {
  content: "\f6d5";
}
.fa-fa-drumstick:before {
  content: "\f6d6";
}
.fa-fa-drumstick-bite:before {
  content: "\f6d7";
}
.fa-fa-duck:before {
  content: "\f6d8";
}
.fa-fa-dungeon:before {
  content: "\f6d9";
}
.fa-fa-elephant:before {
  content: "\f6da";
}
.fa-fa-eye-evil:before {
  content: "\f6db";
}
.fa-fa-file-csv:before {
  content: "\f6dd";
}
.fa-fa-fist-raised:before {
  content: "\f6de";
}
.fa-fa-flame:before {
  content: "\f6df";
}
.fa-fa-flask-poison:before {
  content: "\f6e0";
}
.fa-fa-flask-potion:before {
  content: "\f6e1";
}
.fa-fa-ghost:before {
  content: "\f6e2";
}
.fa-fa-hammer:before {
  content: "\f6e3";
}
.fa-fa-hammer-war:before {
  content: "\f6e4";
}
.fa-fa-hand-holding-magic:before {
  content: "\f6e5";
}
.fa-fa-hanukiah:before {
  content: "\f6e6";
}
.fa-fa-hat-witch:before {
  content: "\f6e7";
}
.fa-fa-hat-wizard:before {
  content: "\f6e8";
}
.fa-fa-head-side:before {
  content: "\f6e9";
}
.fa-fa-head-vr:before {
  content: "\f6ea";
}
.fa-fa-helmet-battle:before {
  content: "\f6eb";
}
.fa-fa-hiking:before {
  content: "\f6ec";
}
.fa-fa-hippo:before {
  content: "\f6ed";
}
.fa-fa-hockey-mask:before {
  content: "\f6ee";
}
.fa-fa-hood-cloak:before {
  content: "\f6ef";
}
.fa-fa-horse:before {
  content: "\f6f0";
}
.fa-fa-house-damage:before {
  content: "\f6f1";
}
.fa-fa-hryvnia:before {
  content: "\f6f2";
}
.fa-fa-key-skeleton:before {
  content: "\f6f3";
}
.fa-fa-kite:before {
  content: "\f6f4";
}
.fa-fa-knife-kitchen:before {
  content: "\f6f5";
}
.fa-fa-leaf-maple:before {
  content: "\f6f6";
}
.fa-fa-leaf-oak:before {
  content: "\f6f7";
}
.fa-fa-mace:before {
  content: "\f6f8";
}
.fa-fa-mandolin:before {
  content: "\f6f9";
}
.fa-fa-mask:before {
  content: "\f6fa";
}
.fa-fa-monkey:before {
  content: "\f6fb";
}
.fa-fa-mountain:before {
  content: "\f6fc";
}
.fa-fa-mountains:before {
  content: "\f6fd";
}
.fa-fa-network-wired:before {
  content: "\f6ff";
}
.fa-fa-otter:before {
  content: "\f700";
}
.fa-fa-pie:before {
  content: "\f705";
}
.fa-fa-pumpkin:before {
  content: "\f707";
}
.fa-fa-rabbit:before {
  content: "\f708";
}
.fa-fa-ram:before {
  content: "\f70a";
}
.fa-fa-running:before {
  content: "\f70c";
}
.fa-fa-scarecrow:before {
  content: "\f70d";
}
.fa-fa-scroll:before {
  content: "\f70e";
}
.fa-fa-shovel:before {
  content: "\f713";
}
.fa-fa-slash:before {
  content: "\f715";
}
.fa-fa-snake:before {
  content: "\f716";
}
.fa-fa-spider:before {
  content: "\f717";
}
.fa-fa-spider-web:before {
  content: "\f719";
}
.fa-fa-squirrel:before {
  content: "\f71a";
}
.fa-fa-staff:before {
  content: "\f71b";
}
.fa-fa-sword:before {
  content: "\f71c";
}
.fa-fa-toilet-paper:before {
  content: "\f71e";
}
.fa-fa-tombstone:before {
  content: "\f720";
}
.fa-fa-turtle:before {
  content: "\f726";
}
.fa-fa-vr-cardboard:before {
  content: "\f729";
}
.fa-fa-whale:before {
  content: "\f72c";
}
.fa-fa-wheat:before {
  content: "\f72d";
}
.fa-fa-ballot:before {
  content: "\f732";
}
.fa-fa-ballot-check:before {
  content: "\f733";
}
.fa-fa-booth-curtain:before {
  content: "\f734";
}
.fa-fa-box-ballot:before {
  content: "\f735";
}
.fa-fa-calendar-star:before {
  content: "\f736";
}
.fa-fa-clipboard-list-check:before {
  content: "\f737";
}
.fa-fa-cloud-drizzle:before {
  content: "\f738";
}
.fa-fa-cloud-hail:before {
  content: "\f739";
}
.fa-fa-cloud-hail-mixed:before {
  content: "\f73a";
}
.fa-fa-cloud-meatball:before {
  content: "\f73b";
}
.fa-fa-cloud-moon-rain:before {
  content: "\f73c";
}
.fa-fa-cloud-rain:before {
  content: "\f73d";
}
.fa-fa-cloud-rainbow:before {
  content: "\f73e";
}
.fa-fa-cloud-showers:before {
  content: "\f73f";
}
.fa-fa-cloud-showers-heavy:before {
  content: "\f740";
}
.fa-fa-cloud-sleet:before {
  content: "\f741";
}
.fa-fa-cloud-snow:before {
  content: "\f742";
}
.fa-fa-cloud-sun-rain:before {
  content: "\f743";
}
.fa-fa-clouds:before {
  content: "\f744";
}
.fa-fa-clouds-moon:before {
  content: "\f745";
}
.fa-fa-clouds-sun:before {
  content: "\f746";
}
.fa-fa-democrat:before {
  content: "\f747";
}
.fa-fa-dewpoint:before {
  content: "\f748";
}
.fa-fa-eclipse:before {
  content: "\f749";
}
.fa-fa-eclipse-alt:before {
  content: "\f74a";
}
.fa-fa-fire-smoke:before {
  content: "\f74b";
}
.fa-fa-flag-alt:before {
  content: "\f74c";
}
.fa-fa-flag-usa:before {
  content: "\f74d";
}
.fa-fa-fog:before {
  content: "\f74e";
}
.fa-fa-house-flood:before {
  content: "\f74f";
}
.fa-fa-humidity:before {
  content: "\f750";
}
.fa-fa-hurricane:before {
  content: "\f751";
}
.fa-fa-landmark-alt:before {
  content: "\f752";
}
.fa-fa-meteor:before {
  content: "\f753";
}
.fa-fa-moon-cloud:before {
  content: "\f754";
}
.fa-fa-moon-stars:before {
  content: "\f755";
}
.fa-fa-podium-star:before {
  content: "\f758";
}
.fa-fa-raindrops:before {
  content: "\f75c";
}
.fa-fa-smog:before {
  content: "\f75f";
}
.fa-fa-thunderstorm:before {
  content: "\f76c";
}
.fa-fa-volcano:before {
  content: "\f770";
}
.fa-fa-water:before {
  content: "\f773";
}
.fa-fa-angel:before {
  content: "\f779";
}
.fa-fa-artstation:before {
  content: "\f77a";
}
.fa-fa-atlassian:before {
  content: "\f77b";
}
.fa-fa-baby:before {
  content: "\f77c";
}
.fa-fa-baby-carriage:before {
  content: "\f77d";
}
.fa-fa-ball-pile:before {
  content: "\f77e";
}
.fa-fa-bells:before {
  content: "\f77f";
}
.fa-fa-biohazard:before {
  content: "\f780";
}
.fa-fa-blog:before {
  content: "\f781";
}
.fa-fa-boot:before {
  content: "\f782";
}
.fa-fa-calendar-day:before {
  content: "\f783";
}
.fa-fa-calendar-week:before {
  content: "\f784";
}
.fa-fa-canadian-maple-leaf:before {
  content: "\f785";
}
.fa-fa-candy-cane:before {
  content: "\f786";
}
.fa-fa-carrot:before {
  content: "\f787";
}
.fa-fa-cash-register:before {
  content: "\f788";
}
.fa-fa-centos:before {
  content: "\f789";
}
.fa-fa-chart-network:before {
  content: "\f78a";
}
.fa-fa-chimney:before {
  content: "\f78b";
}
.fa-fa-compress-arrows-alt:before {
  content: "\f78c";
}
.fa-fa-confluence:before {
  content: "\f78d";
}
.fa-fa-deer:before {
  content: "\f78e";
}
.fa-fa-deer-rudolph:before {
  content: "\f78f";
}
.fa-fa-diaspora:before {
  content: "\f791";
}
.fa-fa-dreidel:before {
  content: "\f792";
}
.fa-fa-dumpster:before {
  content: "\f793";
}
.fa-fa-dumpster-fire:before {
  content: "\f794";
}
.fa-fa-ear-muffs:before {
  content: "\f795";
}
.fa-fa-ethernet:before {
  content: "\f796";
}
.fa-fa-fireplace:before {
  content: "\f79a";
}
.fa-fa-frosty-head:before {
  content: "\f79b";
}
.fa-fa-gifts:before {
  content: "\f79c";
}
.fa-gingerbread-man:before {
  content: "\f79d";
}
.fa-fa-glass-champagne:before {
  content: "\f79e";
}
.fa-fa-glass-cheers:before {
  content: "\f79f";
}
.fa-fa-glass-whiskey:before {
  content: "\f7a0";
}
.fa-fa-glass-whiskey-rocks:before {
  content: "\f7a1";
}
.fa-fa-globe-europe:before {
  content: "\f7a2";
}
.fa-fa-globe-snow:before {
  content: "\f7a3";
}
.fa-fa-grip-lines:before {
  content: "\f7a4";
}
.fa-fa-grip-lines-vertical:before {
  content: "\f7a5";
}
.fa-fa-guitar:before {
  content: "\f7a6";
}
.fa-fa-hat-santa:before {
  content: "\f7a7";
}
.fa-fa-hat-winter:before {
  content: "\f7a8";
}
.fa-fa-heart-broken:before {
  content: "\f7a9";
}
.fa-fa-holly-berry:before {
  content: "\f7aa";
}
.fa-fa-horse-head:before {
  content: "\f7ab";
}
.fa-fa-ice-skate:before {
  content: "\f7ac";
}
.fa-fa-icicles:before {
  content: "\f7ad";
}
.fa-fa-igloo:before {
  content: "\f7ae";
}
.fa-fa-lights-holiday:before {
  content: "\f7b2";
}
.fa-fa-mistletoe:before {
  content: "\f7b4";
}
.fa-fa-mitten:before {
  content: "\f7b5";
}
.fa-fa-mug-hot:before {
  content: "\f7b6";
}
.fa-fa-mug-marshmallows:before {
  content: "\f7b7";
}
.fa-fa-ornament:before {
  content: "\f7b8";
}
.fa-fa-radiation-alt:before {
  content: "\f7ba";
}
.fa-fa-restroom:before {
  content: "\f7bd";
}
.fa-fa-satellite:before {
  content: "\f7bf";
}
.fa-fa-scarf:before {
  content: "\f7c1";
}
.fa-fa-sd-card:before {
  content: "\f7c2";
}
.fa-fa-sim-card:before {
  content: "\f7c4";
}
.fa-fa-sleigh:before {
  content: "\f7cc";
}
.fa-fa-sms:before {
  content: "\f7cd";
}
.fa-fa-snowman:before {
  content: "\f7d0";
}
.fa-fa-toilet:before {
  content: "\f7d8";
}
.fa-fa-tools:before {
  content: "\f7d9";
}
.fa-fa-fire-alt:before {
  content: "\f7e4";
}
.fa-fa-bacon:before {
  content: "\f7e5";
}
.fa-fa-book-medical:before {
  content: "\f7e6";
}
.fa-fa-book-user:before {
  content: "\f7e7";
}
.fa-fa-books-medical:before {
  content: "\f7e8";
}
.fa-fa-brackets:before {
  content: "\f7e9";
}
.fa-fa-brackets-curly:before {
  content: "\f7ea";
}
.fa-fa-bread-loaf:before {
  content: "\f7eb";
}
.fa-fa-bread-slice:before {
  content: "\f7ec";
}
.fa-fa-burrito:before {
  content: "\f7ed";
}
.fa-fa-chart-scatter:before {
  content: "\f7ee";
}
.fa-fa-cheese:before {
  content: "\f7ef";
}
.fa-fa-cheese-swiss:before {
  content: "\f7f0";
}
.fa-fa-cheeseburger:before {
  content: "\f7f1";
}
.fa-fa-clinic-medical:before {
  content: "\f7f2";
}
.fa-fa-clipboard-user:before {
  content: "\f7f3";
}
.fa-fa-comment-alt-medical:before {
  content: "\f7f4";
}
.fa-fa-comment-medical:before {
  content: "\f7f5";
}
.fa-fa-croissant:before {
  content: "\f7f6";
}
.fa-fa-crutch:before {
  content: "\f7f7";
}
.fa-fa-crutches:before {
  content: "\f7f8";
}
.fa-fa-debug:before {
  content: "\f7f9";
}
.fa-fa-disease:before {
  content: "\f7fa";
}
.fa-fa-egg:before {
  content: "\f7fb";
}
.fa-fa-egg-fried:before {
  content: "\f7fc";
}
.fa-fa-files-medical:before {
  content: "\f7fd";
}
.fa-fa-fish-cooked:before {
  content: "\f7fe";
}
.fa-fa-flower:before {
  content: "\f7ff";
}
.fa-fa-flower-daffodil:before {
  content: "\f800";
}
.fa-fa-flower-tulip:before {
  content: "\f801";
}
.fa-fa-folder-tree:before {
  content: "\f802";
}
.fa-fa-french-fries:before {
  content: "\f803";
}
.fa-fa-glass:before {
  content: "\f804";
}
.fa-fa-hamburger:before {
  content: "\f805";
}
.fa-fa-hand-middle-finger:before {
  content: "\f806";
}
.fa-fa-hard-hat:before {
  content: "\f807";
}
.fa-fa-head-side-brain:before {
  content: "\f808";
}
.fa-fa-head-side-medical:before {
  content: "\f809";
}
.fa-fa-home-alt:before {
  content: "\f80a";
}
.fa-fa-home-lg:before {
  content: "\f80b";
}
.fa-fa-home-lg-alt:before {
  content: "\f80c";
}
.fa-fa-hospital-user:before {
  content: "\f80d";
}
.fa-fa-hospitals:before {
  content: "\f80e";
}
.fa-fa-hotdog:before {
  content: "\f80f";
}
.fa-fa-ice-cream:before {
  content: "\f810";
}
.fa-fa-island-tropical:before {
  content: "\f811";
}
.fa-fa-laptop-medical:before {
  content: "\f812";
}
.fa-fa-mailbox:before {
  content: "\f813";
}
.fa-fa-meat:before {
  content: "\f814";
}
.fa-fa-pager:before {
  content: "\f815";
}
.fa-fa-pepper-hot:before {
  content: "\f816";
}
.fa-fa-pizza-slice:before {
  content: "\f818";
}
.fa-fa-popcorn:before {
  content: "\f819";
}
.fa-fa-rings-wedding:before {
  content: "\f81b";
}
.fa-fa-sack-dollar:before {
  content: "\f81d";
}
.fa-fa-salad:before {
  content: "\f81e";
}
.fa-fa-sandwich:before {
  content: "\f81f";
}
.fa-fa-sausage:before {
  content: "\f820";
}
.fa-fa-soup:before {
  content: "\f823";
}
.fa-fa-steak:before {
  content: "\f824";
}
.fa-fa-stretcher:before {
  content: "\f825";
}
.fa-fa-user-headset:before {
  content: "\f82d";
}
.fa-fa-users-medical:before {
  content: "\f830";
}
.fa-fa-walker:before {
  content: "\f831";
}
.fa-fa-webcam:before {
  content: "\f832";
}
.fa-fa-airbnb:before {
  content: "\f834";
}
.fa-fa-battle-net:before {
  content: "\f835";
}
.fa-bootstrap:before {
  content: "\f836";
}
.fa-fa-chromecast:before {
  content: "\f838";
}
.fa-fa-alarm-exclamation:before {
  content: "\f843";
}
.fa-fa-alarm-plus:before {
  content: "\f844";
}
.fa-fa-alarm-snooze:before {
  content: "\f845";
}
.fa-fa-align-slash:before {
  content: "\f846";
}
.fa-fa-bags-shopping:before {
  content: "\f847";
}
.fa-fa-bell-exclamation:before {
  content: "\f848";
}
.fa-fa-bell-plus:before {
  content: "\f849";
}
.fa-fa-biking:before {
  content: "\f84a";
}
.fa-fa-biking-mountain:before {
  content: "\f84b";
}
.fa-fa-border-all:before {
  content: "\f84c";
}
.fa-fa-border-bottom:before {
  content: "\f84d";
}
.fa-fa-border-inner:before {
  content: "\f84e";
}
.fa-fa-border-left:before {
  content: "\f84f";
}
.fa-fa-border-none:before {
  content: "\f850";
}
.fa-fa-border-outer:before {
  content: "\f851";
}
.fa-fa-border-right:before {
  content: "\f852";
}
.fa-fa-border-style:before {
  content: "\f853";
}
.fa-fa-border-style-alt:before {
  content: "\f854";
}
.fa-fa-border-top:before {
  content: "\f855";
}
.fa-fa-bring-forward:before {
  content: "\f856";
}
.fa-fa-bring-front:before {
  content: "\f857";
}
.fa-fa-burger-soda:before {
  content: "\f858";
}
.fa-fa-car-building:before {
  content: "\f859";
}
.fa-fa-car-bus:before {
  content: "\f85a";
}
.fa-fa-cars:before {
  content: "\f85b";
}
.fa-fa-coin:before {
  content: "\f85c";
}
.fa-fa-construction:before {
  content: "\f85d";
}
.fa-fa-digging:before {
  content: "\f85e";
}
.fa-fa-drone:before {
  content: "\f85f";
}
.fa-drone-alt:before {
  content: "\f860";
}
.fa-dryer:before {
  content: "\f861";
}
.fa-dryer-alt:before {
  content: "\f862";
}
.fa-fan:before {
  content: "\f863";
}
.fa-farm:before {
  content: "\f864";
}
.fa-file-search:before {
  content: "\f865";
}
.fa-font-case:before {
  content: "\f866";
}
.fa-game-board:before {
  content: "\f867";
}
.fa-game-board-alt:before {
  content: "\f868";
}
.fa-glass-citrus:before {
  content: "\f869";
}
.fa-h4:before {
  content: "\f86a";
}
.fa-hat-chef:before {
  content: "\f86b";
}
.fa-horizontal-rule:before {
  content: "\f86c";
}
.fa-icons:before {
  content: "\f86d";
}
.fa-kerning:before {
  content: "\f86f";
}
.fa-line-columns:before {
  content: "\f870";
}
.fa-line-height:before {
  content: "\f871";
}
.fa-money-check-edit:before {
  content: "\f872";
}
.fa-money-check-edit-alt:before {
  content: "\f873";
}
.fa-mug:before {
  content: "\f874";
}
.fa-phone-alt:before {
  content: "\f879";
}
.fa-snooze:before {
  content: "\f880";
}
.fa-sort-alt:before {
  content: "\f883";
}
.fa-sort-amount-down-alt:before {
  content: "\f884";
}
.fa-sort-size-down:before {
  content: "\f88c";
}
.fa-sparkles:before {
  content: "\f890";
}
.fa-text:before {
  content: "\f893";
}
.fa-text-size:before {
  content: "\f894";
}
.fa-voicemail:before {
  content: "\f897";
}
.fa-washer:before {
  content: "\f898";
}
.fa-wind-turbine:before {
  content: "\f89b";
}
.fa-border-center-h:before {
  content: "\f89c";
}
.fa-border-center-v:before {
  content: "\f89d";
}
.fa-cotton-bureau:before {
  content: "\f89e";
}
.fa-album:before {
  content: "\f89f";
}
.fa-album-collection:before {
  content: "\f8a0";
}
.fa-amp-guitar:before {
  content: "\f8a1";
}
.fa-badge-sheriff:before {
  content: "\f8a2";
}
.fa-banjo:before {
  content: "\f8a3";
}
.fa-betamax:before {
  content: "\f8a4";
}
.fa-boombox:before {
  content: "\f8a5";
}
.fa-buy-n-large:before {
  content: "\f8a6";
}
.fa-cactus:before {
  content: "\f8a7";
}
.fa-camcorder:before {
  content: "\f8a8";
}
.fa-camera-movie:before {
  content: "\f8a9";
}
.fa-camera-polaroid:before {
  content: "\f8aa";
}
.fa-cassette-tape:before {
  content: "\f8ab";
}
.fa-cctv:before {
  content: "\f8ac";
}
.fa-clarinet:before {
  content: "\f8ad";
}
.fa-cloud-music:before {
  content: "\f8ae";
}
.fa-comment-alt-music:before {
  content: "\f8af";
}
.fa-comment-music:before {
  content: "\f8b0";
}
.fa-computer-classic:before {
  content: "\f8b1";
}
.fa-computer-speaker:before {
  content: "\f8b2";
}
.fa-cowbell:before {
  content: "\f8b3";
}
.fa-cowbell-more:before {
  content: "\f8b4";
}
.fa-disc-drive:before {
  content: "\f8b5";
}
.fa-file-music:before {
  content: "\f8b6";
}
.fa-film-canister:before {
  content: "\f8b7";
}
.fa-flashlight:before {
  content: "\f8b8";
}
.fa-flute:before {
  content: "\f8b9";
}
.fa-flux-capacitor:before {
  content: "\f8ba";
}
.fa-game-console-handheld:before {
  content: "\f8bb";
}
.fa-gamepad-alt:before {
  content: "\f8bc";
}
.fa-gramophone:before {
  content: "\f8bd";
}
.fa-guitar-electric:before {
  content: "\f8be";
}
.fa-guitars:before {
  content: "\f8bf";
}
.fa-hat-cowboy:before {
  content: "\f8c0";
}
.fa-hat-cowboy-side:before {
  content: "\f8c1";
}
.fa-head-side-headphones:before {
  content: "\f8c2";
}
.fa-horse-saddle:before {
  content: "\f8c3";
}
.fa-image-polaroid:before {
  content: "\f8c4";
}
.fa-joystick:before {
  content: "\f8c5";
}
.fa-jug:before {
  content: "\f8c6";
}
.fa-kazoo:before {
  content: "\f8c7";
}
.fa-lasso:before {
  content: "\f8c8";
}
.fa-list-music:before {
  content: "\f8c9";
}
.fa-microphone-stand:before {
  content: "\f8cb";
}
.fa-mouse:before {
  content: "\f8cc";
}
.fa-mouse-alt:before {
  content: "\f8cd";
}
.fa-mp3-player:before {
  content: "\f8ce";
}
.fa-music-slash:before {
  content: "\f8d1";
}
.fa-piano-keyboard:before {
  content: "\f8d5";
}
.fa-projector:before {
  content: "\f8d6";
}
.fa-radio-alt:before {
  content: "\f8d8";
}
.fa-router:before {
  content: "\f8da";
}
.fa-saxophone:before {
  content: "\f8dc";
}
.fa-speakers:before {
  content: "\f8e0";
}
.fa-trumpet:before {
  content: "\f8e3";
}
.fa-usb-drive:before {
  content: "\f8e9";
}
.fa-walkie-talkie:before {
  content: "\f8ef";
}
.fa-waveform:before {
  content: "\f8f1";
}
.fa-scanner-image:before {
  content: "\f8f3";
}
.fa-air-conditioner:before {
  content: "\f8f4";
}
.fa-alien:before {
  content: "\f8f5";
}
.fa-alien-monster:before {
  content: "\f8f6";
}
.fa-bed-alt:before {
  content: "\f8f7";
}
.fa-bed-bunk:before {
  content: "\f8f8";
}
.fa-bed-empty:before {
  content: "\f8f9";
}
.fa-bell-on:before {
  content: "\f8fa";
}
.fa-blinds:before {
  content: "\f8fb";
}
.fa-blinds-raised:before {
  content: "\f8fd";
}
.fa-camera-home:before {
  content: "\f8fe";
}
.fa-caravan:before {
  content: "\f8ff";
}
