import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useTranslation } from 'react-i18next';
import {
  loadAvailableLanguages,
  getAvailableLanguages,
  getCurrentLanguage,
  changeLanguage as changeI18nLanguage,
  setLoginStatus,
  loadSavedLanguageTranslations,
  LanguageType
} from '../multilinguial/i18n';
import { updateLanguage } from '../multilinguial/LanguageService';
import useInfoStore from '../store/UserInfoStore';

interface TranslationContextType {
  // Language data
  availableLanguages: LanguageType[];
  currentLanguage: string;
  currentLanguageInfo: LanguageType | null;

  // Loading states
  isLoading: boolean;
  isInitialized: boolean;

  // Actions
  changeLanguage: (languageCode: string) => Promise<void>;
  refreshLanguages: () => Promise<void>;

  // Translation function
  t: (key: string, options?: any) => string;
}

const TranslationContext = createContext<TranslationContextType | undefined>(undefined);

export const useTranslationContext = () => {
  const context = useContext(TranslationContext);
  if (!context) {
    throw new Error('useTranslationContext must be used within a TranslationProvider');
  }
  return context;
};

interface TranslationProviderProps {
  children: ReactNode;
}

export const TranslationProvider: React.FC<TranslationProviderProps> = ({ children }) => {
  const { t, i18n } = useTranslation();
  const [availableLanguages, setAvailableLanguages] = useState<LanguageType[]>([]);
  const [currentLanguage, setCurrentLanguage] = useState<string>('en');
  const [currentLanguageInfo, setCurrentLanguageInfo] = useState<LanguageType | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  // Get organization ID and user info from store
  const orgId = useInfoStore((state) => state.orgDetails?.OrganizationId || state.user?.OrganizationId);
  const accessToken = useInfoStore((state) => state.accessToken);
  const userInfoObj = useInfoStore((state) => state.user);

  // Update login status in i18n module when user logs in/out
  useEffect(() => {
    const hasValidLogin = !!(orgId && accessToken);
    setLoginStatus(hasValidLogin, orgId);
  }, [orgId, accessToken]);

  // Load available languages when user logs in
  useEffect(() => {
    const initializeLanguages = async () => {
      if (!orgId || !accessToken) {
        setAvailableLanguages([]);
        setIsInitialized(false);
        return;
      }

      try {
        setIsLoading(true);
        console.log('🌐 Loading available languages for organization:', orgId);

        const languages = await loadAvailableLanguages();
        setAvailableLanguages(languages);
        setIsInitialized(true);

        // Load saved language translations if user had a saved language
        await loadSavedLanguageTranslations(orgId);

        // Set initial language from user preference only on first load
        if (userInfoObj?.Language && languages.length > 0) {
          console.log('🔄 Setting initial language from user preference:', userInfoObj.Language);

          // Find the language code that matches the user's language preference
          const userLanguage = languages.find(lang =>
            lang.Language.toLowerCase() === userInfoObj.Language!.toLowerCase()
          );

          if (userLanguage) {
            await changeI18nLanguage(userLanguage.LanguageCode);
            console.log('✅ Initial language set to:', userLanguage.LanguageCode);
          } else {
            console.log('⚠️ User language preference not found in available languages:', userInfoObj.Language);
            // Set English as default if user's language is not found
            await changeI18nLanguage('en');
            console.log('✅ Default language (English) set');
          }
        } else if (userInfoObj && !userInfoObj.Language) {
          // If user exists but has no language preference, set English as default
          console.log('ℹ️ No language preference found for user, setting English as default');
          await changeI18nLanguage('en');
          console.log('✅ Default language (English) set');
        }

        console.log('✅ Languages loaded successfully:', languages.length);
      } catch (error) {
        console.error('❌ Failed to load languages:', error);
        setAvailableLanguages([]);
      } finally {
        setIsLoading(false);
      }
    };

    initializeLanguages();
  }, [orgId, accessToken]);

  // Update current language when i18n language changes
  useEffect(() => {
    const handleLanguageChange = () => {
      const currentLang = getCurrentLanguage();
      setCurrentLanguage(currentLang);

      // Update current language info
      const currentInfo = availableLanguages.find(
        lang => lang.LanguageCode.toLowerCase() === currentLang.toLowerCase()
      );
      setCurrentLanguageInfo(currentInfo || null);
    };

    // Initial setup
    handleLanguageChange();

    // Listen for language changes
    i18n.on('languageChanged', handleLanguageChange);

    return () => {
      i18n.off('languageChanged', handleLanguageChange);
    };
  }, [i18n, availableLanguages]);

  const changeLanguage = async (languageCode: string): Promise<void> => {
    try {
      setIsLoading(true);

      const selectedLanguage = availableLanguages.find(
        lang => lang.LanguageCode.toLowerCase() === languageCode.toLowerCase()
      );

      if (!selectedLanguage) {
        throw new Error(`Language not found: ${languageCode}`);
      }

      console.log(`🔄 Changing language to ${languageCode} (${selectedLanguage.Language})`);

      // Call the updateLanguage API to update user's language preference on the server
      await updateLanguage(selectedLanguage.Language);
      console.log(`✅ User language preference updated on server: ${selectedLanguage.Language}`);

      // Use the simplified change language function
      await changeI18nLanguage(languageCode);

      console.log('✅ Language changed successfully');

    } catch (error) {
      console.error('❌ Failed to change language:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const refreshLanguages = async (): Promise<void> => {
    try {
      setIsLoading(true);
      const languages = await loadAvailableLanguages();
      setAvailableLanguages(languages);
    } catch (error) {
      console.error('Failed to refresh languages:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const contextValue: TranslationContextType = {
    availableLanguages,
    currentLanguage,
    currentLanguageInfo,
    isLoading,
    isInitialized,
    changeLanguage,
    refreshLanguages,
    t,
  };

  return (
    <TranslationContext.Provider value={contextValue}>
      {children}
    </TranslationContext.Provider>
  );
};

export default TranslationProvider;
