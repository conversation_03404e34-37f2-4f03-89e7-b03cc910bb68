import React, { useEffect, useMemo, useState } from 'react';
import ChecklistCircle from "./ChecklistCheckIcon";
import useDrawerStore from '../../store/drawerStore';
import LauncherSettings from './LauncherSettings';
import ImageCarousel from "./ImageCarousel";
import VideoPlayer from "./VideoPlayer";
import { editInteractionName } from './Chekpoints';
import { GetGudeDetailsByGuideId } from '../../services/GuideListServices';
import { closeicon, closepluginicon, maximize, chkicn1, chkdefault } from '../../assets/icons/icons';
import AlertPopup from '../drawer/AlertPopup';
import '../../styles/rtl_styles.scss';
import { useTranslation } from 'react-i18next';



interface CheckListPopupProps {
  isOpen: any;
  onClose: () => void;
  onRemainingCountUpdate: (formattedCount: string) => void;
  data: any;
  guideDetails: any;
  setopenWarning: any;
  handleLeave: () => void;

}
// Function to modify the color of an SVG icon
const modifySVGColor = (base64SVG: any, color: any) => {
	if (!base64SVG) {
		return "";
	}

	try {
		// Check if the string is a valid base64 SVG
		if (!base64SVG.includes("data:image/svg+xml;base64,")) {
			return base64SVG; // Return the original if it's not an SVG
		}

		const decodedSVG = atob(base64SVG.split(",")[1]);

		// Check if this is primarily a stroke-based or fill-based icon
		const hasStroke = decodedSVG.includes('stroke="');
		const hasColoredFill = /fill="(?!none)[^"]+"/g.test(decodedSVG);

		let modifiedSVG = decodedSVG;

		if (hasStroke && !hasColoredFill) {
			// This is a stroke-based icon (like chkicn2-6) - only change stroke color
			modifiedSVG = modifiedSVG.replace(/stroke="[^"]+"/g, `stroke="${color}"`);
		} else if (hasColoredFill) {
			// This is a fill-based icon (like chkicn1) - only change fill color
			modifiedSVG = modifiedSVG.replace(/fill="(?!none)[^"]+"/g, `fill="${color}"`);
		} else {
			// No existing fill or stroke, add fill to make it visible
			modifiedSVG = modifiedSVG.replace(/<path(?![^>]*fill=)/g, `<path fill="${color}"`);
			modifiedSVG = modifiedSVG.replace(/<svg(?![^>]*fill=)/g, `<svg fill="${color}"`);
		}

		const modifiedBase64 = `data:image/svg+xml;base64,${btoa(modifiedSVG)}`;
		return modifiedBase64;
	} catch (error) {
		console.error("Error modifying SVG color:", error);
		return base64SVG; // Return the original if there's an error
	}
};

const ChecklistPopup: React.FC<CheckListPopupProps> = ({
	isOpen,
	onClose,
	onRemainingCountUpdate,
	data,
	guideDetails,
	setopenWarning,
	handleLeave,
}) => {
	const { t: translate } = useTranslation();
	const { checklistGuideMetaData, checkpointsEditPopup, isUnSavedChanges, openWarning } = useDrawerStore(
		(state: any) => state
	);

	const initialCompletedStatus: { [key: string]: boolean } = {};

	// State to track which steps are completed
	const [completedStatus, setCompletedStatus] = useState<{ [key: string]: boolean }>({});

	// Map checkpoints and set the first one as completed by default
	const checkpointslistData =
		checklistGuideMetaData[0]?.checkpoints?.checkpointsList?.map((checkpoint: any, index: number) => ({
			...checkpoint,
			completed: index === 0 ? true : false,
		})) || [];

	const [checklistItems, setChecklistItems] = useState(checkpointslistData);
	const [activeItem, setActiveItem] = useState(
		checkpointslistData.length > 0 && !checkpointsEditPopup ? checkpointslistData[0]?.id : "default-placeholder"
	);
	const checklistColor = checklistGuideMetaData[0]?.canvas?.primaryColor;

function getLocalizedTitleSubTitle(ts: any, t: any) {
	  const defaultTitle = "Checklist Title";
	  const defaultSubTitle = "Context about the tasks in the checklist below users should prioritize completing.";
	  return {
	    title:
	      !ts?.title || ts.title === defaultTitle
	        ? t(defaultTitle, { defaultValue: defaultTitle })
	        : ts.title,
	    subTitle:
	      !ts?.subTitle || ts.subTitle === defaultSubTitle
	        ? t(defaultSubTitle, { defaultValue: defaultSubTitle })
	        : ts.subTitle,
	  };
	}

	const localizedTitleSubTitle = getLocalizedTitleSubTitle(checklistGuideMetaData[0]?.TitleSubTitle, translate); {/*this is here what i did is i am getting the title and subtitle from the checklistGuideMetaData and then i am using it for modifying inital translation of the title and subtitle so if you want to modify data such as color and all go to store and look for checklistGuideMetaData */ }

	// Initialize completedStatus when checkpointslistData changes
	useEffect(() => {
		if (checkpointslistData.length > 0) {
			const initialCompletedStatus: { [key: string]: boolean } = {};

			checkpointslistData.forEach((item: any, index: number) => {
				// Set the first item as completed by default, others as not completed
				initialCompletedStatus[item.id] = index === 0;
			});

			// console.log("Initializing completedStatus:", initialCompletedStatus);
			setCompletedStatus(initialCompletedStatus);

			// Calculate and update the initial remaining count
			const remainingItems = checkpointslistData.length - 1; // All items except the first one
			const formattedCount = remainingItems.toString().padStart(2, "0");
			// console.log("Initial remaining count set to:", formattedCount);

			// Update localStorage directly
			if (window.localStorage) {
				window.localStorage.setItem("remainingCount", formattedCount);
			}

			// Call the callback
			if (onRemainingCountUpdate) {
				onRemainingCountUpdate(formattedCount);
			}
		}
	}, [checkpointslistData, onRemainingCountUpdate,completedStatus]);
	useEffect(() => {
		document.documentElement.style.setProperty("--chkcolor", checklistColor);
	}, [checklistColor]);

	useEffect(() => {
		if (checkpointslistData.length === 0) {
			setActiveItem("default-placeholder");
		} else {
			setActiveItem(checkpointslistData[0]?.id);
		}
	}, [checkpointslistData.length == 1]);

	const [icons, setIcons] = useState<any[]>([
		{
			id: 1,
			component: (
				<span
					dangerouslySetInnerHTML={{ __html: chkicn1 }}
					style={{ zoom: 1, display: "flex" }}
				/>
			),
			selected: true,
		},
	]);
	const encodeToBase64 = (svgString: string) => {
		return `data:image/svg+xml;base64,${btoa(svgString)}`;
	};
	let base64Icon: any;

	const initialSelectedIcon = icons.find((icon) => icon.selected);
	if (initialSelectedIcon && checkpointslistData.length == 0) {
		const svgElement = initialSelectedIcon.component.props.dangerouslySetInnerHTML?.__html;

		if (svgElement) {
			base64Icon = encodeToBase64(svgElement);
		}
	}
	const iconColor = checklistGuideMetaData[0]?.launcher?.iconColor || "#fff"; // Default to black if no color
	const base64IconFinal = checklistGuideMetaData[0]?.launcher?.icon || base64Icon;

	const totalItems = checkpointslistData.length || 1;
	const progress = 1;

	// Update the remaining count whenever completedStatus or checkpointslistData changes
	useEffect(() => {
		if (checkpointslistData.length > 0 && Object.keys(completedStatus).length > 0) {
			// Count the number of incomplete steps
			const remainingItems =
				checkpointslistData.length - Object.values(completedStatus).filter((status) => status).length;
			// Format with leading zero (01, 02, 03, etc.)
			const formattedCount = remainingItems.toString().padStart(2, "0");
			// console.log("ChecklistPopup updating count to:", formattedCount, "from completedStatus:", completedStatus);

			// Make sure the callback is called with the updated count
			if (onRemainingCountUpdate) {
				onRemainingCountUpdate(formattedCount);
			}

			// Also update the count in the drawerStore to ensure it's available to all components
			if (window.localStorage) {
				window.localStorage.setItem("remainingCount", formattedCount);
			}
		}
	}, [completedStatus, checkpointslistData, onRemainingCountUpdate]);

	const toggleItemCompletion = (id: string) => {
		setCompletedStatus((prevStatus) => {
			const newStatus = {
				...prevStatus,
				[id]: !prevStatus[id],
			};

			// Update the remaining count immediately
			if (checkpointslistData.length > 0) {
				const remainingItems = checkpointslistData.length - Object.values(newStatus).filter((status) => status).length;
				const formattedCount = remainingItems.toString().padStart(2, "0");
				console.log("toggleItemCompletion updating count to:", formattedCount);

				// Update localStorage directly
				if (window.localStorage) {
					window.localStorage.setItem("remainingCount", formattedCount);
				}

				// Call the callback
				if (onRemainingCountUpdate) {
					onRemainingCountUpdate(formattedCount);
				}
			}

			return newStatus;
		});
	};

	// Mark the active item as completed
	const handleMarkAsCompleted = () => {
		if (activeItem && activeItem !== "default-placeholder") {
			setCompletedStatus((prevStatus) => {
				const newStatus = {
					...prevStatus,
					[activeItem]: true,
				};

				// Update the remaining count immediately
				if (checkpointslistData.length > 0) {
					const remainingItems =
						checkpointslistData.length - Object.values(newStatus).filter((status) => status).length;
					const formattedCount = remainingItems.toString().padStart(2, "0");
					console.log("handleMarkAsCompleted updating count to:", formattedCount);

					// Update localStorage directly
					if (window.localStorage) {
						window.localStorage.setItem("remainingCount", formattedCount);
					}

					// Call the callback
					if (onRemainingCountUpdate) {
						onRemainingCountUpdate(formattedCount);
					}
				}

				console.log("Marked item as completed:", activeItem);
				return newStatus;
			});
		}
	};

	const handleSelect = (id: any) => {
		setActiveItem(id);
	};

	const handleClose = () => {
		onClose();
		setActiveItem(checkpointslistData[0]?.id);
	};

	if (!isOpen) return null;

	if (!isOpen) return null;
	const selectedItem =
		checkpointslistData.length > 0
			? checkpointslistData.find((item: any) =>
					checkpointsEditPopup ? item.id === editInteractionName : item.id === activeItem
			  ) || {
					id: "default-placeholder",
				title: translate("Step Title", { defaultValue: "Step Title" }),
				description: translate("Step Description", { defaultValue: "Step Description" }),
					icon: base64Icon,
				mediaTitle: translate("Media Title", { defaultValue: "Media Title" }),
				mediaDescription: translate("Media Description", { defaultValue: "Media Description" }),
					supportingMedia: [],
			  }
			: {
					id: "default-placeholder",
				title: translate("Step Title", { defaultValue: "Step Title" }),
				description: translate("Step Description", { defaultValue: "Step Description" }),
					icon: base64Icon,
				mediaTitle: translate("Media Title", { defaultValue: "Media Title" }),
				mediaDescription: translate("Media Description", { defaultValue: "Media Description" }),
					supportingMedia: [],
			  };

	const handleNavigate = () => {
		// window.open("http://localhost:3000/", '_blank');
	};
	const isRTL = 
  document.documentElement.getAttribute('dir') === 'rtl' ||
  document.body.getAttribute('dir') === 'rtl';
	
	return (
		<>
			{isUnSavedChanges && openWarning && (
				<AlertPopup
					openWarning={openWarning}
					setopenWarning={setopenWarning}
					handleLeave={handleLeave}
				/>
			)}
			<div
				style={{
					position: "fixed",
					inset: 0,
					display: "flex",
					alignItems: "center",
					// justifyContent: 'center',
					zIndex: 99999,				}}
			>
				<div
					style={{
						position: "absolute",
						inset: 0,
						backgroundColor: "rgba(0, 0, 0, 0.3)",
					}}
					onClick={handleClose}
				></div>

				<div
					
					className='qadpt-chkpopup'
				>
					<div
						style={{
							backgroundColor: checklistGuideMetaData[0]?.canvas?.backgroundColor,
							border: `${checklistGuideMetaData[0]?.canvas?.borderWidth}px solid ${checklistGuideMetaData[0]?.canvas?.borderColor}`,
							borderRadius: `${checklistGuideMetaData[0]?.canvas?.cornerRadius}px`,
							width: `${checklistGuideMetaData[0]?.canvas?.width || 930}px`,
							height: `${checklistGuideMetaData[0]?.canvas?.height || 500}px`,
						}}
					>
						<div
							style={{
								display: "flex",
								height: "100%",
								width: "100%",
								overflow: "auto hidden",
							}}
							className="qadpt-chkcontent"
						>
							{/* Left side - Checklist items */}
							<div
								
								className="qadpt-chkrgt"
							>
								<div
									style={{
										display: "flex",
										flexDirection: "column",
										gap: "16px",
										borderBottom: "1px solid #E8E8E8",
										padding: "24px 24px 16px 24px",
										textAlign : isRTL ? 'right' : 'left',

									}}
								>
									<div
										style={{
											display: "flex",
											flexDirection: "column",
											gap: "6px",
										}}
									>
										<div
											style={{
												fontSize: "20px",
												fontWeight: checklistGuideMetaData[0]?.TitleSubTitle?.titleBold ? "bold" : "normal",
												fontStyle: checklistGuideMetaData[0]?.TitleSubTitle?.titleItalic ? "italic" : "normal",
												color: checklistGuideMetaData[0]?.TitleSubTitle?.titleColor || "#333",
												display: "block",
												textOverflow: "ellipsis",
												whiteSpace: "nowrap",
												wordBreak: "break-word",
												overflow: "hidden",
											}}
										>
											{localizedTitleSubTitle.title}
										</div>

										<div
											style={{
												fontSize: "14px",
												fontWeight: checklistGuideMetaData[0]?.TitleSubTitle?.subTitleBold ? "bold" : "normal",
												fontStyle: checklistGuideMetaData[0]?.TitleSubTitle?.subTitleItalic ? "italic" : "normal",
												color: checklistGuideMetaData[0]?.TitleSubTitle?.subTitleColor || "#8D8D8D",
											}}
											className="qadpt-subtl"
										>
											{localizedTitleSubTitle.subTitle}
										</div>
									</div>

									<div>
										<div
											style={{
												display: "flex",
												alignItems: "center",
												justifyContent: "space-between",
												marginBottom: "8px",
											}}
										>
											<span style={{ fontSize: "14px", color: "#6b7280" }}>
												{progress}/{totalItems}
											</span>
										</div>
										<div
											style={{
												height: "8px",
												backgroundColor: "#e5e7eb",
												borderRadius: "9999px",
												overflow: "hidden",
											}}
										>
											<div
												style={{
													height: "100%",
													backgroundColor: checklistGuideMetaData[0]?.canvas?.primaryColor,
													borderRadius: "9999px",
													width: `${(progress / totalItems) * 100}%`,
												}}
											></div>
										</div>
									</div>
								</div>

								<div style={{ overflow: "auto", maxHeight:  `calc(${checklistGuideMetaData[0]?.canvas?.height || 500}px - 190px)`}}>
									{checkpointslistData.length === 0 ? (
										<div className={`${activeItem === "default-placeholder" ? "qadpt-chkstp" : ""}`}>
											<div
												
												className='qadpt-chkstpctn'
											>
												{/* Title Section */}
												<div
													style={{
														display: "flex",
														alignItems: "center",
														justifyContent: "space-between",
														width: "100%",
													}}
												>
													<div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
														{/* Default Icon */}
														<img
															src={base64Icon}
															alt="icon"
															style={{ width: "20px", height: "20px" }}
														/>
														<span style={{ color: checklistGuideMetaData[0]?.checkpoints?.checkpointTitles }}>
															{translate("Step Title", { defaultValue: "Step Title" })}
														</span>
													</div>
													<div>
														<ChecklistCircle
															key="default"
															completed={true}
															onClick={() => {}}
															size="sm"
														/>
													</div>
												</div>
												<div style={{ display: "flex", alignItems: "center", marginTop: "8px", gap: "8px" }}>
													<p
														style={{
															fontSize: "14px",
															color: checklistGuideMetaData[0]?.checkpoints?.checkpointsDescription,
															lineHeight: "1.5",
															margin: 0,
															whiteSpace: "normal",
															wordBreak: "break-word",
														}}
													>
														{translate("Step Description", { defaultValue: "Step Description" })}
													</p>
												</div>
											</div>
										</div>
									) : (
										checkpointslistData?.map((item: any) => (
											<div
												className={`${
													(checkpointsEditPopup ? editInteractionName === item.id : activeItem === item.id)
														? "qadpt-chkstp"
														: ""
												}`}
											>
												<div
													key={item.id}
													className='qadpt-chkstpctn'
													onClick={() => handleSelect(item.id)}
												>
													{/* Title Section */}
													<div style={{ paddingLeft: "10px", display: "flex", gap: "6px", flexDirection: "column" }}>
														<div
															style={{
																display: "flex",
																alignItems: "center",
																justifyContent: "space-between",
																width: "100%",
															}}
														>
															<div
																style={{
																	display: "flex",
																	alignItems: "center",
																	gap: "10px",
																	flexDirection: "row",
																	width: "calc(100% - 60px)",
																}}
															>
																{item.icon && typeof item.icon === "string" ? (
																	<img
																		src={modifySVGColor(item.icon, checklistGuideMetaData[0]?.checkpoints?.checkpointsIcons || "#333")}
																		alt="icon"
																		style={{ width: "20px", height: "20px" }}
																	/>
																) : (
																	<div
																		style={{
																			width: "20px",
																			height: "20px",
																			display: "flex",
																			alignItems: "center",
																			justifyContent: "center",
																		}}
																	>
																		<span style={{ width: "16px", height: "16px" }}></span>
																	</div>
																)}

																<span
																	style={{
																		color: checklistGuideMetaData[0]?.checkpoints?.checkpointTitles || "#333",
																		overflow: "hidden",
																		textOverflow: "ellipsis",
																		whiteSpace: "nowrap",
																		wordBreak: "break-word",
																	}}
																>
																	{item.title}
																</span>
														
															</div>
															<div>
																<ChecklistCircle
																	key={item.id}
																	completed={completedStatus[item.id] || false}
																	onClick={() => toggleItemCompletion(item.id)}
																	size="sm"
																/>
															</div>
														</div>
														<div>
															<p
																style={{
																	fontSize: "14px",
																	color: checklistGuideMetaData[0]?.checkpoints?.checkpointsDescription,
																}}
																className="qadpt-chkpopdesc"
															>
																{item.description}
															</p>
														</div>
													</div>
												</div>
											</div>
										))
									)}
								</div>
							</div>

							{/* Right side - Selected item details - only show when an item is selected */}
							<div
								style={{
									width: "60%",
									padding: "20px 20px 0 20px",
								}}
								className="qadpt-chklft"
							>
								<div
										style={{
											display: "flex",
											alignItems: "center",
											placeContent: "end",
											width: "100%",
											gap: "6px",
										}}
									>
										<span
											dangerouslySetInnerHTML={{ __html: maximize }}
											style={{
												background: "#e8e8e8",
												borderRadius: "50%",
												padding: "6px",
												display: "flex",
												cursor: "pointer",
											}}
										/>
										<span
											dangerouslySetInnerHTML={{ __html: closepluginicon }}
											style={{
												background: "#e8e8e8",
												borderRadius: "50%",
												padding: "8px",
												display: "flex",
												cursor: "pointer",
											}}
										/>
									</div>
								<div
									style={{
										display: "flex",
										alignItems: "center",
										flexDirection: "column",
										gap: "10px",
										    height: "calc(100% - 90px)",
									}}
								>
									

									<div style={{
    									overflow: "hidden auto",display: "flex",
										alignItems: "center",
										flexDirection: "column",width:"-webkit-fill-available"}} >

									{selectedItem?.supportingMedia?.length > 0 && (
										<>
											{selectedItem.supportingMedia.some((file: any) => file?.Base64?.startsWith("data:image")) && (
												<ImageCarousel
													selectedItem={selectedItem}
													activeItem={activeItem}
													images={selectedItem.supportingMedia
														.filter((file: any) => file?.Base64?.startsWith("data:image"))
														.map((file: any) => file.Base64)}
													isMaximized={""}
												/>
											)}

											{selectedItem.supportingMedia.some((file: any) => file?.Base64?.startsWith("data:video")) &&
												selectedItem.supportingMedia
													.filter((file: any) => file?.Base64?.startsWith("data:video"))
													.map((file: any, index: number) => (
														<VideoPlayer
															key={index}
															videoFile={file.Base64}
															isMaximized={""}
														/>
													))}
										</>
									)}
									{(selectedItem?.supportingMedia?.length === 0 || !selectedItem?.supportingMedia) && (
										<div style={{ width: "auto", height: "244px" }}>
											<span dangerouslySetInnerHTML={{ __html: chkdefault }} />
											<div style={{ color: "#8D8D8D" }}>{translate('Check tasks, stay organized, and finish strong!', { defaultValue: 'Check tasks, stay organized, and finish strong!' })}</div>
										</div>
									)}

									<div
										style={{ width: "100%", marginTop: "10px" }}
										className="qadpt-chkdesc"
									>
										{selectedItem && (
											<div style={{ height: "100%", display: "flex", flexDirection: "column" }}>
												<div
													style={{
														textAlign: isRTL ? "right": "left",
														display: "flex",
														flexDirection: "column",
														gap: "12px",
													}}
												>
													<div
														style={{
															fontSize: "16px",
															fontWeight: 600,
															color: "#333",
															overflow: "hidden",
															textOverflow: "ellipsis",
															whiteSpace: "nowrap",
															wordBreak: "break-word",
														}}
													>
														{selectedItem.mediaTitle}
													</div>

													<div
														className="qadpt-desc"
														style={{ color: "#8D8D8D" }}
													>
														{selectedItem.mediaDescription}
													</div>
												</div>
											</div>
										)}
										</div>
										</div>
								</div>
								<div
									style={{
										display: "flex",
										gap: "12px",
										alignItems: "center",
										placeContent: "end",
										paddingBottom: "20px",
									}}
									className="qadpt-btnsec"
								>
									<button
										style={{
											backgroundColor: checklistGuideMetaData[0]?.canvas?.primaryColor,
											borderRadius: "10px",
											padding: "9px 16px",
											color: "#fff",
											border: "none",
											cursor: "pointer",
										}}
									>
										{translate('Take Tour', { defaultValue: 'Take Tour' })}
									</button>
									{selectedItem?.supportingMedia?.length > 0 && (
										<button
											style={{
												borderRadius: "10px",
												padding: "9px 16px",
												color: checklistGuideMetaData[0]?.canvas?.primaryColor,
												border: "none",
												background: "#D3D9DA",
												cursor: "pointer",
											}}
											onClick={handleMarkAsCompleted}
										>
											{translate('Mark as Completed', { defaultValue: 'Mark as Completed' })}
										</button>
									)}
								</div>
							</div>
							{/* )} */}
						</div>
					</div>
				</div>
			</div>
		</>
	);
};

const ChecklistApp = ({ setopenWarning, handleLeave }: { setopenWarning: any; handleLeave: any }) => {
	const { checklistGuideMetaData, setShowLauncherSettings, showLauncherSettings, setOpenWarning } = useDrawerStore(
		(state: any) => state
	);
	let base64Icon: any;
	const [icons, setIcons] = useState<any[]>([
		{
			id: 1,
			component: (
				<span
					dangerouslySetInnerHTML={{ __html: chkicn1 }}
					style={{ zoom: 1, display: "flex" }}
				/>
			),
			selected: true,
		},
	]);
	const encodeToBase64 = (svgString: string) => {
		return `data:image/svg+xml;base64,${btoa(svgString)}`;
	};
	const iconColor = checklistGuideMetaData[0]?.launcher?.iconColor || "#fff"; // Default to black if no color
	const base64IconFinal = checklistGuideMetaData[0]?.launcher?.icon;

	const initialSelectedIcon = icons.find((icon) => icon.selected);
	if (initialSelectedIcon) {
		const svgElement = initialSelectedIcon.component.props.dangerouslySetInnerHTML?.__html;

		if (svgElement) {
			base64Icon = encodeToBase64(svgElement);
		}
	}
	// Using the modifySVGColor function defined at the top of the file
	const modifiedIcon = modifySVGColor(base64IconFinal || base64Icon, iconColor);
	const [isOpen, setIsOpen] = useState(true);
	const [remainingCount, setRemainingCount] = useState("00");

	const handleRemainingCountUpdate = (formattedCount: string) => {
		setRemainingCount(formattedCount);
	};

	const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(document.body);
	const [currentStep, setCurrentStep] = useState(0);
	const [isAnnouncementOpen, setAnnouncementOpen] = useState(false);
	const [scrollPercentage, setScrollPercentage] = useState(0);
	const currentUrl = window.location.href;

	return (
		<div>
			{/* Your main app content here */}

			{/* Floating action button */}
			<div
  className='qadpt-chklayout'
>
				<button
					onClick={() => {
						setIsOpen(true);
						setShowLauncherSettings(true);
					}}
					style={{
						backgroundColor: checklistGuideMetaData[0]?.launcher.launcherColor,
						color: "white",
						borderRadius:
							checklistGuideMetaData[0]?.launcher.type === "Text" ||
							checklistGuideMetaData[0]?.launcher.type === "Icon+Txt"
								? "16px"
								: "50%",
						height: "54px",
						width:
							checklistGuideMetaData[0]?.launcher.type === "Text" ||
							checklistGuideMetaData[0]?.launcher.type === "Icon+Txt"
								? `auto`
								: "54px",
						display: "flex",
						alignItems: "center",
						justifyContent: "center",
						padding: "8px",
						boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
						transition: "all 0.2s ease",
						border: "none",
						cursor: "pointer",
						position: "relative",
					}}
				>
					{checklistGuideMetaData[0]?.launcher?.type === "Icon" && (
						<img
							src={modifiedIcon}
							alt="icon"
							style={{ width: "20px", height: "20px" }}
						/>
					)}

					{/* Text Only */}
					{checklistGuideMetaData[0]?.launcher?.type === "Text" && checklistGuideMetaData[0]?.launcher?.text && (
						<span
							style={{
								fontSize: "16px",
								fontWeight: "bold",
								color: checklistGuideMetaData[0]?.launcher?.textColor,
								padding: "8px",
								whiteSpace: "nowrap",
							}}
						>
							{checklistGuideMetaData[0].launcher.text}
						</span>
					)}

					{/* Icon + Text */}
					{checklistGuideMetaData[0]?.launcher?.type === "Icon+Txt" &&
						checklistGuideMetaData[0]?.launcher?.text &&
						checklistGuideMetaData[0]?.launcher?.icon && (
							<span
								style={{
									display: "flex",
									alignItems: "center",
									gap: "8px",
									color: checklistGuideMetaData[0]?.launcher?.textColor,
									fontSize: "16px",
									fontWeight: "bold",
									padding: "8px",
								}}
							>
								<img
									src={modifiedIcon}
									alt="icon"
									style={{ width: "20px", height: "20px" }}
								/>
								{checklistGuideMetaData[0]?.launcher?.text}
							</span>
						)}

					{/* Notification Badge */}
					{checklistGuideMetaData[0]?.launcher?.notificationBadge && (
						<div
							style={{
								position: "absolute",
								top: "-8px",
								right: "-8px",
								backgroundColor: checklistGuideMetaData[0]?.launcher?.notificationBadgeColor,
								color: checklistGuideMetaData[0]?.launcher?.notificationTextColor,
								fontSize: "12px",
								borderRadius: "9999px",
								height: "24px",
								width: "24px",
								display: "flex",
								alignItems: "center",
								justifyContent: "center",
							}}
						>
							{remainingCount}
						</div>
					)}
				</button>
			</div>

			{showLauncherSettings && (
				<>
					<LauncherSettings />
				</>
			)}

			{/* Popup component */}
			<ChecklistPopup
				data={""}
				guideDetails={""}
				isOpen={isOpen}
				onClose={() => setIsOpen(true)}
				onRemainingCountUpdate={handleRemainingCountUpdate}
				setopenWarning={setOpenWarning}
				handleLeave={handleLeave}
			/>
		</div>
	);
};

export default ChecklistApp;