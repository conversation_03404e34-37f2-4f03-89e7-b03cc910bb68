// /* Side menu */
// .side-menu {
//     width: 280px;
//     padding: 12px;
//     margin-left: -16px;
// }

// .menu-header h3 {
//     margin: 0;
//     font-size: 20px;
// }

// .menu-list {
//     list-style-type: none;
//     padding: 0;
// }

// .menu-item {
//     padding: 10px 0;
//     cursor: pointer;
//     display: flex;
//     align-items: center;
//     display: flex;
//     justify-content: center;
//     border-radius: var(--button-border-radius);
//     border: 1px solid rgb(212, 206, 206);
//     gap: 10px;
//     margin: 10px;
//     width: 281px;
//     margin-left: 0px;
//     background-color: var(--border-color);
//     /* width: 264px;
// height: 91px;
// top: 48px;
// left: 18px; */
//     /* gap: 0px; */
//     border-radius: var(--button-border-radius);
//     /* border: 1px 0px 0px 0px; */
//     opacity: 0px;

// }

// .menu-text {
//     display: flex;
//     flex-direction: column;
//     margin-left: 10px;
// }

// .menu-title-icon {
//     display: flex;
//     align-items: center;
//     /* Aligns the text and icon vertically */
//     gap: 8px;
//     /* Adds some space between the name and the icon */
// }

// .menu-title {
//     font-weight: bold;
//     font-size: 16px;
//     display: flex
// }

// .icon {
//     margin-left: 70px;
//     margin-top: -26px;
// }

// .menu-description {
//     font-size: 12px;
//     color: #888;
//     margin-top: 4px;
//     /* Adds space between title-icon and description */
// }

// .arrow-icon {
//     font-size: 18px;
//     margin-left: auto;
//     font-weight: 400;
//     font-style: bold;
// }

// .menu-item.active {
//     background: rgba(202, 202, 202, 1);

// }

// .menu-item i {
//     margin-right: 10px;
// }

// .modal-overlay {
//     position: fixed;
//     top: 0;
//     left: 0;
//     right: 0;
//     bottom: 0;
//     background: rgba(0, 0, 0, 0.5);
//     display: flex;
//     justify-content: center;
//     align-items: center;
// }

// .modal-content {
//     background: white;
//     padding: 32px;
//     border-radius: 5px;
//     width: 801px;
// }

// .modal-header {
//     display: flex;
//     justify-content: space-between;
//     align-items: center;
// }

// .modal-body {
//     margin-top: 20px;
// }

// .search-input {
//     width: 100%;
//     padding: 10px;
//     margin-bottom: 20px;
// }

// .list-container ul {
//     list-style-type: none;
//     padding: 0;
// }

// .list-container li {
//     padding: 10px 0;
//     border-bottom: 1px solid #ccc;
// }

.close-button {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
}

.qadpt-webgird {
  height: calc(100vh - 220px);

  .MuiDataGrid-row {
      background-color: var(--white-color);
      border-radius: var(--button-border-radius);
  }

  .MuiDataGrid-columnHeaders {
      display: none;
  }

  .MuiDataGrid-cell {
      border-bottom: none;
  }

  /* .MuiTablePagination-root{
  // 	width: 100%;
     // 	display: flex;
     // 	align-items: center;
  //   }
  //   .MuiTablePagination-displayedRows{
  // 	display: flex;
  // 	place-content: center;
  // 	width: 197%;
  //   }
  //   .MuiTablePagination-input{
  // 	border: 1px solid #ccc;
  // 	border-radius: 10px;
  // 	.MuiInputBase-input{
  // 		padding-right: 24px !important;
  // 	} */
  .MuiSelect-select.MuiTablePagination-select {
      padding-right: 24px !important;
  }
}

.MuiDataGrid-cell button {
  border: 1px solid #ccc;
  border-radius: 4px;
  background: #F1F2F8;
  height: 30px;
  margin-right: 5px;
}
.qadpt-webclonepopup{
.MuiPaper-root.MuiDialog-paper{
  padding: 20px;
      border-radius: 10px;
   width: 30%;
}
.qadpt-title{
  font-size: 21px;
  font-weight: 600;
  padding: 0 !important;
}
.qadpt-close{
  position: absolute !important;
  top: 15px;
  right: 20px;
  color: #ccc;
  svg{
    font-size: 18px;
  }
}
.qadpt-subtitle{
  margin-top: 20px;
  font-size: 14px;
}
.MuiDialogContent-root{
  padding: 0 !important;
}
.MuiDialogActions-root .MuiButton-root{
  /* background-color: var(--button-bg-color); */
  border-radius: 10px;
}
}



.side-menu {
  .menu-list {
    list-style: none;
    padding: 0;
    margin: 0;

    .menu-item {
      padding: 10px;
  margin: 10px 0;
  background-color: #ccc;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: background-color 0.3s ease;
  cursor: pointer;

      // &:hover {
      //   background-color: #f0f4fa;
      // }

      // &.active {
      //   background-color: #d3d8e3;
      // }

      .menu-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
      }

      .menu-text {
        display: flex;
        flex-direction: column;
        margin-right: 16px;

        .menu-title {
          font-size: 14px;
          font-weight: 600;
          margin-bottom: 4px;
          text-align: left;
        }

        .menu-description {
          font-size: 12px;
          color: #9c9c9c;
          text-align: left;
        }
      }

      .icons {
        display: flex;
        align-items: center;
        gap: 20px;
      }
    }
  }

  // .popup-list {
  //   position: absolute;
  //   top: 0;
  //   right: -320px;
  //   width: 280px;
  //   background-color: #ffffff;
  //   border: 1px solid #e0e0e0;
  //   border-radius: 12px;
  //   padding: 16px;
  //   box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.1);
  //   transition: transform 0.3s ease;

  //   &.open {
  //     transform: translateX(-100%);
  //   }
  // }
}


