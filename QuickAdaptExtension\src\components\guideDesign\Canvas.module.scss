// .qadpt-canvasbuttonsbox{
// display: flex;
// 					alignItems: center;
// 					backgroundColor: #EAE2E2;
// 					borderRadius: 25px;
// 					height: 40px;
// }
// .qadpt-canvasbuttontext{
//     position: relative;
//     top: 35px;
//     right: 20px;
// }

// .qadpt-designpopup {
// 	width: 200px;
// 	border-radius: 8px;
// 	padding: 16px;
// 	background-color: rgba(246, 238, 238, 1);
// 	position: fixed;
// 	z-index: 9999;
// 	display: flex;
// 	flex-direction: column;
// 	transition: height 0.3s ease;
// 	top: 60px;
// 	left: 8px;
// 	// &.qadpt-designpopup-right{
// 	// 	right:160px;
// 	// 	left:auto !important;
// 	// 	top:145px;
// 	// }
// 	// &.qadpt-designpopup-right-htmlelement{
// 	// 	right:160px;
// 	// 	left:auto !important;
// 	// 	top:145px;
// 	// 	width:320px;
// 	// }
  
// 	.qadpt-content {
// 	//   margin-top: 20px;
  
// 	  .qadpt-design-header {
// 		display: flex;
// 		justify-content: space-between;
// 		align-items: center;
// 		margin-bottom: 8px;
// 		.qadpt-title {
// 		  font-weight: 600;
// 		  color: var(--primarycolor);
// 		  font-size: 16px;
// 		  text-align: center;
// 		}
// 		svg {
// 		  font-size: 16px;
// 		}
// 	  }
  
// 	  .qadpt-design-btn {
// 		width: 100%;
// 		justify-content: flex-start;
// 		background-color: #eae2e2;
// 		color: #495e58;
// 		text-transform: none;
// 		margin-bottom: 8px;
// 		border-radius: 12px;
// 		padding: 8px;
  
// 		&:hover {
// 		  background-color: #d8d4d2;
// 		}
// 		svg {
// 		  color: var(--primarycolor);
// 		  border-radius: 50px;
// 		  background: rgba(95, 158, 160, 0.2);
// 		}
// 	  }
  
// 	  .qadpt-status-container {
// 		.qadpt-label {
// 		  margin-bottom: 8px;
// 		  font-weight: 500;
// 		  text-align: left;
// 		  margin-left: 5px;
// 		  font-size: 14px;
// 		}
  
// 		.qadpt-toggle-group {
// 		  margin-bottom: 16px;
// 		  button {
// 			text-transform: capitalize;
// 			height: 35px;
// 			width: 100px;
// 			font-size: 12px;
// 		  }
// 		  .MuiToggleButton-root {
// 			&.Mui-selected {
// 			  border: 1px solid var(--primarycolor);
// 			}
// 		  }
// 		}
// 	  }
// 	  .qadpt-customfield {
// 		background-color: #eae2e2;
// 		border-radius: 8px;
// 		height: calc(100vh - 210px);
// 		textarea {
// 		  height: calc(100vh - 210px) !important;
// 		}
// 		.MuiOutlinedInput-root {
// 		  height: 100%;
// 		  width: 110%;
  
	// 	  &:hover fieldset {
	// 		border-color: #495e58; // Border color on hover
	// 	  }
	// 	}
	//   }
	// }
	// .qadpt-position-grid {
	// 	padding: 5px;
	// 	background-color: var(--back-light-color);
	// 	border-radius: var(--button-border-radius);
	// 	margin-bottom: 5px;
	// 	.MuiGrid-root{
	// 		background: var(--ext-background);
	// 		width: 100%;
	// 		margin: 0;
		
// 		  &:hover fieldset {
// 			border-color: #495e58; // Border color on hover
// 		  }
// 		}
// 	  }
// 	}
// 	.qadpt-position-grid {
// 		padding: 5px;
// 		background-color: var(--back-light-color);
// 		border-radius: 15px;
// 		margin-bottom: 5px;
// 		.MuiGrid-root{
// 			background: var(--ext-background);
// 			width: 100%;
// 			margin: 0;
	
// 		}
// 		.qadpt-ctrl-title {
// 			margin-bottom: 8px !important;
// 			text-align: left;
// 			font-size: 14px !important;
// 		}
// 	}
	
	// .qadpt-controls {
	// 	.qadpt-control-box {
	// 		display: flex;
	// 		align-items: center;
	// 		background-color: var(--back-light-color);
	// 		border-radius: var(--button-border-radius);
	// 		height: 40px;
	// 		padding: 0 5px;
	// 		margin-bottom: 5px;
	
// 			.qadpt-control-label {
// 				font-size: 14px;
// 				margin-right: auto;
// 				display: flex;
// 				align-items: center;
// 			}
	
// 			.qadpt-control-input {
// 				width: 63px;
// 				margin-left: auto;
	
// 				.MuiOutlinedInput-root {
// 					border-radius: 12px;
// 					height: 30px;
// 					font-size: 14px;
// 				}
// 			}
// 		}
// 		.qadpt-color-input {
// 			width: 20px;
// 			height: 20px;
// 			border: none;
		
// 			&::-webkit-color-swatch-wrapper {
// 				padding: 0;
// 				border-radius: 50%; // Ensures circular inside swatch
// 			}
		
// 			&::-webkit-color-swatch {
// 				border-radius: 50%;  // Ensures the color swatch itself is circular
// 				border: none;        // Removes the default border inside the swatch
// 			}
// 		}
// 	}
	
//   }
