/* .qadpt-designpopup {
	width: 200px;
	border-radius: 8px;
	padding: 16px;
	background-color: rgba(246, 238, 238, 1);
	position: fixed;
	z-index: 9999;
	display: flex;
	flex-direction: column;
	transition: height 0.3s ease;
	top: 60px;
	left: 8px;
}

.qadpt-designpopup .qadpt-content .qadpt-design-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 8px;
}
.qadpt-designpopup .qadpt-content .qadpt-design-header .qadpt-title {
	font-weight: 600;
	color: var(--primarycolor);
	font-size: 16px;
	text-align: center;
}
.qadpt-designpopup .qadpt-content .qadpt-design-header svg {
	font-size: 16px;
}
.qadpt-designpopup .qadpt-content .qadpt-design-btn {
	width: 100%;
	justify-content: flex-start;
	background-color: #eae2e2;
	color: #495e58;
	text-transform: none;
	margin-bottom: 8px;
	border-radius: 12px;
	padding: 8px;
}
.qadpt-designpopup .qadpt-content .qadpt-design-btn:hover {
	background-color: #d8d4d2;
}
.qadpt-designpopup .qadpt-content .qadpt-design-btn svg {
	color: var(--primarycolor);
	border-radius: 50px;
	background: rgba(95, 158, 160, 0.2);
}
.qadpt-designpopup .qadpt-content .qadpt-status-container .qadpt-label {
	margin-bottom: 8px;
	font-weight: 500;
	text-align: left;
	margin-left: 5px;
	font-size: 14px;
}
.qadpt-designpopup .qadpt-content .qadpt-status-container .qadpt-toggle-group {
	margin-bottom: 16px;
}
.qadpt-designpopup .qadpt-content .qadpt-status-container .qadpt-toggle-group button {
	text-transform: capitalize;
	height: 35px;
	width: 100px;
	font-size: 12px;
}
.qadpt-designpopup .qadpt-content .qadpt-status-container .qadpt-toggle-group .MuiToggleButton-root.Mui-selected {
	border: 1px solid var(--primarycolor);
}
.qadpt-designpopup .qadpt-content .qadpt-customfield {
	background-color: #eae2e2;
	border-radius: 8px;
	height: calc(100vh - 210px);
}
.qadpt-designpopup .qadpt-content .qadpt-customfield textarea {
	height: calc(100vh - 210px) !important;
}
.qadpt-designpopup .qadpt-content .qadpt-customfield .MuiOutlinedInput-root {
	height: 100%;
	width: 110%;
}
.qadpt-designpopup .qadpt-content .qadpt-customfield .MuiOutlinedInput-root fieldset {
	border: none;
}
.qadpt-designpopup .qadpt-content .qadpt-customfield .MuiOutlinedInput-root:hover fieldset {
	border-color: #495e58;
}
.qadpt-designpopup .qadpt-position-grid {
	padding: 5px;
	background-color: var(--back-light-color);
	border-radius: var(--button-border-radius);
	margin-bottom: 5px;
}
.qadpt-designpopup .qadpt-position-grid .MuiGrid-root {
	background: var(--ext-background);
	width: 100%;
	margin: 0;
}
.qadpt-designpopup .qadpt-position-grid .qadpt-ctrl-title {
	margin-bottom: 8px !important;
	text-align: left;
	font-size: 14px !important;
}
.qadpt-designpopup .qadpt-controls .qadpt-control-box {
	display: flex;
	align-items: center;
	background-color: var(--back-light-color);
	border-radius: var(--button-border-radius);
	height: 40px;
	padding: 0 5px;
	margin-bottom: 5px;
}
.qadpt-designpopup .qadpt-controls .qadpt-control-box .qadpt-control-label {
	font-size: 14px;
	margin-right: auto;
	display: flex;
	align-items: center;
}
.qadpt-designpopup .qadpt-controls .qadpt-control-box .qadpt-control-input {
	width: 63px;
	margin-left: auto;
}
.qadpt-designpopup .qadpt-controls .qadpt-control-box .qadpt-control-input .MuiOutlinedInput-root {
	border-radius: 12px;
	height: 30px;
	font-size: 14px;
}
.qadpt-designpopup .qadpt-controls .qadpt-color-input {
	width: 20px;
	height: 20px;
	border: none;
}
.qadpt-designpopup .qadpt-controls .qadpt-color-input::-webkit-color-swatch-wrapper {
	padding: 0;
	border-radius: 50%;
}
.qadpt-designpopup .qadpt-controls .qadpt-color-input::-webkit-color-swatch {
	border-radius: 50%;
	border: none;
} */
