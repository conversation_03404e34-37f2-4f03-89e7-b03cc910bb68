import React from "react";
import { Box, TextField } from "@mui/material";
import { useTranslation } from 'react-i18next';

type TTextFieldProps = import("@mui/material").TextFieldProps & {};
const CustomTextField = (props: TTextFieldProps) => {
	const { t: translate } = useTranslation();
	const { placeholder, onChange, value, InputProps, ...rest } = props;
	return (
		<TextField
			{...rest}
			placeholder={placeholder ? translate(placeholder) : undefined}
			size="small"
			fullWidth
			value={value}
			onChange={onChange}
			InputProps={{
				...InputProps,
				className: "qadpt-input-field",
				disableUnderline: true,
			}}
			variant="standard"
		/>
	);
};

export default CustomTextField;
