/**
 * Speech Recognition Service
 *
 * This service provides functionality for speech-to-text conversion using the Web Speech API.
 * It includes a pause detection feature that stops listening after a specified pause duration.
 */

// Define the interface for the SpeechRecognition object
interface IWindow extends Window {
  webkitSpeechRecognition: any;
  SpeechRecognition: any;
  msSpeechRecognition: any; // Microsoft Edge prefix
}

// Get the SpeechRecognition constructor
const getRecognitionConstructor = (): any => {
  const windowWithSpeech = window as unknown as IWindow;
  return windowWithSpeech.SpeechRecognition ||
         windowWithSpeech.webkitSpeechRecognition ||
         windowWithSpeech.msSpeechRecognition;
};

// Check if speech recognition is supported
export const isSpeechRecognitionSupported = (): boolean => {
  // First check if the constructor exists
  const constructor = getRecognitionConstructor();
  if (!constructor) {
    return false;
  }

  // For Edge browser, do additional checks
  if (isEdgeBrowser()) {
    // Edge Chromium should support it, but older Edge might not
    const edgeVersion = getEdgeVersion();
    if (edgeVersion && edgeVersion < 79) {
      // Older Edge (pre-Chromium) might have issues
      console.warn("Older Edge version detected, speech recognition might not work properly");
    }
  }

  return true;
};

// Get Edge browser version
const getEdgeVersion = (): number | null => {
  const match = navigator.userAgent.match(/Edg\/(\d+)/);
  return match ? parseInt(match[1], 10) : null;
};

// Speech recognition instance
let recognition: any = null;

// Check if browser is Microsoft Edge
const isEdgeBrowser = (): boolean => {
  return navigator.userAgent.indexOf("Edg") !== -1;
};

// Initialize speech recognition
const initializeSpeechRecognition = (): any => {
  if (!recognition) {
    const SpeechRecognition = getRecognitionConstructor();
    if (SpeechRecognition) {
      recognition = new SpeechRecognition();
      recognition.continuous = true; // Enable continuous recognition
      recognition.interimResults = true; // Get interim results
      recognition.lang = 'en-US'; // Set language to English

      // Special handling for Edge browser
      if (isEdgeBrowser()) {
        console.log("Edge browser detected, applying special configuration");
        // Some Edge versions need these settings
        recognition.maxAlternatives = 1;
      }
    }
  }
  return recognition;
};

// Interface for speech recognition callbacks
interface SpeechRecognitionCallbacks {
  onResult?: (text: string, isFinal: boolean) => void;
  onEnd?: () => void;
  onError?: (error: any) => void;
  onStart?: () => void;
  pauseDuration?: number; // Duration of pause in milliseconds before stopping
}

// Variable to track the pause timer
let pauseTimer: NodeJS.Timeout | null = null;

// Variable to store the accumulated transcript
let accumulatedTranscript = '';

// Start speech recognition
export const startSpeechRecognition = (callbacks: SpeechRecognitionCallbacks): void => {
  try {
    const recognitionInstance = initializeSpeechRecognition();

    if (!recognitionInstance) {
      if (callbacks.onError) {
        callbacks.onError('Speech recognition not supported');
      }
      return;
    }

    // Reset accumulated transcript at the start of a new recognition session
    accumulatedTranscript = '';

    // Default pause duration is 5 seconds if not specified
    const pauseDuration = callbacks.pauseDuration || 5000;

    // Set up event handlers
    recognitionInstance.onresult = (event: any) => {
      // Clear any existing pause timer
      if (pauseTimer) {
        clearTimeout(pauseTimer);
        pauseTimer = null;
      }

      try {
        // Build a complete transcript from all results
        let fullTranscript = '';

        // Edge might have a different event structure
        if (isEdgeBrowser() && event.interpretation) {
          // Edge-specific handling
          fullTranscript = event.interpretation;
        } else {
          // Standard handling for Chrome and others
          for (let i = 0; i < event.results.length; i++) {
            fullTranscript += event.results[i][0].transcript + ' ';
          }
        }

        // Clean up the transcript (remove extra spaces)
        fullTranscript = fullTranscript.trim().replace(/\s+/g, ' ');

        // Update the accumulated transcript
        accumulatedTranscript = fullTranscript;

        // Get the latest result for isFinal status
        let isFinal = false;

        if (isEdgeBrowser() && event.interpretation) {
          // Edge-specific handling for isFinal
          isFinal = event.emma && event.emma.interpretation &&
                   event.emma.interpretation.attributes &&
                   event.emma.interpretation.attributes.confidence > 0.8;
        } else {
          // Standard handling for Chrome and others
          const results = event.results;
          const latestResult = results[results.length - 1];
          isFinal = latestResult.isFinal;
        }

        if (callbacks.onResult) {
          callbacks.onResult(accumulatedTranscript, isFinal);
        }
      } catch (error) {
        console.error("Error processing speech recognition result:", error);
        // Fallback to just using the raw event data if available
        if (event.results && event.results[0] && event.results[0][0] && event.results[0][0].transcript) {
          const fallbackText = event.results[0][0].transcript;
          accumulatedTranscript = fallbackText;
          if (callbacks.onResult) {
            callbacks.onResult(fallbackText, true);
          }
        }
      }

      // Set a timer to stop recognition after the specified pause duration
      pauseTimer = setTimeout(() => {
        stopSpeechRecognition();
        if (callbacks.onEnd) {
          callbacks.onEnd();
        }
      }, pauseDuration);
    };

    recognitionInstance.onend = () => {
      if (callbacks.onEnd) {
        callbacks.onEnd();
      }
    };

    recognitionInstance.onerror = (event: any) => {
      // Special handling for Edge browser errors
      if (isEdgeBrowser()) {
        console.log("Edge browser error:", event);

        // In Edge, sometimes we need to restart recognition after an error
        if (event.error === 'network' || event.error === 'aborted') {
          // These errors can sometimes be recovered from by restarting
          setTimeout(() => {
            try {
              // Try to restart recognition
              recognitionInstance.start();
              return; // Skip the error callback if we're trying to recover
            } catch (e) {
              console.error("Failed to restart recognition after error:", e);
              // Continue to error callback
            }
          }, 1000);
        }
      }

      if (callbacks.onError) {
        callbacks.onError(event.error || "Unknown speech recognition error");
      }
    };

    recognitionInstance.onstart = () => {
      if (callbacks.onStart) {
        callbacks.onStart();
      }
    };

    // Start recognition
    recognitionInstance.start();
  } catch (error) {
    if (callbacks.onError) {
      callbacks.onError(error);
    }
  }
};

// Stop speech recognition
export const stopSpeechRecognition = (): void => {
  if (recognition) {
    try {
      // Clear any existing pause timer
      if (pauseTimer) {
        clearTimeout(pauseTimer);
        pauseTimer = null;
      }

      // Reset the accumulated transcript
      accumulatedTranscript = '';

      recognition.stop();
    } catch (error) {
      console.error('Error stopping speech recognition:', error);
    }
  }
};
