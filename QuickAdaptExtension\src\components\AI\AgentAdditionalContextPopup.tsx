import React, { useState } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';
import TextareaAutosize from '@mui/material/TextareaAutosize';
import useDrawerStore, { DrawerState } from '../../store/drawerStore';
import { cancelTraining } from '../../services/ScrapingService';

interface AgentAdditionalContextPopupProps {
  open: boolean;
  onClose: () => void;
  onSaved: () => void;
  onCancel?: () => void;
}

const AgentAdditionalContextPopup: React.FC<AgentAdditionalContextPopupProps> = ({ open, onClose, onSaved, onCancel }) => {
  const {
    agentAdditionalContext,
    setAgentAdditionalContext,
    setIsAgentTraining
  } = useDrawerStore((state: DrawerState) => state);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSave = async () => {
    setSaving(true);
    setError(null);
    try {
      // Process the save - the actual agent training is handled in the parent component
      onSaved();
    } catch (e) {
      setError('Failed to save agent.');
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = async () => {
    setSaving(true);
    setError(null);
    try {
      // Cancel the training process (stops scraping and clears data)
      await cancelTraining();

      // Update the training state
      setIsAgentTraining(false);

      // Call the onCancel callback if provided, otherwise use onClose
      if (onCancel) {
        onCancel();
      } else {
        onClose();
      }
    } catch (e) {
      setError('Failed to cancel training.');
      console.error('Error canceling training:', e);
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className='AgentAdditionalContextPopup' id='AgentAdditionalContextPopup'>
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>Agent Additional Context</DialogTitle>
      <DialogContent>
        <TextareaAutosize
        id="textareaadditionalcontextpopup"
          minRows={5}
          style={{ width: '100%', marginTop: 16 }}
          placeholder="Enter additional context for the agent..."
          value={agentAdditionalContext}
          onChange={e => setAgentAdditionalContext(e.target.value)}
        />
        {error && <div style={{ color: 'red', marginTop: 8 }}>{error}</div>}
      </DialogContent>
      <DialogActions>
        <Button id="textareaadditionalcontextpopup" onClick={handleCancel} disabled={saving}>Cancel</Button>
        <Button id="textareaadditionalcontextpopup" onClick={handleSave} disabled={saving} variant="contained" color="primary">Save</Button>
      </DialogActions>
    </Dialog>
    </div>
  );
};

export default AgentAdditionalContextPopup; 