import React, { useContext,useState } from 'react';
import { stopScraping } from '../../services/ScrapingService';
import './EnableAIButton.css';
import { AccountContext } from '../../components/login/AccountContext';
import {
    Dialog,
	DialogContent,
	InputAdornment,
	DialogContentText,
    Grid,
    Box,
    Button,
    Container,
    TextField,
    DialogTitle,
    DialogActions,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    IconButton,
    Tooltip,
    Alert,
    Chip,
    TextareaAutosize
} from '@mui/material';
import useDrawerStore, { DrawerState } from '../../store/drawerStore';
const AgentTrainingFields = ({ setShowtrainingAgentFields, showtrainingAgentFields, handleEnableAgentTraining }: { setShowtrainingAgentFields: any; showtrainingAgentFields: any; handleEnableAgentTraining:any}) => {

    const {
        agentName,
        agentDescription,
        agentUrl,
        setAgentName,
        setAgentDescription,
        setAgentUrl
    } = useDrawerStore((state: DrawerState) => state);

    const handleClick = () =>
    {
        setShowtrainingAgentFields(false);
        handleEnableAgentTraining();

        }
    return (
      <>

<div className="qadpt-modal-overlay">
<div className="qadpt-accountcreatepopup qadpt-agent-training-popup" style={{marginTop:"-106px",height:"489px"}}>
				  <div className="qadpt-title-sec">
					<div className="qadpt-title">Create Agent</div>

				  </div>
				  <div className="qadpt-accountcreatefield">
					<Grid container spacing={2}>
					  <Grid item xs={12}>
						<FormControl fullWidth required style={{marginBottom:"20px"}}>
						  <label htmlFor="account-name" style={{textAlign:"left"}}>Agent Name</label>
						  <TextField
                          style={{marginTop:"10px"}}
							id="account-name"
							name="AccountName"
							required
							value={agentName}
							onChange={(e) => {
                  setAgentName(e.target.value)
              }
            }
					        variant="outlined"

							inputProps={{ maxLength: 50 }}
							className="qadpt-acctfield"
						  />
						</FormControl>
						<FormControl fullWidth required style={{marginBottom:"20px"}}>
  <label htmlFor="agent-description" style={{textAlign:"left"}}>Description</label>
  <textarea
  style={{marginTop:"10px", minHeight:"60px", resize:"vertical"}}
    id="agent-description"
    name="DomainUrl"
    required
    value={agentDescription}
    onChange={(e) => setAgentDescription(e.target.value)}
    maxLength={200}
    className="qadpt-acctfield"
  />
</FormControl>

<FormControl fullWidth required style={{marginBottom:"20px"}}>
  <label htmlFor="agent-url" style={{textAlign:"left"}}>URL</label>
  <TextField
  style={{marginTop:"10px"}}
    id="agent-url"
    name="AgentUrl"
    required
    value={agentUrl}
    onChange={(e) => setAgentUrl(e.target.value)}
    slotProps={{ htmlInput: { maxLength: 500 } }}
    className="qadpt-acctfield"
    placeholder="Enter the URL for the agent..."
  />
</FormControl>

					  </Grid>
					</Grid>
				  </div>
				  <div style={{marginTop:"-19px"}}>
     <Button
						onClick={handleClick}
						sx={{
							backgroundColor: "#04417F",
							color: "#FFF",
							borderRadius: "8px",
							textTransform: "capitalize",
							fontSize: "16px",
							fontWeight: "400",
							minWidth: "120px",
							"&:hover": {
								backgroundColor: "#0776E5",
								transform: "translateY(-1px)",
								boxShadow: "0 4px 12px rgba(4, 65, 127, 0.3)",
							},
							transition: "all 0.3s ease",
						}}
					>
						Start Training
					</Button>
    </div>
				</div>
                </div>
		


            </>
  );
};

export default AgentTrainingFields;