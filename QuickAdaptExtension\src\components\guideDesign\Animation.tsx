import React, { useState } from "react";
import {
	Box,
	Typography,
	IconButton,
	MenuItem,
	Select,
	FormControl,
	InputLabel,
	SelectChangeEvent,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import ArrowBackIosNewOutlinedIcon from "@mui/icons-material/ArrowBackIosNewOutlined";
import "./Canvas.module.css";

const AnimationSettings = ({ selectedTemplate }: { selectedTemplate: any }) => {
	const [entryAnimation, setEntryAnimation] = useState("None");
	const [delightAnimation, setDelightAnimation] = useState("None");
	const [isOpen, setIsOpen] = useState(true);

	const handleEntryAnimationChange = (event: SelectChangeEvent<string>) => {
		setEntryAnimation(event.target.value as string);
	};

	const handleDelightAnimationChange = (event: SelectChangeEvent<string>) => {
		setDelightAnimation(event.target.value as string);
	};

	const handleClose = () => {
		setIsOpen(false); // Close the popup when close button is clicked
	};

	if (!isOpen) return null;

	return (
		<div className="qadpt-designpopup">
			<div className="qadpt-content">
				<div className="qadpt-design-header">
					<IconButton
						aria-label="close"
						onClick={handleClose}
					>
						<ArrowBackIosNewOutlinedIcon  className="qadpt-design-back"/>
					</IconButton>
					<div className="qadpt-title">Animation</div>
					<IconButton
						size="small"
						aria-label="close"
						onClick={handleClose}
					>
						<CloseIcon className="qadpt-design-close"/>
					</IconButton>
				</div>
{/* Animation Settings Section */}
<div
    style={{
        display: "flex",
        flexDirection: "column",
        gap: "15px",
        padding: "10px",
    }}
>
    {/* Entry Animation Dropdown */}
    <FormControl fullWidth>
        <div style={{ marginBottom: "5px", fontSize: "14px" }}>
            Entry Animation
        </div>
        <Select
    labelId="entry-animation-label"
    value={entryAnimation}
    onChange={handleEntryAnimationChange}
    MenuProps={{
		PopoverClasses: {
            root: "custom-popover-root"
        }
    }}
    style={{
        border: "none",
        borderRadius: "8px",
        fontSize: "14px",
        boxShadow: "none",
        backgroundColor: "var(--back-light-color)",
    }}
>
    <MenuItem value="None">None</MenuItem>
    <MenuItem value="Top to Bottom">Top to Bottom</MenuItem>
    <MenuItem value="Fade">Fade</MenuItem>
</Select>

    </FormControl>

    {/* Delight Animation Dropdown */}
    <FormControl fullWidth>
        <div style={{ marginBottom: "5px", fontSize: "14px" }}>
            Delight Animation
        </div>
        <Select
    labelId="delight-animation-label"
    value={delightAnimation}
    onChange={handleDelightAnimationChange}
    MenuProps={{
		PopoverClasses: {
            root: "custom-popover-root"
        }
    }}
    style={{
        borderRadius: "8px",
        fontSize: "14px",
        boxShadow: "none",
        backgroundColor: "var(--back-light-color)",
    }}
>
    <MenuItem value="None">None</MenuItem>
    <MenuItem value="Confetti">Confetti</MenuItem>
</Select>

    </FormControl>
</div>


			</div>
		</div>
	);
};

export default AnimationSettings;
