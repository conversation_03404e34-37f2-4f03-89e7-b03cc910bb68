import { adminApiService, userApiService } from './APIService';
import { ScrapedElement } from './ScrapingService';
export interface Agent {
  Name: string | undefined;
  Description: string | undefined;
  AccountId:string;
  url: string;
  TrainingFields: ScrapedElement[];
  AdditionalContext?: string;
}


export const NewAgentTraining = async (agent: Agent) => {
    try {
        const response = await userApiService.post(`Assistant/NewAgentTraining`, agent);
        return response.data;
    } catch (error) {
        console.error("Error uploading agent", error);
        return { message: "Upload failed" };
    }
};


export const CreateInteraction = async (userCommand: string, accountId: string,targetUrl:string) => {
    
    try {
        const requestBody = {
			userCommand,
			accountId,
			targetUrl,
		};
        const response = await adminApiService.post(`Ai/CreateInteraction`, requestBody)
        return response.data;
    } catch (error) {
        console.error("Error in creating integration", error);
        return [];
    }
}