@import url('https://fonts.googleapis.com/css2?family=Syncopate:wght@700&display=swap');
:root {
--font-family: <PERSON>pins, Proxima Nova, arial, serif;
    --ext-background: #F6EEEE;
    --border-color: #ccc;
    --white-color: #fff;
    --back-light-color:#EAE2E2;
    --font-size : 14px;
    --button-padding : 4px 8px;
    --button-lineheight : normal;
    --error-color:#d05353;
   --Theme-primaryColor: #FFFFFF;
    --Theme-secondaryColor: #FCF3F0;
    --Theme-accentColor: #5F9EA0;
    --Theme-primaryTextColor: #00000;
    --Theme-primaryIconColor:#2614ad;
    --Theme-accentTextColor:#c01b9c;
    --ThemefontFamily: Poppins;
    --Theme-shape: 4px;

    /* Dynamic color variants - these will be set by JavaScript */
    /* Primary Color Variants */
    --primaryColor-10: rgba(255, 255, 255, 0.1);
    --primaryColor-20: rgba(255, 255, 255, 0.2);
    --primaryColor-30: rgba(255, 255, 255, 0.3);
    --primaryColor-40: rgba(255, 255, 255, 0.4);
    --primaryColor-50: rgba(255, 255, 255, 0.5);

    /* Secondary Color Variants */
    --secondaryColor-10: rgba(252, 243, 240, 0.1);
    --secondaryColor-20: rgba(252, 243, 240, 0.2);
    --secondaryColor-30: rgba(252, 243, 240, 0.3);
    --secondaryColor-40: rgba(252, 243, 240, 0.4);
    --secondaryColor-50: rgba(252, 243, 240, 0.5);

    /* Accent Color Variants */
    --accentColor-10: rgba(95, 158, 160, 0.1);
    --accentColor-20: rgba(95, 158, 160, 0.2);
    --accentColor-25: rgba(95, 158, 160, 0.25);
    --accentColor-30: rgba(95, 158, 160, 0.3);
    --accentColor-40: rgba(95, 158, 160, 0.4);
    --accentColor-50: rgba(95, 158, 160, 0.5);

    /* White-mixed color variants - lighter tints */
    /* Primary Color White Variants - #FFFFFF mixed with white */
    --primaryColor-white-10: rgb(255, 255, 255); /* #FFFFFF + 10% white = still white */
    --primaryColor-white-20: rgb(255, 255, 255); /* #FFFFFF + 20% white = still white */
    --primaryColor-white-30: rgb(255, 255, 255); /* #FFFFFF + 30% white = still white */
    --primaryColor-white-40: rgb(255, 255, 255); /* #FFFFFF + 40% white = still white */
    --primaryColor-white-50: rgb(255, 255, 255); /* #FFFFFF + 50% white = still white */

    /* Secondary Color White Variants - #FCF3F0 mixed with white */
    --secondaryColor-white-10: rgb(252, 246, 242); /* #FCF3F0 + 10% white */
    --secondaryColor-white-20: rgb(253, 249, 246); /* #FCF3F0 + 20% white */
    --secondaryColor-white-30: rgb(253, 251, 248); /* #FCF3F0 + 30% white */
    --secondaryColor-white-40: rgb(254, 252, 250); /* #FCF3F0 + 40% white */
    --secondaryColor-white-50: rgb(254, 254, 252); /* #FCF3F0 + 50% white */

    /* Accent Color White Variants - #5F9EA0 mixed with white */
    --accentColor-white-10: rgb(111, 174, 176); /* #5F9EA0 + 10% white */
    --accentColor-white-20: rgb(127, 190, 192); /* #5F9EA0 + 20% white */
    --accentColor-white-30: rgb(143, 206, 208); /* #5F9EA0 + 30% white */
    --accentColor-white-40: rgb(159, 222, 224); /* #5F9EA0 + 40% white */
    --accentColor-white-50: rgb(175, 238, 240); /* #5F9EA0 + 50% white */
    --accentColor-white-60: rgb(191, 254, 255); /* #5F9EA0 + 60% white */
    --accentColor-white-90: rgb(239, 254, 255); /* #5F9EA0 + 90% white */

    /* Primary Text Color White Variants - #00000 mixed with white */
    --primaryTextColor-white-50: rgb(128, 128, 128); /* #000000 + 50% white */
}
*:not(.qadpt-rte *):not(.qadpt-preview *):not(.fal, .far, .fad, .fas, .fab, i):not(.qadpt-jodit *):not(.mat-icon) {
	font-family: var(--font-family) !important;
}

body {
	margin: 0;
	font-family: var(--font-family) !important;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.theme-custom {
  .leftDrawer {
    .qadpt-drawerHeader{
      background-color: var(--Theme-primaryColor);
    }
    .qadpt-drawerContent{
      background-color: var(--primaryColor-white-10);
    }
  }
}





