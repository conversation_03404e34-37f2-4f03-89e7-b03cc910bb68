import React, { useState } from "react";
import { Box, Button, Typography, TextField, IconButton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
// import Draggable from "react-draggable";
import ArrowBackIosNewOutlinedIcon from "@mui/icons-material/ArrowBackIosNewOutlined";
import './Canvas.module.css';
const CustomCSS = () => {
	const [cssCode, setCssCode] = useState(""); // State to store the CSS code
	const [isOpen, setIsOpen] = useState(true);

	const handleClose = () => {
		setIsOpen(false); // Close the popup when close button is clicked
	};
	const handleCodeChange = (event: any) => {
		setCssCode(event.target.value); // Update the state with the input value
	};

	const handleSave = () => {
		// Handle the save functionality, e.g., sending the cssCode to an API or saving locally
		
	};
	if (!isOpen) return null;

	return (
		//<Draggable>
		<div className="qadpt-designpopup">
			<div className="qadpt-content">
			<div className="qadpt-design-header">
			<IconButton
				aria-label="close"
				onClick={handleClose}>
						<ArrowBackIosNewOutlinedIcon />
						{/* Header */}
					</IconButton>
					<div
				className="qadpt-title">
				Custom CSS
			</div>
			{/* Close Button */}
			<IconButton size="small" aria-label="close" onClick={handleClose}>
      <CloseIcon />
    </IconButton>

		</div>

					<TextField
						//label="Paste or Write Custom CSS"
						variant="outlined"
						fullWidth
						multiline
						minRows={10} // Minimum rows to display, making it a large text area
						maxRows={20} // Max rows to avoid excessive stretching
						value={cssCode} // Bind the value to the state
					onChange={handleCodeChange} // Handle changes in the text area
					className="qadpt-customfield"
						// sx={{
						// 	backgroundColor: "#EAE2E2",
						// 	borderRadius: "8px",
						// 	marginBottom: "16px",
						// 	height: "340px",
						// 	"& .MuiOutlinedInput-root": {
						// 		height: "100%",
						// 		"& fieldset": {
						// 			borderColor: "#67a1a3", // Custom border color
						// 		},
						// 		"&:hover fieldset": {
						// 			borderColor: "#495e58",
						// 		},
						// 	},
						// }}
					/>

					<Button
						variant="contained"
						fullWidth
					onClick={handleSave} // Call handleSave when the button is clicked
					className="qadpt-btn"
						// sx={{
						// 	backgroundColor: "#67a1a3",
						// 	color: "#fff",
						// 	borderRadius: "8px",
						// 	textTransform: "none",
						// 	"&:hover": {
						// 		backgroundColor: "#568a8a", // Darker color on hover
						// 	},
						// 	position: "relative",
						// 	top: "20px",
						// }}
					>
						Save
					</Button>
			</div>
			</div>
		//</Draggable>
	);
};

export default CustomCSS;
